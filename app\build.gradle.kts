import java.util.Properties

plugins {
	alias(libs.plugins.android.application)
	alias(libs.plugins.kotlin.android)
	alias(libs.plugins.kotlin.serialization)
	id("kotlin-parcelize")
	alias(libs.plugins.hilt.android)
	id("org.jetbrains.kotlin.kapt")
}

// Load secrets from local.properties (do not commit real keys)
val localProps = Properties().apply {
	val file = rootProject.file("local.properties")
	if (file.exists()) {
		file.inputStream().use { this.load(it) }
	}
}
val SUPABASE_URL_LOCAL = localProps.getProperty("SUPABASE_URL") ?: ""
val SUPABASE_ANON_KEY_LOCAL = localProps.getProperty("SUPABASE_ANON_KEY") ?: ""

android {
	namespace = "com.example.ma"
	compileSdk = 34

	defaultConfig {
		applicationId = "com.example.ma"
		minSdk = 24
		targetSdk = 34
		versionCode = 1
		versionName = "1.0.0"

		testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

		// Supabase Configuration
		buildConfigField("String", "SUPABASE_URL", "\"${SUPABASE_URL_LOCAL}\"")
		buildConfigField("String", "SUPABASE_ANON_KEY", "\"${SUPABASE_ANON_KEY_LOCAL}\"")
		buildConfigField("String", "APP_VERSION_NAME", "\"${versionName}\"")
		buildConfigField("int", "APP_VERSION_CODE", "${versionCode}")
		buildConfigField("boolean", "DEBUG_MODE", "true")
		buildConfigField("boolean", "ENABLE_LOGGING", "true")
	}

	buildTypes {
		release {
			isMinifyEnabled = true
			isShrinkResources = true
			proguardFiles(
				getDefaultProguardFile("proguard-android-optimize.txt"),
				"proguard-rules.pro"
			)
		}
	}
	
	compileOptions {
		sourceCompatibility = JavaVersion.VERSION_17
		targetCompatibility = JavaVersion.VERSION_17
		isCoreLibraryDesugaringEnabled = true
	}
	
	kotlinOptions {
		jvmTarget = "17"
	}
	
	buildFeatures {
		viewBinding = true
		buildConfig = true
	}

	// Suppress warnings
	lint {
		checkReleaseBuilds = false
		abortOnError = false
		disable.addAll(listOf("MissingTranslation", "ExtraTranslation"))
	}
}

dependencies {
	// AndroidX Core
	implementation(libs.androidx.core.ktx)
	implementation(libs.androidx.appcompat)
	implementation(libs.androidx.material)
	implementation(libs.androidx.constraintlayout)

	// Navigation
	implementation(libs.androidx.navigation.fragment.ktx)
	implementation(libs.androidx.navigation.ui.ktx)

	// Lifecycle
	implementation(libs.androidx.lifecycle.viewmodel.ktx)
	implementation(libs.androidx.lifecycle.livedata.ktx)
	implementation(libs.androidx.lifecycle.runtime.ktx)

	// Coroutines
	implementation(libs.kotlinx.coroutines.android)
	implementation(libs.kotlinx.coroutines.core)

	// KotlinX DateTime
	implementation(libs.kotlinx.datetime)
	coreLibraryDesugaring(libs.desugar.jdk)

	// Hilt - Updated versions
	implementation("com.google.dagger:hilt-android:2.50")
	kapt("com.google.dagger:hilt-compiler:2.50")

	// Room - Updated versions
	val room_version = "2.6.1"
	implementation("androidx.room:room-runtime:$room_version")
	implementation("androidx.room:room-ktx:$room_version")
	kapt("androidx.room:room-compiler:$room_version")

	// Networking
	implementation(libs.squareup.okhttp)
	implementation(libs.squareup.okhttp.logging)
	implementation(libs.squareup.retrofit)
	implementation(libs.squareup.retrofit.converter.gson)
	implementation(libs.google.gson)

	// Image Loading
	implementation(libs.github.glide)
	kapt(libs.github.glide.compiler)

	// Circle ImageView
	implementation(libs.hdodenhof.circleimageview)

	// Supabase BOM + modules
	implementation(platform(libs.supabase.bom))
	implementation(libs.supabase.postgrest)
	implementation(libs.supabase.gotrue)
	implementation(libs.supabase.realtime)
	implementation(libs.supabase.storage)

	// Ktor engine for supabase-kt
	implementation(libs.ktor.client.android)

	// Testing
	testImplementation(libs.test.junit)
	androidTestImplementation(libs.androidTest.androidx.junit)
	androidTestImplementation(libs.androidTest.androidx.espresso.core)
}
	androidTestImplementation(libs.androidTest.androidx.junit)
	androidTestImplementation(libs.androidTest.androidx.espresso.core)
}
