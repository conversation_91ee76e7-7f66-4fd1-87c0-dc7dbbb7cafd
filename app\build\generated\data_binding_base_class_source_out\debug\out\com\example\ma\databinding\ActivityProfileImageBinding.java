// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import de.hdodenhof.circleimageview.CircleImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityProfileImageBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final MaterialButton btnSelectImage;

  @NonNull
  public final MaterialButton btnTakePhoto;

  @NonNull
  public final FloatingActionButton fabCamera;

  @NonNull
  public final CircleImageView ivProfileImage;

  private ActivityProfileImageBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton btnCancel, @NonNull MaterialButton btnSave,
      @NonNull MaterialButton btnSelectImage, @NonNull MaterialButton btnTakePhoto,
      @NonNull FloatingActionButton fabCamera, @NonNull CircleImageView ivProfileImage) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnSave = btnSave;
    this.btnSelectImage = btnSelectImage;
    this.btnTakePhoto = btnTakePhoto;
    this.fabCamera = fabCamera;
    this.ivProfileImage = ivProfileImage;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityProfileImageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityProfileImageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_profile_image, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityProfileImageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnSave;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.btnSelectImage;
      MaterialButton btnSelectImage = ViewBindings.findChildViewById(rootView, id);
      if (btnSelectImage == null) {
        break missingId;
      }

      id = R.id.btnTakePhoto;
      MaterialButton btnTakePhoto = ViewBindings.findChildViewById(rootView, id);
      if (btnTakePhoto == null) {
        break missingId;
      }

      id = R.id.fabCamera;
      FloatingActionButton fabCamera = ViewBindings.findChildViewById(rootView, id);
      if (fabCamera == null) {
        break missingId;
      }

      id = R.id.ivProfileImage;
      CircleImageView ivProfileImage = ViewBindings.findChildViewById(rootView, id);
      if (ivProfileImage == null) {
        break missingId;
      }

      return new ActivityProfileImageBinding((LinearLayout) rootView, btnCancel, btnSave,
          btnSelectImage, btnTakePhoto, fabCamera, ivProfileImage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
