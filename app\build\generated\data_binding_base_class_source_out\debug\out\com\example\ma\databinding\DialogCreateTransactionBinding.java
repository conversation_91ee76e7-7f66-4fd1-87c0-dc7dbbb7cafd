// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.textfield.TextInputEditText;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogCreateTransactionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCancel;

  @NonNull
  public final Button btnSave;

  @NonNull
  public final TextInputEditText etAmount;

  @NonNull
  public final TextInputEditText etDescription;

  @NonNull
  public final RadioButton rbCapital;

  @NonNull
  public final RadioButton rbExpense;

  @NonNull
  public final RadioButton rbSale;

  @NonNull
  public final RadioButton rbWithdrawal;

  @NonNull
  public final RadioGroup rgTransactionType;

  private DialogCreateTransactionBinding(@NonNull LinearLayout rootView, @NonNull Button btnCancel,
      @NonNull Button btnSave, @NonNull TextInputEditText etAmount,
      @NonNull TextInputEditText etDescription, @NonNull RadioButton rbCapital,
      @NonNull RadioButton rbExpense, @NonNull RadioButton rbSale,
      @NonNull RadioButton rbWithdrawal, @NonNull RadioGroup rgTransactionType) {
    this.rootView = rootView;
    this.btnCancel = btnCancel;
    this.btnSave = btnSave;
    this.etAmount = etAmount;
    this.etDescription = etDescription;
    this.rbCapital = rbCapital;
    this.rbExpense = rbExpense;
    this.rbSale = rbSale;
    this.rbWithdrawal = rbWithdrawal;
    this.rgTransactionType = rgTransactionType;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogCreateTransactionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogCreateTransactionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_create_transaction, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogCreateTransactionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCancel;
      Button btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btnSave;
      Button btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.etAmount;
      TextInputEditText etAmount = ViewBindings.findChildViewById(rootView, id);
      if (etAmount == null) {
        break missingId;
      }

      id = R.id.etDescription;
      TextInputEditText etDescription = ViewBindings.findChildViewById(rootView, id);
      if (etDescription == null) {
        break missingId;
      }

      id = R.id.rbCapital;
      RadioButton rbCapital = ViewBindings.findChildViewById(rootView, id);
      if (rbCapital == null) {
        break missingId;
      }

      id = R.id.rbExpense;
      RadioButton rbExpense = ViewBindings.findChildViewById(rootView, id);
      if (rbExpense == null) {
        break missingId;
      }

      id = R.id.rbSale;
      RadioButton rbSale = ViewBindings.findChildViewById(rootView, id);
      if (rbSale == null) {
        break missingId;
      }

      id = R.id.rbWithdrawal;
      RadioButton rbWithdrawal = ViewBindings.findChildViewById(rootView, id);
      if (rbWithdrawal == null) {
        break missingId;
      }

      id = R.id.rgTransactionType;
      RadioGroup rgTransactionType = ViewBindings.findChildViewById(rootView, id);
      if (rgTransactionType == null) {
        break missingId;
      }

      return new DialogCreateTransactionBinding((LinearLayout) rootView, btnCancel, btnSave,
          etAmount, etDescription, rbCapital, rbExpense, rbSale, rbWithdrawal, rgTransactionType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
