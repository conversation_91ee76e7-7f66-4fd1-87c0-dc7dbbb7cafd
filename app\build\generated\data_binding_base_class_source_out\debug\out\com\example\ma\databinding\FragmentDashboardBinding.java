// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.button.MaterialButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentDashboardBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton btnRegisterTransaction;

  @NonNull
  public final MaterialButton btnReports;

  @NonNull
  public final TextView tvMyShare;

  @NonNull
  public final TextView tvNetProfit;

  @NonNull
  public final TextView tvPartnerShare;

  @NonNull
  public final TextView tvTotalExpenses;

  @NonNull
  public final TextView tvTotalSales;

  private FragmentDashboardBinding(@NonNull ScrollView rootView,
      @NonNull MaterialButton btnRegisterTransaction, @NonNull MaterialButton btnReports,
      @NonNull TextView tvMyShare, @NonNull TextView tvNetProfit, @NonNull TextView tvPartnerShare,
      @NonNull TextView tvTotalExpenses, @NonNull TextView tvTotalSales) {
    this.rootView = rootView;
    this.btnRegisterTransaction = btnRegisterTransaction;
    this.btnReports = btnReports;
    this.tvMyShare = tvMyShare;
    this.tvNetProfit = tvNetProfit;
    this.tvPartnerShare = tvPartnerShare;
    this.tvTotalExpenses = tvTotalExpenses;
    this.tvTotalSales = tvTotalSales;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentDashboardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentDashboardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_dashboard, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentDashboardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnRegisterTransaction;
      MaterialButton btnRegisterTransaction = ViewBindings.findChildViewById(rootView, id);
      if (btnRegisterTransaction == null) {
        break missingId;
      }

      id = R.id.btnReports;
      MaterialButton btnReports = ViewBindings.findChildViewById(rootView, id);
      if (btnReports == null) {
        break missingId;
      }

      id = R.id.tvMyShare;
      TextView tvMyShare = ViewBindings.findChildViewById(rootView, id);
      if (tvMyShare == null) {
        break missingId;
      }

      id = R.id.tvNetProfit;
      TextView tvNetProfit = ViewBindings.findChildViewById(rootView, id);
      if (tvNetProfit == null) {
        break missingId;
      }

      id = R.id.tvPartnerShare;
      TextView tvPartnerShare = ViewBindings.findChildViewById(rootView, id);
      if (tvPartnerShare == null) {
        break missingId;
      }

      id = R.id.tvTotalExpenses;
      TextView tvTotalExpenses = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalExpenses == null) {
        break missingId;
      }

      id = R.id.tvTotalSales;
      TextView tvTotalSales = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalSales == null) {
        break missingId;
      }

      return new FragmentDashboardBinding((ScrollView) rootView, btnRegisterTransaction, btnReports,
          tvMyShare, tvNetProfit, tvPartnerShare, tvTotalExpenses, tvTotalSales);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
