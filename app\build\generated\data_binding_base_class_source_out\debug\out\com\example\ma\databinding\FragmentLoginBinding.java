// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.checkbox.MaterialCheckBox;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentLoginBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton btnAppleSignIn;

  @NonNull
  public final MaterialButton btnForgotPassword;

  @NonNull
  public final MaterialButton btnGoogleSignIn;

  @NonNull
  public final MaterialButton btnLogin;

  @NonNull
  public final MaterialButton btnSignUp;

  @NonNull
  public final MaterialCheckBox cbRememberMe;

  @NonNull
  public final TextInputEditText etEmail;

  @NonNull
  public final TextInputEditText etPassword;

  @NonNull
  public final LinearLayout headerLayout;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final MaterialCardView loginCard;

  @NonNull
  public final CircularProgressIndicator progressIndicator;

  @NonNull
  public final TextInputLayout tilEmail;

  @NonNull
  public final TextInputLayout tilPassword;

  private FragmentLoginBinding(@NonNull ScrollView rootView, @NonNull MaterialButton btnAppleSignIn,
      @NonNull MaterialButton btnForgotPassword, @NonNull MaterialButton btnGoogleSignIn,
      @NonNull MaterialButton btnLogin, @NonNull MaterialButton btnSignUp,
      @NonNull MaterialCheckBox cbRememberMe, @NonNull TextInputEditText etEmail,
      @NonNull TextInputEditText etPassword, @NonNull LinearLayout headerLayout,
      @NonNull ImageView ivLogo, @NonNull MaterialCardView loginCard,
      @NonNull CircularProgressIndicator progressIndicator, @NonNull TextInputLayout tilEmail,
      @NonNull TextInputLayout tilPassword) {
    this.rootView = rootView;
    this.btnAppleSignIn = btnAppleSignIn;
    this.btnForgotPassword = btnForgotPassword;
    this.btnGoogleSignIn = btnGoogleSignIn;
    this.btnLogin = btnLogin;
    this.btnSignUp = btnSignUp;
    this.cbRememberMe = cbRememberMe;
    this.etEmail = etEmail;
    this.etPassword = etPassword;
    this.headerLayout = headerLayout;
    this.ivLogo = ivLogo;
    this.loginCard = loginCard;
    this.progressIndicator = progressIndicator;
    this.tilEmail = tilEmail;
    this.tilPassword = tilPassword;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentLoginBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentLoginBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_login, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentLoginBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAppleSignIn;
      MaterialButton btnAppleSignIn = ViewBindings.findChildViewById(rootView, id);
      if (btnAppleSignIn == null) {
        break missingId;
      }

      id = R.id.btnForgotPassword;
      MaterialButton btnForgotPassword = ViewBindings.findChildViewById(rootView, id);
      if (btnForgotPassword == null) {
        break missingId;
      }

      id = R.id.btnGoogleSignIn;
      MaterialButton btnGoogleSignIn = ViewBindings.findChildViewById(rootView, id);
      if (btnGoogleSignIn == null) {
        break missingId;
      }

      id = R.id.btnLogin;
      MaterialButton btnLogin = ViewBindings.findChildViewById(rootView, id);
      if (btnLogin == null) {
        break missingId;
      }

      id = R.id.btnSignUp;
      MaterialButton btnSignUp = ViewBindings.findChildViewById(rootView, id);
      if (btnSignUp == null) {
        break missingId;
      }

      id = R.id.cbRememberMe;
      MaterialCheckBox cbRememberMe = ViewBindings.findChildViewById(rootView, id);
      if (cbRememberMe == null) {
        break missingId;
      }

      id = R.id.etEmail;
      TextInputEditText etEmail = ViewBindings.findChildViewById(rootView, id);
      if (etEmail == null) {
        break missingId;
      }

      id = R.id.etPassword;
      TextInputEditText etPassword = ViewBindings.findChildViewById(rootView, id);
      if (etPassword == null) {
        break missingId;
      }

      id = R.id.headerLayout;
      LinearLayout headerLayout = ViewBindings.findChildViewById(rootView, id);
      if (headerLayout == null) {
        break missingId;
      }

      id = R.id.ivLogo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.loginCard;
      MaterialCardView loginCard = ViewBindings.findChildViewById(rootView, id);
      if (loginCard == null) {
        break missingId;
      }

      id = R.id.progressIndicator;
      CircularProgressIndicator progressIndicator = ViewBindings.findChildViewById(rootView, id);
      if (progressIndicator == null) {
        break missingId;
      }

      id = R.id.tilEmail;
      TextInputLayout tilEmail = ViewBindings.findChildViewById(rootView, id);
      if (tilEmail == null) {
        break missingId;
      }

      id = R.id.tilPassword;
      TextInputLayout tilPassword = ViewBindings.findChildViewById(rootView, id);
      if (tilPassword == null) {
        break missingId;
      }

      return new FragmentLoginBinding((ScrollView) rootView, btnAppleSignIn, btnForgotPassword,
          btnGoogleSignIn, btnLogin, btnSignUp, cbRememberMe, etEmail, etPassword, headerLayout,
          ivLogo, loginCard, progressIndicator, tilEmail, tilPassword);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
