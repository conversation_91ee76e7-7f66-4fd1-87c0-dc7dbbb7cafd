// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.appbar.MaterialToolbar;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentNotificationsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout emptyStateLayout;

  @NonNull
  public final RecyclerView recyclerViewNotifications;

  @NonNull
  public final MaterialToolbar toolbar;

  private FragmentNotificationsBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout emptyStateLayout, @NonNull RecyclerView recyclerViewNotifications,
      @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.emptyStateLayout = emptyStateLayout;
    this.recyclerViewNotifications = recyclerViewNotifications;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentNotificationsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentNotificationsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_notifications, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentNotificationsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.emptyStateLayout;
      LinearLayout emptyStateLayout = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateLayout == null) {
        break missingId;
      }

      id = R.id.recyclerViewNotifications;
      RecyclerView recyclerViewNotifications = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewNotifications == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new FragmentNotificationsBinding((LinearLayout) rootView, emptyStateLayout,
          recyclerViewNotifications, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
