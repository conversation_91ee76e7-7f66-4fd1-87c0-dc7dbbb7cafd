// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import de.hdodenhof.circleimageview.CircleImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentProfileBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton btnChangePassword;

  @NonNull
  public final MaterialButton btnChangePhoto;

  @NonNull
  public final MaterialButton btnEditProfile;

  @NonNull
  public final MaterialCardView cardFinancialSummary;

  @NonNull
  public final MaterialCardView cardProfileInfo;

  @NonNull
  public final CircleImageView ivProfileImage;

  @NonNull
  public final TextView tvMyShare;

  @NonNull
  public final TextView tvTotalExpenses;

  @NonNull
  public final TextView tvTotalSales;

  @NonNull
  public final TextView tvUserEmail;

  @NonNull
  public final TextView tvUserName;

  @NonNull
  public final TextView tvUserPhone;

  @NonNull
  public final TextView tvUserRole;

  private FragmentProfileBinding(@NonNull ScrollView rootView,
      @NonNull MaterialButton btnChangePassword, @NonNull MaterialButton btnChangePhoto,
      @NonNull MaterialButton btnEditProfile, @NonNull MaterialCardView cardFinancialSummary,
      @NonNull MaterialCardView cardProfileInfo, @NonNull CircleImageView ivProfileImage,
      @NonNull TextView tvMyShare, @NonNull TextView tvTotalExpenses,
      @NonNull TextView tvTotalSales, @NonNull TextView tvUserEmail, @NonNull TextView tvUserName,
      @NonNull TextView tvUserPhone, @NonNull TextView tvUserRole) {
    this.rootView = rootView;
    this.btnChangePassword = btnChangePassword;
    this.btnChangePhoto = btnChangePhoto;
    this.btnEditProfile = btnEditProfile;
    this.cardFinancialSummary = cardFinancialSummary;
    this.cardProfileInfo = cardProfileInfo;
    this.ivProfileImage = ivProfileImage;
    this.tvMyShare = tvMyShare;
    this.tvTotalExpenses = tvTotalExpenses;
    this.tvTotalSales = tvTotalSales;
    this.tvUserEmail = tvUserEmail;
    this.tvUserName = tvUserName;
    this.tvUserPhone = tvUserPhone;
    this.tvUserRole = tvUserRole;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentProfileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_profile, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentProfileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnChangePassword;
      MaterialButton btnChangePassword = ViewBindings.findChildViewById(rootView, id);
      if (btnChangePassword == null) {
        break missingId;
      }

      id = R.id.btnChangePhoto;
      MaterialButton btnChangePhoto = ViewBindings.findChildViewById(rootView, id);
      if (btnChangePhoto == null) {
        break missingId;
      }

      id = R.id.btnEditProfile;
      MaterialButton btnEditProfile = ViewBindings.findChildViewById(rootView, id);
      if (btnEditProfile == null) {
        break missingId;
      }

      id = R.id.cardFinancialSummary;
      MaterialCardView cardFinancialSummary = ViewBindings.findChildViewById(rootView, id);
      if (cardFinancialSummary == null) {
        break missingId;
      }

      id = R.id.cardProfileInfo;
      MaterialCardView cardProfileInfo = ViewBindings.findChildViewById(rootView, id);
      if (cardProfileInfo == null) {
        break missingId;
      }

      id = R.id.ivProfileImage;
      CircleImageView ivProfileImage = ViewBindings.findChildViewById(rootView, id);
      if (ivProfileImage == null) {
        break missingId;
      }

      id = R.id.tvMyShare;
      TextView tvMyShare = ViewBindings.findChildViewById(rootView, id);
      if (tvMyShare == null) {
        break missingId;
      }

      id = R.id.tvTotalExpenses;
      TextView tvTotalExpenses = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalExpenses == null) {
        break missingId;
      }

      id = R.id.tvTotalSales;
      TextView tvTotalSales = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalSales == null) {
        break missingId;
      }

      id = R.id.tvUserEmail;
      TextView tvUserEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvUserEmail == null) {
        break missingId;
      }

      id = R.id.tvUserName;
      TextView tvUserName = ViewBindings.findChildViewById(rootView, id);
      if (tvUserName == null) {
        break missingId;
      }

      id = R.id.tvUserPhone;
      TextView tvUserPhone = ViewBindings.findChildViewById(rootView, id);
      if (tvUserPhone == null) {
        break missingId;
      }

      id = R.id.tvUserRole;
      TextView tvUserRole = ViewBindings.findChildViewById(rootView, id);
      if (tvUserRole == null) {
        break missingId;
      }

      return new FragmentProfileBinding((ScrollView) rootView, btnChangePassword, btnChangePhoto,
          btnEditProfile, cardFinancialSummary, cardProfileInfo, ivProfileImage, tvMyShare,
          tvTotalExpenses, tvTotalSales, tvUserEmail, tvUserName, tvUserPhone, tvUserRole);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
