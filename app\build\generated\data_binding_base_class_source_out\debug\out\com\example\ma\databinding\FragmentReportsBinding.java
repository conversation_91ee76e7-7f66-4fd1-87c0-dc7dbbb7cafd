// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentReportsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton btnExportReport;

  @NonNull
  public final MaterialButton btnShareReport;

  @NonNull
  public final MaterialCardView cardCategoryAnalysis;

  @NonNull
  public final MaterialCardView cardDailyReport;

  @NonNull
  public final MaterialCardView cardMonthlyReport;

  @NonNull
  public final MaterialCardView cardPartnerComparison;

  @NonNull
  public final TextView tvInventoryCount;

  @NonNull
  public final TextView tvMyShare;

  @NonNull
  public final TextView tvNetProfit;

  @NonNull
  public final TextView tvPartnerShare;

  @NonNull
  public final TextView tvTotalExpenses;

  @NonNull
  public final TextView tvTotalSales;

  private FragmentReportsBinding(@NonNull ScrollView rootView,
      @NonNull MaterialButton btnExportReport, @NonNull MaterialButton btnShareReport,
      @NonNull MaterialCardView cardCategoryAnalysis, @NonNull MaterialCardView cardDailyReport,
      @NonNull MaterialCardView cardMonthlyReport, @NonNull MaterialCardView cardPartnerComparison,
      @NonNull TextView tvInventoryCount, @NonNull TextView tvMyShare,
      @NonNull TextView tvNetProfit, @NonNull TextView tvPartnerShare,
      @NonNull TextView tvTotalExpenses, @NonNull TextView tvTotalSales) {
    this.rootView = rootView;
    this.btnExportReport = btnExportReport;
    this.btnShareReport = btnShareReport;
    this.cardCategoryAnalysis = cardCategoryAnalysis;
    this.cardDailyReport = cardDailyReport;
    this.cardMonthlyReport = cardMonthlyReport;
    this.cardPartnerComparison = cardPartnerComparison;
    this.tvInventoryCount = tvInventoryCount;
    this.tvMyShare = tvMyShare;
    this.tvNetProfit = tvNetProfit;
    this.tvPartnerShare = tvPartnerShare;
    this.tvTotalExpenses = tvTotalExpenses;
    this.tvTotalSales = tvTotalSales;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentReportsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentReportsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_reports, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentReportsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnExportReport;
      MaterialButton btnExportReport = ViewBindings.findChildViewById(rootView, id);
      if (btnExportReport == null) {
        break missingId;
      }

      id = R.id.btnShareReport;
      MaterialButton btnShareReport = ViewBindings.findChildViewById(rootView, id);
      if (btnShareReport == null) {
        break missingId;
      }

      id = R.id.cardCategoryAnalysis;
      MaterialCardView cardCategoryAnalysis = ViewBindings.findChildViewById(rootView, id);
      if (cardCategoryAnalysis == null) {
        break missingId;
      }

      id = R.id.cardDailyReport;
      MaterialCardView cardDailyReport = ViewBindings.findChildViewById(rootView, id);
      if (cardDailyReport == null) {
        break missingId;
      }

      id = R.id.cardMonthlyReport;
      MaterialCardView cardMonthlyReport = ViewBindings.findChildViewById(rootView, id);
      if (cardMonthlyReport == null) {
        break missingId;
      }

      id = R.id.cardPartnerComparison;
      MaterialCardView cardPartnerComparison = ViewBindings.findChildViewById(rootView, id);
      if (cardPartnerComparison == null) {
        break missingId;
      }

      id = R.id.tvInventoryCount;
      TextView tvInventoryCount = ViewBindings.findChildViewById(rootView, id);
      if (tvInventoryCount == null) {
        break missingId;
      }

      id = R.id.tvMyShare;
      TextView tvMyShare = ViewBindings.findChildViewById(rootView, id);
      if (tvMyShare == null) {
        break missingId;
      }

      id = R.id.tvNetProfit;
      TextView tvNetProfit = ViewBindings.findChildViewById(rootView, id);
      if (tvNetProfit == null) {
        break missingId;
      }

      id = R.id.tvPartnerShare;
      TextView tvPartnerShare = ViewBindings.findChildViewById(rootView, id);
      if (tvPartnerShare == null) {
        break missingId;
      }

      id = R.id.tvTotalExpenses;
      TextView tvTotalExpenses = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalExpenses == null) {
        break missingId;
      }

      id = R.id.tvTotalSales;
      TextView tvTotalSales = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalSales == null) {
        break missingId;
      }

      return new FragmentReportsBinding((ScrollView) rootView, btnExportReport, btnShareReport,
          cardCategoryAnalysis, cardDailyReport, cardMonthlyReport, cardPartnerComparison,
          tvInventoryCount, tvMyShare, tvNetProfit, tvPartnerShare, tvTotalExpenses, tvTotalSales);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
