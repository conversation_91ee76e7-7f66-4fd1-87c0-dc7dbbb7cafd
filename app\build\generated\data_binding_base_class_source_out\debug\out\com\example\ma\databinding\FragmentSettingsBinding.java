// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.google.android.material.textview.MaterialTextView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentSettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final MaterialButton btnBackupData;

  @NonNull
  public final MaterialButton btnChangePassword;

  @NonNull
  public final MaterialButton btnChangeTheme;

  @NonNull
  public final MaterialButton btnClearCache;

  @NonNull
  public final MaterialButton btnContactSupport;

  @NonNull
  public final MaterialButton btnLogout;

  @NonNull
  public final MaterialCardView cardAbout;

  @NonNull
  public final MaterialCardView cardAppearance;

  @NonNull
  public final MaterialCardView cardDataSync;

  @NonNull
  public final MaterialCardView cardNotifications;

  @NonNull
  public final MaterialCardView cardSecurity;

  @NonNull
  public final SwitchMaterial switchAutoLock;

  @NonNull
  public final SwitchMaterial switchAutoSync;

  @NonNull
  public final SwitchMaterial switchBiometric;

  @NonNull
  public final SwitchMaterial switchPushNotifications;

  @NonNull
  public final SwitchMaterial switchSoundVibration;

  @NonNull
  public final SwitchMaterial switchTransactionAlerts;

  @NonNull
  public final MaterialTextView tvAppVersion;

  @NonNull
  public final MaterialTextView tvCurrentTheme;

  private FragmentSettingsBinding(@NonNull ScrollView rootView,
      @NonNull MaterialButton btnBackupData, @NonNull MaterialButton btnChangePassword,
      @NonNull MaterialButton btnChangeTheme, @NonNull MaterialButton btnClearCache,
      @NonNull MaterialButton btnContactSupport, @NonNull MaterialButton btnLogout,
      @NonNull MaterialCardView cardAbout, @NonNull MaterialCardView cardAppearance,
      @NonNull MaterialCardView cardDataSync, @NonNull MaterialCardView cardNotifications,
      @NonNull MaterialCardView cardSecurity, @NonNull SwitchMaterial switchAutoLock,
      @NonNull SwitchMaterial switchAutoSync, @NonNull SwitchMaterial switchBiometric,
      @NonNull SwitchMaterial switchPushNotifications, @NonNull SwitchMaterial switchSoundVibration,
      @NonNull SwitchMaterial switchTransactionAlerts, @NonNull MaterialTextView tvAppVersion,
      @NonNull MaterialTextView tvCurrentTheme) {
    this.rootView = rootView;
    this.btnBackupData = btnBackupData;
    this.btnChangePassword = btnChangePassword;
    this.btnChangeTheme = btnChangeTheme;
    this.btnClearCache = btnClearCache;
    this.btnContactSupport = btnContactSupport;
    this.btnLogout = btnLogout;
    this.cardAbout = cardAbout;
    this.cardAppearance = cardAppearance;
    this.cardDataSync = cardDataSync;
    this.cardNotifications = cardNotifications;
    this.cardSecurity = cardSecurity;
    this.switchAutoLock = switchAutoLock;
    this.switchAutoSync = switchAutoSync;
    this.switchBiometric = switchBiometric;
    this.switchPushNotifications = switchPushNotifications;
    this.switchSoundVibration = switchSoundVibration;
    this.switchTransactionAlerts = switchTransactionAlerts;
    this.tvAppVersion = tvAppVersion;
    this.tvCurrentTheme = tvCurrentTheme;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnBackupData;
      MaterialButton btnBackupData = ViewBindings.findChildViewById(rootView, id);
      if (btnBackupData == null) {
        break missingId;
      }

      id = R.id.btnChangePassword;
      MaterialButton btnChangePassword = ViewBindings.findChildViewById(rootView, id);
      if (btnChangePassword == null) {
        break missingId;
      }

      id = R.id.btnChangeTheme;
      MaterialButton btnChangeTheme = ViewBindings.findChildViewById(rootView, id);
      if (btnChangeTheme == null) {
        break missingId;
      }

      id = R.id.btnClearCache;
      MaterialButton btnClearCache = ViewBindings.findChildViewById(rootView, id);
      if (btnClearCache == null) {
        break missingId;
      }

      id = R.id.btnContactSupport;
      MaterialButton btnContactSupport = ViewBindings.findChildViewById(rootView, id);
      if (btnContactSupport == null) {
        break missingId;
      }

      id = R.id.btnLogout;
      MaterialButton btnLogout = ViewBindings.findChildViewById(rootView, id);
      if (btnLogout == null) {
        break missingId;
      }

      id = R.id.cardAbout;
      MaterialCardView cardAbout = ViewBindings.findChildViewById(rootView, id);
      if (cardAbout == null) {
        break missingId;
      }

      id = R.id.cardAppearance;
      MaterialCardView cardAppearance = ViewBindings.findChildViewById(rootView, id);
      if (cardAppearance == null) {
        break missingId;
      }

      id = R.id.cardDataSync;
      MaterialCardView cardDataSync = ViewBindings.findChildViewById(rootView, id);
      if (cardDataSync == null) {
        break missingId;
      }

      id = R.id.cardNotifications;
      MaterialCardView cardNotifications = ViewBindings.findChildViewById(rootView, id);
      if (cardNotifications == null) {
        break missingId;
      }

      id = R.id.cardSecurity;
      MaterialCardView cardSecurity = ViewBindings.findChildViewById(rootView, id);
      if (cardSecurity == null) {
        break missingId;
      }

      id = R.id.switchAutoLock;
      SwitchMaterial switchAutoLock = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoLock == null) {
        break missingId;
      }

      id = R.id.switchAutoSync;
      SwitchMaterial switchAutoSync = ViewBindings.findChildViewById(rootView, id);
      if (switchAutoSync == null) {
        break missingId;
      }

      id = R.id.switchBiometric;
      SwitchMaterial switchBiometric = ViewBindings.findChildViewById(rootView, id);
      if (switchBiometric == null) {
        break missingId;
      }

      id = R.id.switchPushNotifications;
      SwitchMaterial switchPushNotifications = ViewBindings.findChildViewById(rootView, id);
      if (switchPushNotifications == null) {
        break missingId;
      }

      id = R.id.switchSoundVibration;
      SwitchMaterial switchSoundVibration = ViewBindings.findChildViewById(rootView, id);
      if (switchSoundVibration == null) {
        break missingId;
      }

      id = R.id.switchTransactionAlerts;
      SwitchMaterial switchTransactionAlerts = ViewBindings.findChildViewById(rootView, id);
      if (switchTransactionAlerts == null) {
        break missingId;
      }

      id = R.id.tvAppVersion;
      MaterialTextView tvAppVersion = ViewBindings.findChildViewById(rootView, id);
      if (tvAppVersion == null) {
        break missingId;
      }

      id = R.id.tvCurrentTheme;
      MaterialTextView tvCurrentTheme = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentTheme == null) {
        break missingId;
      }

      return new FragmentSettingsBinding((ScrollView) rootView, btnBackupData, btnChangePassword,
          btnChangeTheme, btnClearCache, btnContactSupport, btnLogout, cardAbout, cardAppearance,
          cardDataSync, cardNotifications, cardSecurity, switchAutoLock, switchAutoSync,
          switchBiometric, switchPushNotifications, switchSoundVibration, switchTransactionAlerts,
          tvAppVersion, tvCurrentTheme);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
