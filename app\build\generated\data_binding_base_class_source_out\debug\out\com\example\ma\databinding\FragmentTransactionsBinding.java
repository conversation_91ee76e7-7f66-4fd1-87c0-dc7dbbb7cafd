// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentTransactionsBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton btnFilter;

  @NonNull
  public final LinearLayout emptyStateLayout;

  @NonNull
  public final FloatingActionButton fabAddTransaction;

  @NonNull
  public final RecyclerView recyclerViewTransactions;

  @NonNull
  public final TextView tvEmptyState;

  private FragmentTransactionsBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton btnFilter, @NonNull LinearLayout emptyStateLayout,
      @NonNull FloatingActionButton fabAddTransaction,
      @NonNull RecyclerView recyclerViewTransactions, @NonNull TextView tvEmptyState) {
    this.rootView = rootView;
    this.btnFilter = btnFilter;
    this.emptyStateLayout = emptyStateLayout;
    this.fabAddTransaction = fabAddTransaction;
    this.recyclerViewTransactions = recyclerViewTransactions;
    this.tvEmptyState = tvEmptyState;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTransactionsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTransactionsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_transactions, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTransactionsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnFilter;
      MaterialButton btnFilter = ViewBindings.findChildViewById(rootView, id);
      if (btnFilter == null) {
        break missingId;
      }

      id = R.id.emptyStateLayout;
      LinearLayout emptyStateLayout = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateLayout == null) {
        break missingId;
      }

      id = R.id.fabAddTransaction;
      FloatingActionButton fabAddTransaction = ViewBindings.findChildViewById(rootView, id);
      if (fabAddTransaction == null) {
        break missingId;
      }

      id = R.id.recyclerViewTransactions;
      RecyclerView recyclerViewTransactions = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewTransactions == null) {
        break missingId;
      }

      id = R.id.tvEmptyState;
      TextView tvEmptyState = ViewBindings.findChildViewById(rootView, id);
      if (tvEmptyState == null) {
        break missingId;
      }

      return new FragmentTransactionsBinding((CoordinatorLayout) rootView, btnFilter,
          emptyStateLayout, fabAddTransaction, recyclerViewTransactions, tvEmptyState);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
