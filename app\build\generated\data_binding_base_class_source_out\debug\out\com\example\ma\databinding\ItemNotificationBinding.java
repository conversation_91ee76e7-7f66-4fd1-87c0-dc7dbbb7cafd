// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.Chip;
import de.hdodenhof.circleimageview.CircleImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemNotificationBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final MaterialButton btnApprove;

  @NonNull
  public final MaterialButton btnReject;

  @NonNull
  public final MaterialCardView cardNotification;

  @NonNull
  public final Chip chipNotificationType;

  @NonNull
  public final View indicatorUnread;

  @NonNull
  public final CircleImageView ivSenderProfile;

  @NonNull
  public final TextView tvNotificationMessage;

  @NonNull
  public final TextView tvNotificationTime;

  @NonNull
  public final TextView tvNotificationTitle;

  @NonNull
  public final TextView tvSenderName;

  private ItemNotificationBinding(@NonNull MaterialCardView rootView,
      @NonNull MaterialButton btnApprove, @NonNull MaterialButton btnReject,
      @NonNull MaterialCardView cardNotification, @NonNull Chip chipNotificationType,
      @NonNull View indicatorUnread, @NonNull CircleImageView ivSenderProfile,
      @NonNull TextView tvNotificationMessage, @NonNull TextView tvNotificationTime,
      @NonNull TextView tvNotificationTitle, @NonNull TextView tvSenderName) {
    this.rootView = rootView;
    this.btnApprove = btnApprove;
    this.btnReject = btnReject;
    this.cardNotification = cardNotification;
    this.chipNotificationType = chipNotificationType;
    this.indicatorUnread = indicatorUnread;
    this.ivSenderProfile = ivSenderProfile;
    this.tvNotificationMessage = tvNotificationMessage;
    this.tvNotificationTime = tvNotificationTime;
    this.tvNotificationTitle = tvNotificationTitle;
    this.tvSenderName = tvSenderName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemNotificationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemNotificationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_notification, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemNotificationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnApprove;
      MaterialButton btnApprove = ViewBindings.findChildViewById(rootView, id);
      if (btnApprove == null) {
        break missingId;
      }

      id = R.id.btnReject;
      MaterialButton btnReject = ViewBindings.findChildViewById(rootView, id);
      if (btnReject == null) {
        break missingId;
      }

      MaterialCardView cardNotification = (MaterialCardView) rootView;

      id = R.id.chipNotificationType;
      Chip chipNotificationType = ViewBindings.findChildViewById(rootView, id);
      if (chipNotificationType == null) {
        break missingId;
      }

      id = R.id.indicatorUnread;
      View indicatorUnread = ViewBindings.findChildViewById(rootView, id);
      if (indicatorUnread == null) {
        break missingId;
      }

      id = R.id.ivSenderProfile;
      CircleImageView ivSenderProfile = ViewBindings.findChildViewById(rootView, id);
      if (ivSenderProfile == null) {
        break missingId;
      }

      id = R.id.tvNotificationMessage;
      TextView tvNotificationMessage = ViewBindings.findChildViewById(rootView, id);
      if (tvNotificationMessage == null) {
        break missingId;
      }

      id = R.id.tvNotificationTime;
      TextView tvNotificationTime = ViewBindings.findChildViewById(rootView, id);
      if (tvNotificationTime == null) {
        break missingId;
      }

      id = R.id.tvNotificationTitle;
      TextView tvNotificationTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvNotificationTitle == null) {
        break missingId;
      }

      id = R.id.tvSenderName;
      TextView tvSenderName = ViewBindings.findChildViewById(rootView, id);
      if (tvSenderName == null) {
        break missingId;
      }

      return new ItemNotificationBinding((MaterialCardView) rootView, btnApprove, btnReject,
          cardNotification, chipNotificationType, indicatorUnread, ivSenderProfile,
          tvNotificationMessage, tvNotificationTime, tvNotificationTitle, tvSenderName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
