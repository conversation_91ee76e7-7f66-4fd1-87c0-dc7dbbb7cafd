// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.chip.ChipGroup;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTransactionBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final LinearLayout categoryLayout;

  @NonNull
  public final ChipGroup chipGroupTags;

  @NonNull
  public final LinearLayout dateTimeLayout;

  @NonNull
  public final ImageView ivRecurring;

  @NonNull
  public final ImageView ivTransactionType;

  @NonNull
  public final LinearLayout locationLayout;

  @NonNull
  public final View statusIndicator;

  @NonNull
  public final TextView tvAmount;

  @NonNull
  public final TextView tvCategory;

  @NonNull
  public final TextView tvDate;

  @NonNull
  public final TextView tvDescription;

  @NonNull
  public final TextView tvLocation;

  @NonNull
  public final TextView tvSubcategory;

  @NonNull
  public final TextView tvTime;

  private ItemTransactionBinding(@NonNull MaterialCardView rootView,
      @NonNull LinearLayout categoryLayout, @NonNull ChipGroup chipGroupTags,
      @NonNull LinearLayout dateTimeLayout, @NonNull ImageView ivRecurring,
      @NonNull ImageView ivTransactionType, @NonNull LinearLayout locationLayout,
      @NonNull View statusIndicator, @NonNull TextView tvAmount, @NonNull TextView tvCategory,
      @NonNull TextView tvDate, @NonNull TextView tvDescription, @NonNull TextView tvLocation,
      @NonNull TextView tvSubcategory, @NonNull TextView tvTime) {
    this.rootView = rootView;
    this.categoryLayout = categoryLayout;
    this.chipGroupTags = chipGroupTags;
    this.dateTimeLayout = dateTimeLayout;
    this.ivRecurring = ivRecurring;
    this.ivTransactionType = ivTransactionType;
    this.locationLayout = locationLayout;
    this.statusIndicator = statusIndicator;
    this.tvAmount = tvAmount;
    this.tvCategory = tvCategory;
    this.tvDate = tvDate;
    this.tvDescription = tvDescription;
    this.tvLocation = tvLocation;
    this.tvSubcategory = tvSubcategory;
    this.tvTime = tvTime;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTransactionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTransactionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_transaction, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTransactionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.categoryLayout;
      LinearLayout categoryLayout = ViewBindings.findChildViewById(rootView, id);
      if (categoryLayout == null) {
        break missingId;
      }

      id = R.id.chipGroupTags;
      ChipGroup chipGroupTags = ViewBindings.findChildViewById(rootView, id);
      if (chipGroupTags == null) {
        break missingId;
      }

      id = R.id.dateTimeLayout;
      LinearLayout dateTimeLayout = ViewBindings.findChildViewById(rootView, id);
      if (dateTimeLayout == null) {
        break missingId;
      }

      id = R.id.ivRecurring;
      ImageView ivRecurring = ViewBindings.findChildViewById(rootView, id);
      if (ivRecurring == null) {
        break missingId;
      }

      id = R.id.ivTransactionType;
      ImageView ivTransactionType = ViewBindings.findChildViewById(rootView, id);
      if (ivTransactionType == null) {
        break missingId;
      }

      id = R.id.locationLayout;
      LinearLayout locationLayout = ViewBindings.findChildViewById(rootView, id);
      if (locationLayout == null) {
        break missingId;
      }

      id = R.id.statusIndicator;
      View statusIndicator = ViewBindings.findChildViewById(rootView, id);
      if (statusIndicator == null) {
        break missingId;
      }

      id = R.id.tvAmount;
      TextView tvAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvAmount == null) {
        break missingId;
      }

      id = R.id.tvCategory;
      TextView tvCategory = ViewBindings.findChildViewById(rootView, id);
      if (tvCategory == null) {
        break missingId;
      }

      id = R.id.tvDate;
      TextView tvDate = ViewBindings.findChildViewById(rootView, id);
      if (tvDate == null) {
        break missingId;
      }

      id = R.id.tvDescription;
      TextView tvDescription = ViewBindings.findChildViewById(rootView, id);
      if (tvDescription == null) {
        break missingId;
      }

      id = R.id.tvLocation;
      TextView tvLocation = ViewBindings.findChildViewById(rootView, id);
      if (tvLocation == null) {
        break missingId;
      }

      id = R.id.tvSubcategory;
      TextView tvSubcategory = ViewBindings.findChildViewById(rootView, id);
      if (tvSubcategory == null) {
        break missingId;
      }

      id = R.id.tvTime;
      TextView tvTime = ViewBindings.findChildViewById(rootView, id);
      if (tvTime == null) {
        break missingId;
      }

      return new ItemTransactionBinding((MaterialCardView) rootView, categoryLayout, chipGroupTags,
          dateTimeLayout, ivRecurring, ivTransactionType, locationLayout, statusIndicator, tvAmount,
          tvCategory, tvDate, tvDescription, tvLocation, tvSubcategory, tvTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
