// Generated by view binder compiler. Do not edit!
package com.example.ma.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.ma.R;
import de.hdodenhof.circleimageview.CircleImageView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class NavHeaderMainBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CircleImageView ivProfileImage;

  @NonNull
  public final TextView tvHeaderUserEmail;

  @NonNull
  public final TextView tvHeaderUserName;

  private NavHeaderMainBinding(@NonNull LinearLayout rootView,
      @NonNull CircleImageView ivProfileImage, @NonNull TextView tvHeaderUserEmail,
      @NonNull TextView tvHeaderUserName) {
    this.rootView = rootView;
    this.ivProfileImage = ivProfileImage;
    this.tvHeaderUserEmail = tvHeaderUserEmail;
    this.tvHeaderUserName = tvHeaderUserName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static NavHeaderMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static NavHeaderMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.nav_header_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static NavHeaderMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ivProfileImage;
      CircleImageView ivProfileImage = ViewBindings.findChildViewById(rootView, id);
      if (ivProfileImage == null) {
        break missingId;
      }

      id = R.id.tvHeaderUserEmail;
      TextView tvHeaderUserEmail = ViewBindings.findChildViewById(rootView, id);
      if (tvHeaderUserEmail == null) {
        break missingId;
      }

      id = R.id.tvHeaderUserName;
      TextView tvHeaderUserName = ViewBindings.findChildViewById(rootView, id);
      if (tvHeaderUserName == null) {
        break missingId;
      }

      return new NavHeaderMainBinding((LinearLayout) rootView, ivProfileImage, tvHeaderUserEmail,
          tvHeaderUserName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
