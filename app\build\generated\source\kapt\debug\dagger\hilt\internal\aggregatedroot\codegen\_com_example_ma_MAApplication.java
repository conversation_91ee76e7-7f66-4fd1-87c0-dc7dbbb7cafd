package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.ma.MAApplication",
    rootPackage = "com.example.ma",
    originatingRoot = "com.example.ma.MAApplication",
    originatingRootPackage = "com.example.ma",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "MAApplication",
    originatingRootSimpleNames = "MAApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_example_ma_MAApplication {
}
