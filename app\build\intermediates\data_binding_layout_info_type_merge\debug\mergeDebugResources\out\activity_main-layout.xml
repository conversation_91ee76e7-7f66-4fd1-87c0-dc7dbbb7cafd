<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.example.ma" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.drawerlayout.widget.DrawerLayout" rootNodeViewId="@+id/drawer_layout"><Targets><Target id="@+id/drawer_layout" tag="layout/activity_main_0" view="androidx.drawerlayout.widget.DrawerLayout"><Expressions/><location startLine="1" startOffset="0" endLine="51" endOffset="43"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="20" startOffset="12" endLine="26" endOffset="63"/></Target><Target id="@+id/nav_host_fragment" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="30" startOffset="8" endLine="37" endOffset="50"/></Target><Target id="@+id/nav_view" view="com.google.android.material.navigation.NavigationView"><Expressions/><location startLine="42" startOffset="4" endLine="49" endOffset="47"/></Target></Targets></Layout>