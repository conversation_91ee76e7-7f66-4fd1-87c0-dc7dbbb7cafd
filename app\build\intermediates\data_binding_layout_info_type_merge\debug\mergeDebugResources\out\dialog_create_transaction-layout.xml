<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_create_transaction" modulePackage="com.example.ma" filePath="app\src\main\res\layout\dialog_create_transaction.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_create_transaction_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="104" endOffset="14"/></Target><Target id="@+id/rgTransactionType" view="RadioGroup"><Expressions/><location startLine="14" startOffset="4" endLine="49" endOffset="16"/></Target><Target id="@+id/rbSale" view="RadioButton"><Expressions/><location startLine="21" startOffset="8" endLine="26" endOffset="32"/></Target><Target id="@+id/rbExpense" view="RadioButton"><Expressions/><location startLine="28" startOffset="8" endLine="33" endOffset="33"/></Target><Target id="@+id/rbCapital" view="RadioButton"><Expressions/><location startLine="35" startOffset="8" endLine="40" endOffset="33"/></Target><Target id="@+id/rbWithdrawal" view="RadioButton"><Expressions/><location startLine="42" startOffset="8" endLine="47" endOffset="34"/></Target><Target id="@+id/etAmount" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="57" startOffset="8" endLine="61" endOffset="46"/></Target><Target id="@+id/etDescription" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="72" startOffset="8" endLine="76" endOffset="37"/></Target><Target id="@+id/btnCancel" view="Button"><Expressions/><location startLine="86" startOffset="8" endLine="93" endOffset="43"/></Target><Target id="@+id/btnSave" view="Button"><Expressions/><location startLine="95" startOffset="8" endLine="100" endOffset="33"/></Target></Targets></Layout>