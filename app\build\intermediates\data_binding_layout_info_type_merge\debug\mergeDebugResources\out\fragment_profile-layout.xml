<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_profile" modulePackage="com.example.ma" filePath="app\src\main\res\layout\fragment_profile.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_profile_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="295" endOffset="12"/></Target><Target id="@+id/ivProfileImage" view="de.hdodenhof.circleimageview.CircleImageView"><Expressions/><location startLine="23" startOffset="12" endLine="33" endOffset="69"/></Target><Target id="@+id/tvUserName" view="TextView"><Expressions/><location startLine="36" startOffset="12" endLine="44" endOffset="51"/></Target><Target id="@+id/tvUserRole" view="TextView"><Expressions/><location startLine="46" startOffset="12" endLine="52" endOffset="65"/></Target><Target id="@+id/cardProfileInfo" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="57" startOffset="8" endLine="133" endOffset="59"/></Target><Target id="@+id/tvUserEmail" view="TextView"><Expressions/><location startLine="95" startOffset="20" endLine="102" endOffset="66"/></Target><Target id="@+id/tvUserPhone" view="TextView"><Expressions/><location startLine="120" startOffset="20" endLine="127" endOffset="66"/></Target><Target id="@+id/cardFinancialSummary" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="136" startOffset="8" endLine="249" endOffset="59"/></Target><Target id="@+id/tvTotalSales" view="TextView"><Expressions/><location startLine="181" startOffset="24" endLine="188" endOffset="70"/></Target><Target id="@+id/tvTotalExpenses" view="TextView"><Expressions/><location startLine="208" startOffset="24" endLine="215" endOffset="68"/></Target><Target id="@+id/tvMyShare" view="TextView"><Expressions/><location startLine="234" startOffset="24" endLine="241" endOffset="70"/></Target><Target id="@+id/btnEditProfile" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="257" startOffset="12" endLine="266" endOffset="51"/></Target><Target id="@+id/btnChangePassword" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="268" startOffset="12" endLine="278" endOffset="51"/></Target><Target id="@+id/btnChangePhoto" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="280" startOffset="12" endLine="289" endOffset="40"/></Target></Targets></Layout>