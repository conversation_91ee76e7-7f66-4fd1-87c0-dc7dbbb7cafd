<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_reports" modulePackage="com.example.ma" filePath="app\src\main\res\layout\fragment_reports.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_reports_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="167" endOffset="12"/></Target><Target id="@+id/tvTotalSales" view="TextView"><Expressions/><location startLine="48" startOffset="16" endLine="55" endOffset="55"/></Target><Target id="@+id/tvTotalExpenses" view="TextView"><Expressions/><location startLine="57" startOffset="16" endLine="64" endOffset="55"/></Target><Target id="@+id/tvNetProfit" view="TextView"><Expressions/><location startLine="66" startOffset="16" endLine="73" endOffset="55"/></Target><Target id="@+id/tvMyShare" view="TextView"><Expressions/><location startLine="75" startOffset="16" endLine="82" endOffset="55"/></Target><Target id="@+id/tvPartnerShare" view="TextView"><Expressions/><location startLine="84" startOffset="16" endLine="91" endOffset="55"/></Target><Target id="@+id/tvInventoryCount" view="TextView"><Expressions/><location startLine="93" startOffset="16" endLine="99" endOffset="53"/></Target><Target id="@+id/cardDailyReport" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="106" startOffset="8" endLine="112" endOffset="40"/></Target><Target id="@+id/cardMonthlyReport" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="114" startOffset="8" endLine="120" endOffset="40"/></Target><Target id="@+id/cardPartnerComparison" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="122" startOffset="8" endLine="128" endOffset="40"/></Target><Target id="@+id/cardCategoryAnalysis" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="130" startOffset="8" endLine="136" endOffset="40"/></Target><Target id="@+id/btnExportReport" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="145" startOffset="12" endLine="152" endOffset="48"/></Target><Target id="@+id/btnShareReport" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="154" startOffset="12" endLine="161" endOffset="50"/></Target></Targets></Layout>