<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_notification" modulePackage="com.example.ma" filePath="app\src\main\res\layout\item_notification.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView" rootNodeViewId="@+id/cardNotification"><Targets><Target id="@+id/cardNotification" tag="layout/item_notification_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="127" endOffset="51"/></Target><Target id="@+id/ivSenderProfile" view="de.hdodenhof.circleimageview.CircleImageView"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="50"/></Target><Target id="@+id/tvSenderName" view="TextView"><Expressions/><location startLine="41" startOffset="16" endLine="49" endOffset="55"/></Target><Target id="@+id/tvNotificationTime" view="TextView"><Expressions/><location startLine="51" startOffset="16" endLine="57" endOffset="69"/></Target><Target id="@+id/tvNotificationTitle" view="TextView"><Expressions/><location startLine="61" startOffset="12" endLine="69" endOffset="51"/></Target><Target id="@+id/tvNotificationMessage" view="TextView"><Expressions/><location startLine="71" startOffset="12" endLine="78" endOffset="51"/></Target><Target id="@+id/chipNotificationType" view="com.google.android.material.chip.Chip"><Expressions/><location startLine="81" startOffset="12" endLine="87" endOffset="61"/></Target><Target id="@+id/btnReject" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="97" startOffset="16" endLine="103" endOffset="47"/></Target><Target id="@+id/btnApprove" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="105" startOffset="16" endLine="112" endOffset="47"/></Target><Target id="@+id/indicatorUnread" view="View"><Expressions/><location startLine="118" startOffset="8" endLine="123" endOffset="64"/></Target></Targets></Layout>