<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_transaction" modulePackage="com.example.ma" filePath="app\src\main\res\layout\item_transaction.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_transaction_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="218" endOffset="51"/></Target><Target id="@+id/statusIndicator" view="View"><Expressions/><location startLine="18" startOffset="8" endLine="25" endOffset="59"/></Target><Target id="@+id/ivTransactionType" view="ImageView"><Expressions/><location startLine="28" startOffset="8" endLine="38" endOffset="55"/></Target><Target id="@+id/ivRecurring" view="ImageView"><Expressions/><location startLine="41" startOffset="8" endLine="50" endOffset="70"/></Target><Target id="@+id/tvAmount" view="TextView"><Expressions/><location startLine="53" startOffset="8" endLine="65" endOffset="70"/></Target><Target id="@+id/tvDescription" view="TextView"><Expressions/><location startLine="68" startOffset="8" endLine="82" endOffset="73"/></Target><Target id="@+id/categoryLayout" view="LinearLayout"><Expressions/><location startLine="85" startOffset="8" endLine="119" endOffset="22"/></Target><Target id="@+id/tvCategory" view="TextView"><Expressions/><location startLine="96" startOffset="12" endLine="105" endOffset="35"/></Target><Target id="@+id/tvSubcategory" view="TextView"><Expressions/><location startLine="107" startOffset="12" endLine="117" endOffset="40"/></Target><Target id="@+id/dateTimeLayout" view="LinearLayout"><Expressions/><location startLine="122" startOffset="8" endLine="166" endOffset="22"/></Target><Target id="@+id/tvDate" view="TextView"><Expressions/><location startLine="141" startOffset="12" endLine="147" endOffset="43"/></Target><Target id="@+id/tvTime" view="TextView"><Expressions/><location startLine="158" startOffset="12" endLine="164" endOffset="36"/></Target><Target id="@+id/locationLayout" view="LinearLayout"><Expressions/><location startLine="169" startOffset="8" endLine="198" endOffset="22"/></Target><Target id="@+id/tvLocation" view="TextView"><Expressions/><location startLine="190" startOffset="12" endLine="196" endOffset="50"/></Target><Target id="@+id/chipGroupTags" view="com.google.android.material.chip.ChipGroup"><Expressions/><location startLine="201" startOffset="8" endLine="214" endOffset="40"/></Target></Targets></Layout>