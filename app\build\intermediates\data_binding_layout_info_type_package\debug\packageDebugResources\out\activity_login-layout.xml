<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.example.ma" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_login_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="107" endOffset="12"/></Target><Target id="@+id/etUsername" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="44" startOffset="12" endLine="50" endOffset="45"/></Target><Target id="@+id/etPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="65" startOffset="12" endLine="71" endOffset="45"/></Target><Target id="@+id/tvError" view="TextView"><Expressions/><location startLine="76" startOffset="8" endLine="84" endOffset="39"/></Target><Target id="@+id/btnLogin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="87" startOffset="8" endLine="93" endOffset="36"/></Target></Targets></Layout>