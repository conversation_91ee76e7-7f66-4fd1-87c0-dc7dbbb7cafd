<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_profile_image" modulePackage="com.example.ma" filePath="app\src\main\res\layout\activity_profile_image.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_profile_image_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="115" endOffset="14"/></Target><Target id="@+id/ivProfileImage" view="de.hdodenhof.circleimageview.CircleImageView"><Expressions/><location startLine="27" startOffset="8" endLine="35" endOffset="54"/></Target><Target id="@+id/fabCamera" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="38" startOffset="8" endLine="47" endOffset="48"/></Target><Target id="@+id/btnSelectImage" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="64" startOffset="12" endLine="72" endOffset="48"/></Target><Target id="@+id/btnTakePhoto" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="74" startOffset="12" endLine="82" endOffset="50"/></Target><Target id="@+id/btnCancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="91" startOffset="12" endLine="99" endOffset="48"/></Target><Target id="@+id/btnSave" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="101" startOffset="12" endLine="109" endOffset="50"/></Target></Targets></Layout>