<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_dashboard" modulePackage="com.example.ma" filePath="app\src\main\res\layout\fragment_dashboard.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_dashboard_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="295" endOffset="12"/></Target><Target id="@+id/tvTotalSales" view="TextView"><Expressions/><location startLine="66" startOffset="24" endLine="73" endOffset="70"/></Target><Target id="@+id/tvTotalExpenses" view="TextView"><Expressions/><location startLine="116" startOffset="24" endLine="123" endOffset="68"/></Target><Target id="@+id/tvNetProfit" view="TextView"><Expressions/><location startLine="166" startOffset="24" endLine="173" endOffset="70"/></Target><Target id="@+id/tvMyShare" view="TextView"><Expressions/><location startLine="213" startOffset="20" endLine="220" endOffset="66"/></Target><Target id="@+id/tvPartnerShare" view="TextView"><Expressions/><location startLine="249" startOffset="20" endLine="256" endOffset="63"/></Target><Target id="@+id/btnRegisterTransaction" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="270" startOffset="12" endLine="278" endOffset="40"/></Target><Target id="@+id/btnReports" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="280" startOffset="12" endLine="289" endOffset="40"/></Target></Targets></Layout>