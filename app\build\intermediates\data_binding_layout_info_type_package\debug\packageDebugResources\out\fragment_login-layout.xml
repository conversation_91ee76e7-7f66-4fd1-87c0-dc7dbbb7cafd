<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_login" modulePackage="com.example.ma" filePath="app\src\main\res\layout\fragment_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_login_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="266" endOffset="12"/></Target><Target id="@+id/headerLayout" view="LinearLayout"><Expressions/><location startLine="16" startOffset="8" endLine="56" endOffset="22"/></Target><Target id="@+id/ivLogo" view="ImageView"><Expressions/><location startLine="28" startOffset="12" endLine="35" endOffset="63"/></Target><Target id="@+id/loginCard" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="59" startOffset="8" endLine="221" endOffset="59"/></Target><Target id="@+id/tilEmail" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="76" startOffset="16" endLine="94" endOffset="71"/></Target><Target id="@+id/etEmail" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="86" startOffset="20" endLine="92" endOffset="61"/></Target><Target id="@+id/tilPassword" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="97" startOffset="16" endLine="117" endOffset="71"/></Target><Target id="@+id/etPassword" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="109" startOffset="20" endLine="115" endOffset="64"/></Target><Target id="@+id/cbRememberMe" view="com.google.android.material.checkbox.MaterialCheckBox"><Expressions/><location startLine="127" startOffset="20" endLine="133" endOffset="78"/></Target><Target id="@+id/btnForgotPassword" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="135" startOffset="20" endLine="141" endOffset="69"/></Target><Target id="@+id/btnLogin" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="146" startOffset="16" endLine="153" endOffset="45"/></Target><Target id="@+id/btnGoogleSignIn" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="192" startOffset="20" endLine="202" endOffset="48"/></Target><Target id="@+id/btnAppleSignIn" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="204" startOffset="20" endLine="215" endOffset="48"/></Target><Target id="@+id/btnSignUp" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="241" startOffset="12" endLine="248" endOffset="61"/></Target><Target id="@+id/progressIndicator" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="253" startOffset="8" endLine="262" endOffset="55"/></Target></Targets></Layout>