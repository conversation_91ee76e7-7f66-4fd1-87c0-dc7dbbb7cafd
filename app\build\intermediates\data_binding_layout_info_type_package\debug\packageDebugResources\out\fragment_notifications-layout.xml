<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_notifications" modulePackage="com.example.ma" filePath="app\src\main\res\layout\fragment_notifications.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_notifications_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="60" endOffset="14"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="14" startOffset="8" endLine="20" endOffset="47"/></Target><Target id="@+id/recyclerViewNotifications" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="25" startOffset="4" endLine="31" endOffset="39"/></Target><Target id="@+id/emptyStateLayout" view="LinearLayout"><Expressions/><location startLine="34" startOffset="4" endLine="58" endOffset="18"/></Target></Targets></Layout>