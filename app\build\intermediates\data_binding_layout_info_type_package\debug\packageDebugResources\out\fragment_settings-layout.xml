<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_settings" modulePackage="com.example.ma" filePath="app\src\main\res\layout\fragment_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_settings_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="188" endOffset="12"/></Target><Target id="@+id/cardAppearance" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="13" startOffset="8" endLine="41" endOffset="59"/></Target><Target id="@+id/tvCurrentTheme" view="com.google.android.material.textview.MaterialTextView"><Expressions/><location startLine="27" startOffset="16" endLine="31" endOffset="53"/></Target><Target id="@+id/btnChangeTheme" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="33" startOffset="16" endLine="38" endOffset="52"/></Target><Target id="@+id/cardNotifications" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="44" startOffset="8" endLine="77" endOffset="59"/></Target><Target id="@+id/switchPushNotifications" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="58" startOffset="16" endLine="62" endOffset="50"/></Target><Target id="@+id/switchSoundVibration" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="64" startOffset="16" endLine="68" endOffset="47"/></Target><Target id="@+id/switchTransactionAlerts" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="70" startOffset="16" endLine="74" endOffset="52"/></Target><Target id="@+id/cardSecurity" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="80" startOffset="8" endLine="113" endOffset="59"/></Target><Target id="@+id/btnChangePassword" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="94" startOffset="16" endLine="98" endOffset="51"/></Target><Target id="@+id/switchBiometric" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="100" startOffset="16" endLine="104" endOffset="56"/></Target><Target id="@+id/switchAutoLock" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="106" startOffset="16" endLine="110" endOffset="47"/></Target><Target id="@+id/cardDataSync" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="116" startOffset="8" endLine="149" endOffset="59"/></Target><Target id="@+id/switchAutoSync" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="130" startOffset="16" endLine="134" endOffset="54"/></Target><Target id="@+id/btnBackupData" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="136" startOffset="16" endLine="140" endOffset="49"/></Target><Target id="@+id/btnClearCache" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="142" startOffset="16" endLine="146" endOffset="48"/></Target><Target id="@+id/cardAbout" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="152" startOffset="8" endLine="184" endOffset="59"/></Target><Target id="@+id/tvAppVersion" view="com.google.android.material.textview.MaterialTextView"><Expressions/><location startLine="165" startOffset="16" endLine="169" endOffset="48"/></Target><Target id="@+id/btnContactSupport" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="171" startOffset="16" endLine="175" endOffset="55"/></Target><Target id="@+id/btnLogout" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="177" startOffset="16" endLine="181" endOffset="41"/></Target></Targets></Layout>