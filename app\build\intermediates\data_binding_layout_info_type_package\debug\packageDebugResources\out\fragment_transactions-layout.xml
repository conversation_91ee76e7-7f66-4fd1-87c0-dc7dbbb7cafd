<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_transactions" modulePackage="com.example.ma" filePath="app\src\main\res\layout\fragment_transactions.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/fragment_transactions_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="92" endOffset="53"/></Target><Target id="@+id/btnFilter" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="31" startOffset="12" endLine="39" endOffset="37"/></Target><Target id="@+id/recyclerViewTransactions" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="44" startOffset="8" endLine="50" endOffset="43"/></Target><Target id="@+id/emptyStateLayout" view="LinearLayout"><Expressions/><location startLine="53" startOffset="8" endLine="78" endOffset="22"/></Target><Target id="@+id/tvEmptyState" view="TextView"><Expressions/><location startLine="69" startOffset="12" endLine="76" endOffset="42"/></Target><Target id="@+id/fabAddTransaction" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="83" startOffset="4" endLine="90" endOffset="44"/></Target></Targets></Layout>