<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="nav_header_main" modulePackage="com.example.ma" filePath="app\src\main\res\layout\nav_header_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/nav_header_main_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="38" endOffset="14"/></Target><Target id="@+id/ivProfileImage" view="de.hdodenhof.circleimageview.CircleImageView"><Expressions/><location startLine="11" startOffset="4" endLine="18" endOffset="36"/></Target><Target id="@+id/tvHeaderUserName" view="TextView"><Expressions/><location startLine="20" startOffset="4" endLine="27" endOffset="53"/></Target><Target id="@+id/tvHeaderUserEmail" view="TextView"><Expressions/><location startLine="29" startOffset="4" endLine="36" endOffset="29"/></Target></Targets></Layout>