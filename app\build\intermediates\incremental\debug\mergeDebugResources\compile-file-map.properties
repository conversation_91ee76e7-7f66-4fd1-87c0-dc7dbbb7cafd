#Thu Aug 21 16:19:13 PDT 2025
com.example.ma.app-main-57\:/drawable/bg_unread_indicator.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_bg_unread_indicator.xml.flat
com.example.ma.app-main-57\:/drawable/category_background.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_category_background.xml.flat
com.example.ma.app-main-57\:/drawable/circle_background.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background.xml.flat
com.example.ma.app-main-57\:/drawable/circle_background_primary.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background_primary.xml.flat
com.example.ma.app-main-57\:/drawable/circle_background_white.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_circle_background_white.xml.flat
com.example.ma.app-main-57\:/drawable/gradient_primary.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_gradient_primary.xml.flat
com.example.ma.app-main-57\:/drawable/ic_add.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_add.xml.flat
com.example.ma.app-main-57\:/drawable/ic_apple.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_apple.xml.flat
com.example.ma.app-main-57\:/drawable/ic_calendar.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_calendar.xml.flat
com.example.ma.app-main-57\:/drawable/ic_camera.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_camera.xml.flat
com.example.ma.app-main-57\:/drawable/ic_clear_all.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_clear_all.xml.flat
com.example.ma.app-main-57\:/drawable/ic_dashboard.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_dashboard.xml.flat
com.example.ma.app-main-57\:/drawable/ic_edit.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_edit.xml.flat
com.example.ma.app-main-57\:/drawable/ic_email.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_email.xml.flat
com.example.ma.app-main-57\:/drawable/ic_empty_state.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_empty_state.xml.flat
com.example.ma.app-main-57\:/drawable/ic_expense.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_expense.xml.flat
com.example.ma.app-main-57\:/drawable/ic_export.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_export.xml.flat
com.example.ma.app-main-57\:/drawable/ic_filter.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_filter.xml.flat
com.example.ma.app-main-57\:/drawable/ic_google.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_google.xml.flat
com.example.ma.app-main-57\:/drawable/ic_image.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_image.xml.flat
com.example.ma.app-main-57\:/drawable/ic_income.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_income.xml.flat
com.example.ma.app-main-57\:/drawable/ic_launcher.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher.xml.flat
com.example.ma.app-main-57\:/drawable/ic_launcher_foreground.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.ma.app-main-57\:/drawable/ic_launcher_round.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_round.xml.flat
com.example.ma.app-main-57\:/drawable/ic_location.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_location.xml.flat
com.example.ma.app-main-57\:/drawable/ic_lock.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_lock.xml.flat
com.example.ma.app-main-57\:/drawable/ic_logo.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_logo.xml.flat
com.example.ma.app-main-57\:/drawable/ic_logout.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_logout.xml.flat
com.example.ma.app-main-57\:/drawable/ic_notifications.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_notifications.xml.flat
com.example.ma.app-main-57\:/drawable/ic_person.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_person.xml.flat
com.example.ma.app-main-57\:/drawable/ic_phone.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_phone.xml.flat
com.example.ma.app-main-57\:/drawable/ic_profit.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_profit.xml.flat
com.example.ma.app-main-57\:/drawable/ic_recurring.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_recurring.xml.flat
com.example.ma.app-main-57\:/drawable/ic_reports.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_reports.xml.flat
com.example.ma.app-main-57\:/drawable/ic_sales.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_sales.xml.flat
com.example.ma.app-main-57\:/drawable/ic_share.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_share.xml.flat
com.example.ma.app-main-57\:/drawable/ic_time.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_time.xml.flat
com.example.ma.app-main-57\:/drawable/ic_transactions.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_transactions.xml.flat
com.example.ma.app-main-57\:/drawable/ic_transfer.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_transfer.xml.flat
com.example.ma.app-main-57\:/drawable/profile_placeholder.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_profile_placeholder.xml.flat
com.example.ma.app-main-57\:/drawable/status_background.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_status_background.xml.flat
com.example.ma.app-main-57\:/drawable/subcategory_background.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_subcategory_background.xml.flat
com.example.ma.app-main-57\:/menu/activity_main_drawer.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\menu_activity_main_drawer.xml.flat
com.example.ma.app-main-57\:/navigation/nav_graph.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\navigation_nav_graph.xml.flat
com.example.ma.app-main-57\:/xml/backup_rules.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.ma.app-main-57\:/xml/data_extraction_rules.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.ma.app-main-57\:/xml/file_paths.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_file_paths.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/activity_login.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_login.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/activity_main.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_main.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/activity_profile_image.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_profile_image.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/dialog_create_transaction.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_create_transaction.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/fragment_dashboard.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_dashboard.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/fragment_login.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_login.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/fragment_notifications.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_notifications.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/fragment_profile.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_profile.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/fragment_reports.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_reports.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/fragment_settings.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_settings.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/fragment_transactions.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_fragment_transactions.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/item_notification.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_notification.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/item_transaction.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_transaction.xml.flat
com.example.ma.app-mergeDebugResources-54\:/layout/nav_header_main.xml=C\:\\Users\\alika\\AndroidStudioProjects\\MA\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_nav_header_main.xml.flat
