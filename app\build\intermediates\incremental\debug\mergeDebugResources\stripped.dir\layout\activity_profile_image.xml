<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="?android:colorBackground"
    android:padding="24dp">

    <!-- Header -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="تغییر عکس پروفایل"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="?android:textColorPrimary"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="32dp" />

    <!-- Profile Image Preview - Instagram Style -->
    <FrameLayout
        android:layout_width="240dp"
        android:layout_height="240dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="32dp">

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/ivProfileImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/profile_placeholder"
            android:scaleType="centerCrop"
            app:civ_border_width="4dp"
            app:civ_border_color="?colorPrimary"
            android:contentDescription="عکس پروفایل" />

        <!-- Camera Icon Overlay -->
        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/fabCamera"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_margin="8dp"
            android:src="@drawable/ic_camera"
            app:fabSize="mini"
            app:tint="@color/white"
            app:backgroundTint="?colorPrimary" />

    </FrameLayout>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Instagram Style Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="24dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSelectImage"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="گالری"
                android:textSize="16sp"
                style="@style/Widget.MA.Button"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnTakePhoto"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="دوربین"
                android:textSize="16sp"
                style="@style/Widget.MA.Button.Secondary"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCancel"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="لغو"
                android:textSize="16sp"
                style="@style/Widget.MA.Button.Secondary"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSave"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:text="ذخیره"
                android:textSize="16sp"
                style="@style/Widget.MA.Button"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout> 