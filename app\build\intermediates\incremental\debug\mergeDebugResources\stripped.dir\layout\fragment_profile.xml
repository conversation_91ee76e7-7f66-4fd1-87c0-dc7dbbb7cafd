<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layoutDirection="rtl"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Profile Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="24dp">

            <!-- Profile Image -->
            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/ivProfileImage"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:src="@drawable/ic_person"
                android:layout_marginBottom="16dp"
                app:civ_border_color="@color/color_primary"
                app:civ_border_width="3dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground" />

            <!-- User Info -->
            <TextView
                android:id="@+id/tvUserName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="نام کاربر"
                android:textSize="24sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tvUserRole"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="شریک کسب‌وکار"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurfaceVariant" />

        </LinearLayout>

        <!-- Profile Info Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardProfileInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/personal_info"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="16dp" />

                <!-- Email -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_email"
                        android:tint="?attr/colorOnSurfaceVariant"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:id="@+id/tvUserEmail"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="<EMAIL>"
                        android:textSize="14sp"
                        android:textColor="?attr/colorOnSurface" />

                </LinearLayout>

                <!-- Phone -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_phone"
                        android:tint="?attr/colorOnSurfaceVariant"
                        android:layout_marginEnd="12dp" />

                    <TextView
                        android:id="@+id/tvUserPhone"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="09123456789"
                        android:textSize="14sp"
                        android:textColor="?attr/colorOnSurface" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Financial Summary Card -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardFinancialSummary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/financial_summary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?attr/colorOnSurface"
                    android:layout_marginBottom="16dp" />

                <!-- Financial Stats -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Total Sales -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/total_sales"
                            android:textSize="14sp"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <TextView
                            android:id="@+id/tvTotalSales"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 تومان"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/color_success" />

                    </LinearLayout>

                    <!-- Total Expenses -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/total_expenses"
                            android:textSize="14sp"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <TextView
                            android:id="@+id/tvTotalExpenses"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 تومان"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/color_error" />

                    </LinearLayout>

                    <!-- My Share -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/my_share"
                            android:textSize="14sp"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                        <TextView
                            android:id="@+id/tvMyShare"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 تومان"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/color_success" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnEditProfile"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/edit_profile"
                android:textSize="16sp"
                app:icon="@drawable/ic_edit"
                app:iconGravity="textStart"
                app:cornerRadius="8dp"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnChangePassword"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/change_password"
                android:textSize="16sp"
                app:icon="@drawable/ic_lock"
                app:iconGravity="textStart"
                style="@style/Widget.Material3.Button.OutlinedButton"
                app:cornerRadius="8dp"
                android:layout_marginBottom="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnChangePhoto"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/change_photo"
                android:textSize="16sp"
                app:icon="@drawable/ic_camera"
                app:iconGravity="textStart"
                style="@style/Widget.Material3.Button.OutlinedButton"
                app:cornerRadius="8dp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView> 