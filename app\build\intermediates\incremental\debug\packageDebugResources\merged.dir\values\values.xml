<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="business_partners">
        <item>علی کاکایی</item>
        <item>میلاد نصیری</item>
    </string-array>
    <string-array name="expense_categories">
        <item>مواد اولیه</item>
        <item>حمل و نقل</item>
        <item>اجاره</item>
        <item>تجهیزات</item>
        <item>سایر</item>
    </string-array>
    <string-array name="payment_types">
        <item>نقدی</item>
        <item>کارت به کارت</item>
        <item>چک</item>
    </string-array>
    <color name="black">#000000</color>
    <color name="color_background">@color/md_theme_background</color>
    <color name="color_error">@color/danger_red</color>
    <color name="color_info">@color/info_blue</color>
    <color name="color_on_background">@color/md_theme_onBackground</color>
    <color name="color_on_primary">@color/md_theme_onPrimary</color>
    <color name="color_on_secondary">@color/md_theme_onSecondary</color>
    <color name="color_on_surface">@color/md_theme_onSurface</color>
    <color name="color_on_surface_variant">@color/md_theme_onSurfaceVariant</color>
    <color name="color_primary">@color/md_theme_primary</color>
    <color name="color_primary_dark">@color/md_theme_primary</color>
    <color name="color_primary_variant">@color/md_theme_primaryContainer</color>
    <color name="color_secondary">@color/md_theme_secondary</color>
    <color name="color_secondary_variant">@color/md_theme_secondaryContainer</color>
    <color name="color_success">@color/success_green</color>
    <color name="color_surface">@color/md_theme_surface</color>
    <color name="color_surface_variant">@color/md_theme_surfaceVariant</color>
    <color name="color_tertiary">@color/md_theme_tertiary</color>
    <color name="color_warning">@color/warning_orange</color>
    <color name="danger_red">#F44336</color>
    <color name="divider">@color/md_theme_outlineVariant</color>
    <color name="expense">@color/expense_red</color>
    <color name="expense_red">#F44336</color>
    <color name="gradient_end">#4CAF50</color>
    <color name="gradient_start">#006C4C</color>
    <color name="income_green">#4CAF50</color>
    <color name="info">@color/info_blue</color>
    <color name="info_blue">#2196F3</color>
    <color name="md_theme_background">#FBFDF9</color>
    <color name="md_theme_error">#BA1A1A</color>
    <color name="md_theme_errorContainer">#FFDAD6</color>
    <color name="md_theme_onBackground">#191C1A</color>
    <color name="md_theme_onError">#FFFFFF</color>
    <color name="md_theme_onErrorContainer">#410002</color>
    <color name="md_theme_onPrimary">#FFFFFF</color>
    <color name="md_theme_onPrimaryContainer">#002114</color>
    <color name="md_theme_onSecondary">#FFFFFF</color>
    <color name="md_theme_onSecondaryContainer">#092017</color>
    <color name="md_theme_onSurface">#191C1A</color>
    <color name="md_theme_onSurfaceVariant">#404943</color>
    <color name="md_theme_onTertiary">#FFFFFF</color>
    <color name="md_theme_onTertiaryContainer">#001F29</color>
    <color name="md_theme_outline">#707973</color>
    <color name="md_theme_outlineVariant">#BFC9C2</color>
    <color name="md_theme_primary">#006C4C</color>
    <color name="md_theme_primaryContainer">#89F8C7</color>
    <color name="md_theme_secondary">#4C6358</color>
    <color name="md_theme_secondaryContainer">#CFE9DB</color>
    <color name="md_theme_surface">#FBFDF9</color>
    <color name="md_theme_surfaceVariant">#DCE5DD</color>
    <color name="md_theme_tertiary">#3D6373</color>
    <color name="md_theme_tertiaryContainer">#C1E8FB</color>
    <color name="neutral_gray">#9E9E9E</color>
    <color name="primary">@color/md_theme_primary</color>
    <color name="sale">@color/income_green</color>
    <color name="secondary">@color/md_theme_secondary</color>
    <color name="shadow_dark">#4D000000</color>
    <color name="shadow_light">#1A000000</color>
    <color name="shadow_medium">#33000000</color>
    <color name="success">@color/success_green</color>
    <color name="success_green">#4CAF50</color>
    <color name="transfer_blue">#2196F3</color>
    <color name="warning_orange">#FF9800</color>
    <color name="white">#FFFFFF</color>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="button_corner_radius">8dp</dimen>
    <dimen name="button_height">48dp</dimen>
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="dashboard_card_height">120dp</dimen>
    <dimen name="dashboard_icon_size">48dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="nav_header_vertical_spacing">8dp</dimen>
    <dimen name="spacing_extra_large">32dp</dimen>
    <dimen name="spacing_large">24dp</dimen>
    <dimen name="spacing_medium">16dp</dimen>
    <dimen name="spacing_small">8dp</dimen>
    <dimen name="text_size_extra_large">24sp</dimen>
    <dimen name="text_size_large">20sp</dimen>
    <dimen name="text_size_medium">16sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    <string name="app_logo">لوگوی اپلیکیشن</string>
    <string name="app_name">MA</string>
    <string name="app_version">نسخه 1.0.0</string>
    <string name="back">بازگشت</string>
    <string name="cancel">لغو</string>
    <string name="category_analysis">تحلیل دسته‌بندی</string>
    <string name="change_password">تغییر رمز عبور</string>
    <string name="change_photo">تغییر عکس</string>
    <string name="clear_all">پاک کردن همه</string>
    <string name="confirm">تایید</string>
    <string name="continue_with_apple">ادامه با اپل</string>
    <string name="continue_with_google">ادامه با گوگل</string>
    <string name="daily_report">گزارش روزانه</string>
    <string name="daily_summary">خلاصه روزانه</string>
    <string name="dashboard_title">خلاصه مالی</string>
    <string name="date">تاریخ</string>
    <string name="delete">حذف</string>
    <string name="detailed_report">گزارش تفصیلی</string>
    <string name="dont_have_account">حساب کاربری ندارید؟</string>
    <string name="edit">ویرایش</string>
    <string name="edit_profile">ویرایش پروفایل</string>
    <string name="email">ایمیل</string>
    <string name="error_general">خطا در عملیات</string>
    <string name="error_network">خطا در اتصال به سرور</string>
    <string name="expenses">هزینه‌ها</string>
    <string name="export">صادر کردن</string>
    <string name="export_report">صادر کردن</string>
    <string name="filter">فیلتر</string>
    <string name="financial_summary">خلاصه مالی</string>
    <string name="forgot_password">فراموشی رمز عبور</string>
    <string name="loading">در حال بارگذاری...</string>
    <string name="location">مکان</string>
    <string name="login">ورود</string>
    <string name="login_button">ورود</string>
    <string name="login_subtitle">برای ورود به سیستم، اطلاعات خود را وارد کنید</string>
    <string name="login_title">ورود به سیستم</string>
    <string name="monthly_report">گزارش ماهانه</string>
    <string name="monthly_summary">خلاصه ماهانه</string>
    <string name="my_share">سهم من</string>
    <string name="nav_dashboard">داشبورد</string>
    <string name="nav_logout">خروج</string>
    <string name="nav_notifications">اعلانات</string>
    <string name="nav_profile">پروفایل</string>
    <string name="nav_reports">گزارشات</string>
    <string name="nav_transactions">تراکنش‌ها</string>
    <string name="net_profit">سود خالص</string>
    <string name="no_notifications">اعلانی وجود ندارد</string>
    <string name="no_transactions">تراکنشی وجود ندارد</string>
    <string name="notifications_title">اعلانات</string>
    <string name="or">یا</string>
    <string name="other">سایر</string>
    <string name="partner_comparison">مقایسه شرکا</string>
    <string name="partner_share">سهم شریک</string>
    <string name="password">رمز عبور</string>
    <string name="password_hint">رمز عبور</string>
    <string name="personal_info">اطلاعات شخصی</string>
    <string name="profile_title">پروفایل</string>
    <string name="profit">سود</string>
    <string name="recurring_transaction">تراکنش تکراری</string>
    <string name="register_transaction">ثبت تراکنش</string>
    <string name="remember_me">مرا به خاطر بسپار</string>
    <string name="reports">گزارشات</string>
    <string name="reports_title">گزارشات</string>
    <string name="sales">فروش</string>
    <string name="sample_email"><EMAIL></string>
    <string name="sample_password">رمز عبور خود را وارد کنید</string>
    <string name="save">ذخیره</string>
    <string name="share">اشتراک‌گذاری</string>
    <string name="share_report">اشتراک‌گذاری</string>
    <string name="sign_up">ثبت نام</string>
    <string name="status_approved">تایید شده</string>
    <string name="status_pending">در انتظار</string>
    <string name="status_rejected">رد شده</string>
    <string name="success">عملیات با موفقیت انجام شد</string>
    <string name="total_expenses">کل هزینه‌ها</string>
    <string name="total_sales">کل فروش</string>
    <string name="transaction_expense">هزینه</string>
    <string name="transaction_sale">فروش</string>
    <string name="transaction_type">نوع تراکنش</string>
    <string name="transaction_withdrawal">برداشت</string>
    <string name="username_hint">نام کاربری</string>
    <style name="TextAppearance.MA.BodyLarge" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">16sp</item>
    </style>
    <style name="TextAppearance.MA.BodyMedium" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="TextAppearance.MA.BodySmall" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textSize">12sp</item>
    </style>
    <style name="TextAppearance.MA.Button" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.MA.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textSize">12sp</item>
    </style>
    <style name="TextAppearance.MA.HeadlineLarge" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.MA.HeadlineMedium" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textSize">28sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.MA.TitleLarge" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:textSize">22sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.MA.TitleMedium" parent="TextAppearance.Material3.TitleMedium">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.Material3.Button" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.1</item>
    </style>
    <style name="Theme.MA" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_primary</item>
        <item name="colorOnPrimary">@color/md_theme_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_onPrimaryContainer</item>
        
        
        <item name="colorSecondary">@color/md_theme_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_onSecondaryContainer</item>
        
        
        <item name="colorTertiary">@color/md_theme_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_onTertiaryContainer</item>
        
        
        <item name="colorError">@color/md_theme_error</item>
        <item name="colorOnError">@color/md_theme_onError</item>
        <item name="colorErrorContainer">@color/md_theme_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_onErrorContainer</item>
        
        
        <item name="colorSurface">@color/md_theme_surface</item>
        <item name="colorOnSurface">@color/md_theme_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_outlineVariant</item>
        
        
        <item name="android:colorBackground">@color/md_theme_background</item>
        <item name="colorOnBackground">@color/md_theme_onBackground</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        
        <item name="elevationOverlayEnabled">false</item>
        <item name="android:windowSplashScreenBackground">@color/md_theme_primary</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/ic_logo</item>
    </style>
    <style name="Theme.MA.AppBarOverlay" parent="ThemeOverlay.Material3.Dark.ActionBar"/>
    <style name="Theme.MA.PopupOverlay" parent="ThemeOverlay.Material3.Light"/>
    <style name="Widget.MA.Button" parent="Widget.Material3.Button">
        <item name="android:textAppearance">@style/TextAppearance.MA.Button</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:minHeight">56dp</item>
    </style>
    <style name="Widget.MA.Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textAppearance">@style/TextAppearance.MA.Button</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:minHeight">56dp</item>
    </style>
    <style name="Widget.MA.Card" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">8dp</item>
        <item name="contentPadding">16dp</item>
    </style>
    <style name="Widget.MA.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusTopStart">16dp</item>
        <item name="boxCornerRadiusTopEnd">16dp</item>
        <item name="boxCornerRadiusBottomStart">16dp</item>
        <item name="boxCornerRadiusBottomEnd">16dp</item>
    </style>
    <style name="Widget.Material3.Button.FilledTonalButton" parent="Widget.Material3.Button"/>
</resources>