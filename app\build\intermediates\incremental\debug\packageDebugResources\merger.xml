<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res"><file name="bg_unread_indicator" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\bg_unread_indicator.xml" qualifiers="" type="drawable"/><file name="category_background" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\category_background.xml" qualifiers="" type="drawable"/><file name="circle_background" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="circle_background_primary" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\circle_background_primary.xml" qualifiers="" type="drawable"/><file name="circle_background_white" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\circle_background_white.xml" qualifiers="" type="drawable"/><file name="gradient_primary" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\gradient_primary.xml" qualifiers="" type="drawable"/><file name="ic_add" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_apple" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_apple.xml" qualifiers="" type="drawable"/><file name="ic_calendar" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_camera" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_camera.xml" qualifiers="" type="drawable"/><file name="ic_clear_all" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_clear_all.xml" qualifiers="" type="drawable"/><file name="ic_dashboard" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_dashboard.xml" qualifiers="" type="drawable"/><file name="ic_edit" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_edit.xml" qualifiers="" type="drawable"/><file name="ic_email" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_email.xml" qualifiers="" type="drawable"/><file name="ic_empty_state" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_empty_state.xml" qualifiers="" type="drawable"/><file name="ic_expense" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_expense.xml" qualifiers="" type="drawable"/><file name="ic_export" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_export.xml" qualifiers="" type="drawable"/><file name="ic_filter" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_filter.xml" qualifiers="" type="drawable"/><file name="ic_google" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_google.xml" qualifiers="" type="drawable"/><file name="ic_image" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_image.xml" qualifiers="" type="drawable"/><file name="ic_income" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_income.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_launcher.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher_round" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_launcher_round.xml" qualifiers="" type="drawable"/><file name="ic_location" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_location.xml" qualifiers="" type="drawable"/><file name="ic_lock" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_lock.xml" qualifiers="" type="drawable"/><file name="ic_logo" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_logo.xml" qualifiers="" type="drawable"/><file name="ic_logout" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_logout.xml" qualifiers="" type="drawable"/><file name="ic_notifications" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_notifications.xml" qualifiers="" type="drawable"/><file name="ic_person" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_phone" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_phone.xml" qualifiers="" type="drawable"/><file name="ic_profit" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_profit.xml" qualifiers="" type="drawable"/><file name="ic_recurring" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_recurring.xml" qualifiers="" type="drawable"/><file name="ic_reports" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_reports.xml" qualifiers="" type="drawable"/><file name="ic_sales" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_sales.xml" qualifiers="" type="drawable"/><file name="ic_share" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_share.xml" qualifiers="" type="drawable"/><file name="ic_time" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_time.xml" qualifiers="" type="drawable"/><file name="ic_transactions" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_transactions.xml" qualifiers="" type="drawable"/><file name="ic_transfer" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\ic_transfer.xml" qualifiers="" type="drawable"/><file name="profile_placeholder" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\profile_placeholder.xml" qualifiers="" type="drawable"/><file name="status_background" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\status_background.xml" qualifiers="" type="drawable"/><file name="subcategory_background" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\drawable\subcategory_background.xml" qualifiers="" type="drawable"/><file name="activity_login" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_profile_image" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\activity_profile_image.xml" qualifiers="" type="layout"/><file name="dialog_create_transaction" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\dialog_create_transaction.xml" qualifiers="" type="layout"/><file name="fragment_dashboard" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\fragment_dashboard.xml" qualifiers="" type="layout"/><file name="fragment_login" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\fragment_login.xml" qualifiers="" type="layout"/><file name="fragment_notifications" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\fragment_notifications.xml" qualifiers="" type="layout"/><file name="fragment_profile" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\fragment_profile.xml" qualifiers="" type="layout"/><file name="fragment_reports" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\fragment_reports.xml" qualifiers="" type="layout"/><file name="fragment_settings" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\fragment_settings.xml" qualifiers="" type="layout"/><file name="fragment_transactions" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\fragment_transactions.xml" qualifiers="" type="layout"/><file name="item_notification" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\item_notification.xml" qualifiers="" type="layout"/><file name="item_transaction" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\item_transaction.xml" qualifiers="" type="layout"/><file name="nav_header_main" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\layout\nav_header_main.xml" qualifiers="" type="layout"/><file name="activity_main_drawer" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\menu\activity_main_drawer.xml" qualifiers="" type="menu"/><file name="nav_graph" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/><file path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\values\colors.xml" qualifiers=""><color name="md_theme_primary">#006C4C</color><color name="md_theme_onPrimary">#FFFFFF</color><color name="md_theme_primaryContainer">#89F8C7</color><color name="md_theme_onPrimaryContainer">#002114</color><color name="md_theme_secondary">#4C6358</color><color name="md_theme_onSecondary">#FFFFFF</color><color name="md_theme_secondaryContainer">#CFE9DB</color><color name="md_theme_onSecondaryContainer">#092017</color><color name="md_theme_tertiary">#3D6373</color><color name="md_theme_onTertiary">#FFFFFF</color><color name="md_theme_tertiaryContainer">#C1E8FB</color><color name="md_theme_onTertiaryContainer">#001F29</color><color name="md_theme_error">#BA1A1A</color><color name="md_theme_onError">#FFFFFF</color><color name="md_theme_errorContainer">#FFDAD6</color><color name="md_theme_onErrorContainer">#410002</color><color name="md_theme_surface">#FBFDF9</color><color name="md_theme_onSurface">#191C1A</color><color name="md_theme_surfaceVariant">#DCE5DD</color><color name="md_theme_onSurfaceVariant">#404943</color><color name="md_theme_outline">#707973</color><color name="md_theme_outlineVariant">#BFC9C2</color><color name="md_theme_background">#FBFDF9</color><color name="md_theme_onBackground">#191C1A</color><color name="success_green">#4CAF50</color><color name="warning_orange">#FF9800</color><color name="info_blue">#2196F3</color><color name="danger_red">#F44336</color><color name="income_green">#4CAF50</color><color name="expense_red">#F44336</color><color name="transfer_blue">#2196F3</color><color name="neutral_gray">#9E9E9E</color><color name="gradient_start">#006C4C</color><color name="gradient_end">#4CAF50</color><color name="shadow_light">#1A000000</color><color name="shadow_medium">#33000000</color><color name="shadow_dark">#4D000000</color><color name="color_primary">@color/md_theme_primary</color><color name="color_primary_dark">@color/md_theme_primary</color><color name="color_primary_variant">@color/md_theme_primaryContainer</color><color name="color_on_primary">@color/md_theme_onPrimary</color><color name="color_secondary">@color/md_theme_secondary</color><color name="color_secondary_variant">@color/md_theme_secondaryContainer</color><color name="color_on_secondary">@color/md_theme_onSecondary</color><color name="color_background">@color/md_theme_background</color><color name="color_surface">@color/md_theme_surface</color><color name="color_surface_variant">@color/md_theme_surfaceVariant</color><color name="color_on_background">@color/md_theme_onBackground</color><color name="color_on_surface">@color/md_theme_onSurface</color><color name="color_on_surface_variant">@color/md_theme_onSurfaceVariant</color><color name="white">#FFFFFF</color><color name="black">#000000</color><color name="color_error">@color/danger_red</color><color name="color_success">@color/success_green</color><color name="color_info">@color/info_blue</color><color name="color_warning">@color/warning_orange</color><color name="divider">@color/md_theme_outlineVariant</color><color name="sale">@color/income_green</color><color name="expense">@color/expense_red</color><color name="success">@color/success_green</color><color name="primary">@color/md_theme_primary</color><color name="secondary">@color/md_theme_secondary</color><color name="info">@color/info_blue</color><color name="color_tertiary">@color/md_theme_tertiary</color></file><file path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen><dimen name="text_size_small">12sp</dimen><dimen name="text_size_medium">16sp</dimen><dimen name="text_size_large">20sp</dimen><dimen name="text_size_extra_large">24sp</dimen><dimen name="spacing_small">8dp</dimen><dimen name="spacing_medium">16dp</dimen><dimen name="spacing_large">24dp</dimen><dimen name="spacing_extra_large">32dp</dimen><dimen name="button_height">48dp</dimen><dimen name="button_corner_radius">8dp</dimen><dimen name="card_corner_radius">12dp</dimen><dimen name="card_elevation">4dp</dimen><dimen name="nav_header_height">176dp</dimen><dimen name="nav_header_vertical_spacing">8dp</dimen><dimen name="dashboard_card_height">120dp</dimen><dimen name="dashboard_icon_size">48dp</dimen></file><file path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">MA</string><string name="app_version">نسخه 1.0.0</string><string name="login_title">ورود به سیستم</string><string name="username_hint">نام کاربری</string><string name="password_hint">رمز عبور</string><string name="login_button">ورود</string><string name="nav_dashboard">داشبورد</string><string name="nav_transactions">تراکنش‌ها</string><string name="nav_profile">پروفایل</string><string name="nav_notifications">اعلانات</string><string name="nav_reports">گزارشات</string><string name="nav_logout">خروج</string><string name="dashboard_title">خلاصه مالی</string><string name="total_sales">کل فروش</string><string name="total_expenses">کل هزینه‌ها</string><string name="net_profit">سود خالص</string><string name="my_share">سهم من</string><string name="partner_share">سهم شریک</string><string name="register_transaction">ثبت تراکنش</string><string name="reports">گزارشات</string><string name="transaction_sale">فروش</string><string name="transaction_expense">هزینه</string><string name="transaction_withdrawal">برداشت</string><string name="status_pending">در انتظار</string><string name="status_approved">تایید شده</string><string name="status_rejected">رد شده</string><string name="profile_title">پروفایل</string><string name="edit_profile">ویرایش پروفایل</string><string name="change_password">تغییر رمز عبور</string><string name="change_photo">تغییر عکس</string><string name="notifications_title">اعلانات</string><string name="no_notifications">اعلانی وجود ندارد</string><string name="reports_title">گزارشات</string><string name="daily_report">گزارش روزانه</string><string name="monthly_report">گزارش ماهانه</string><string name="partner_comparison">مقایسه شرکا</string><string name="category_analysis">تحلیل دسته‌بندی</string><string name="export_report">صادر کردن</string><string name="share_report">اشتراک‌گذاری</string><string name="loading">در حال بارگذاری...</string><string name="error_network">خطا در اتصال به سرور</string><string name="error_general">خطا در عملیات</string><string name="success">عملیات با موفقیت انجام شد</string><string name="cancel">لغو</string><string name="save">ذخیره</string><string name="delete">حذف</string><string name="edit">ویرایش</string><string name="confirm">تایید</string><string name="back">بازگشت</string><string name="filter">فیلتر</string><string name="clear_all">پاک کردن همه</string><string name="export">صادر کردن</string><string name="share">اشتراک‌گذاری</string><string name="other">سایر</string><string name="no_transactions">تراکنشی وجود ندارد</string><string name="personal_info">اطلاعات شخصی</string><string name="financial_summary">خلاصه مالی</string><string name="sales">فروش</string><string name="expenses">هزینه‌ها</string><string name="profit">سود</string><string name="daily_summary">خلاصه روزانه</string><string name="monthly_summary">خلاصه ماهانه</string><string name="detailed_report">گزارش تفصیلی</string><string-array name="payment_types">
        <item>نقدی</item>
        <item>کارت به کارت</item>
        <item>چک</item>
    </string-array><string-array name="expense_categories">
        <item>مواد اولیه</item>
        <item>حمل و نقل</item>
        <item>اجاره</item>
        <item>تجهیزات</item>
        <item>سایر</item>
    </string-array><string-array name="business_partners">
        <item>علی کاکایی</item>
        <item>میلاد نصیری</item>
    </string-array><string name="app_logo">لوگوی اپلیکیشن</string><string name="login_subtitle">برای ورود به سیستم، اطلاعات خود را وارد کنید</string><string name="email">ایمیل</string><string name="sample_email"><EMAIL></string><string name="password">رمز عبور</string><string name="sample_password">رمز عبور خود را وارد کنید</string><string name="remember_me">مرا به خاطر بسپار</string><string name="forgot_password">فراموشی رمز عبور</string><string name="login">ورود</string><string name="or">یا</string><string name="continue_with_google">ادامه با گوگل</string><string name="continue_with_apple">ادامه با اپل</string><string name="dont_have_account">حساب کاربری ندارید؟</string><string name="sign_up">ثبت نام</string><string name="transaction_type">نوع تراکنش</string><string name="recurring_transaction">تراکنش تکراری</string><string name="date">تاریخ</string><string name="location">مکان</string></file><file path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MA" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_primary</item>
        <item name="colorOnPrimary">@color/md_theme_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_onPrimaryContainer</item>
        
        
        <item name="colorSecondary">@color/md_theme_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_onSecondaryContainer</item>
        
        
        <item name="colorTertiary">@color/md_theme_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_onTertiaryContainer</item>
        
        
        <item name="colorError">@color/md_theme_error</item>
        <item name="colorOnError">@color/md_theme_onError</item>
        <item name="colorErrorContainer">@color/md_theme_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_onErrorContainer</item>
        
        
        <item name="colorSurface">@color/md_theme_surface</item>
        <item name="colorOnSurface">@color/md_theme_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_outlineVariant</item>
        
        
        <item name="android:colorBackground">@color/md_theme_background</item>
        <item name="colorOnBackground">@color/md_theme_onBackground</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        
        <item name="elevationOverlayEnabled">false</item>
        <item name="android:windowSplashScreenBackground">@color/md_theme_primary</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/ic_logo</item>
    </style><style name="Widget.MA.Button" parent="Widget.Material3.Button">
        <item name="android:textAppearance">@style/TextAppearance.MA.Button</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:minHeight">56dp</item>
    </style><style name="Widget.MA.Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textAppearance">@style/TextAppearance.MA.Button</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:minHeight">56dp</item>
    </style><style name="Widget.MA.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusTopStart">16dp</item>
        <item name="boxCornerRadiusTopEnd">16dp</item>
        <item name="boxCornerRadiusBottomStart">16dp</item>
        <item name="boxCornerRadiusBottomEnd">16dp</item>
    </style><style name="Widget.MA.Card" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">8dp</item>
        <item name="contentPadding">16dp</item>
    </style><style name="TextAppearance.MA.HeadlineLarge" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.MA.HeadlineMedium" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textSize">28sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.MA.TitleLarge" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:textSize">22sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.MA.TitleMedium" parent="TextAppearance.Material3.TitleMedium">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.MA.BodyLarge" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">16sp</item>
    </style><style name="TextAppearance.MA.BodyMedium" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
    </style><style name="TextAppearance.MA.BodySmall" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textSize">12sp</item>
    </style><style name="TextAppearance.MA.Button" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.Material3.Button" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.1</item>
    </style><style name="TextAppearance.MA.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textSize">12sp</item>
    </style><style name="Theme.MA.AppBarOverlay" parent="ThemeOverlay.Material3.Dark.ActionBar"/><style name="Theme.MA.PopupOverlay" parent="ThemeOverlay.Material3.Light"/><style name="Widget.Material3.Button.FilledTonalButton" parent="Widget.Material3.Button"/></file><file path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.MA" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_primary</item>
        <item name="colorOnPrimary">@color/md_theme_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_onPrimaryContainer</item>
        
        
        <item name="colorSecondary">@color/md_theme_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_onSecondaryContainer</item>
        
        
        <item name="colorTertiary">@color/md_theme_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_onTertiaryContainer</item>
        
        
        <item name="colorError">@color/md_theme_error</item>
        <item name="colorOnError">@color/md_theme_onError</item>
        <item name="colorErrorContainer">@color/md_theme_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_onErrorContainer</item>
        
        
        <item name="colorSurface">@color/md_theme_surface</item>
        <item name="colorOnSurface">@color/md_theme_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_outlineVariant</item>
        
        
        <item name="android:colorBackground">@color/md_theme_background</item>
        <item name="colorOnBackground">@color/md_theme_onBackground</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        
        <item name="elevationOverlayEnabled">false</item>
        <item name="android:windowSplashScreenBackground">@color/md_theme_primary</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/ic_logo</item>
    </style><style name="Widget.MA.Button" parent="Widget.Material3.Button">
        <item name="android:textAppearance">@style/TextAppearance.MA.Button</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:minHeight">56dp</item>
    </style><style name="Widget.MA.Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textAppearance">@style/TextAppearance.MA.Button</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:minHeight">56dp</item>
    </style><style name="Widget.MA.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusTopStart">16dp</item>
        <item name="boxCornerRadiusTopEnd">16dp</item>
        <item name="boxCornerRadiusBottomStart">16dp</item>
        <item name="boxCornerRadiusBottomEnd">16dp</item>
    </style><style name="Widget.MA.Card" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">8dp</item>
        <item name="contentPadding">16dp</item>
    </style><style name="TextAppearance.MA.HeadlineLarge" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.MA.HeadlineMedium" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textSize">28sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.MA.TitleLarge" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:textSize">22sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.MA.TitleMedium" parent="TextAppearance.Material3.TitleMedium">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.MA.BodyLarge" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">16sp</item>
    </style><style name="TextAppearance.MA.BodyMedium" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
    </style><style name="TextAppearance.MA.Button" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="TextAppearance.Material3.Button" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.1</item>
    </style><style name="TextAppearance.MA.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textSize">12sp</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AndroidStudioProjects\MA\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>