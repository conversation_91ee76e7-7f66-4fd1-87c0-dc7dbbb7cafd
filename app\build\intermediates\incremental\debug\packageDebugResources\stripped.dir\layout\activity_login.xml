<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layoutDirection="rtl"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        android:gravity="center">

        <!-- Logo -->
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_marginTop="48dp"
            android:layout_marginBottom="32dp"
            android:src="@drawable/ic_logo"
            android:contentDescription="@string/app_name" />

        <!-- Title -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:text="@string/login_title"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurface" />

        <!-- Username Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="@string/username_hint"
            app:startIconDrawable="@drawable/ic_person"
            app:startIconTint="?attr/colorPrimary"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etUsername"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1"
                android:textDirection="ltr" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Password Input -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:hint="@string/password_hint"
            app:startIconDrawable="@drawable/ic_lock"
            app:startIconTint="?attr/colorPrimary"
            app:endIconMode="password_toggle"
            style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textPassword"
                android:maxLines="1"
                android:textDirection="ltr" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Error Message -->
        <TextView
            android:id="@+id/tvError"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:textColor="@color/color_error"
            android:textSize="14sp"
            android:gravity="center"
            android:visibility="gone" />

        <!-- Login Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnLogin"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="@string/login_button"
            android:textSize="16sp"
            app:cornerRadius="8dp" />

        <!-- Version Info -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="@string/app_version"
            android:textSize="12sp"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:alpha="0.7" />

    </LinearLayout>

</ScrollView> 