<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Appearance -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardAppearance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvCurrentTheme"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="تم فعلی: پیش‌فرض" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnChangeTheme"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="تغییر تم"
                    android:layout_marginTop="8dp" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Notifications -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardNotifications"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switchPushNotifications"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="اعلان‌های پوش" />

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switchSoundVibration"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="صدا و لرزش" />

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switchTransactionAlerts"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="هشدار تراکنش‌ها" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Security -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardSecurity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnChangePassword"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="تغییر رمز عبور" />

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switchBiometric"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="احراز هویت بیومتریک" />

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switchAutoLock"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="قفل خودکار" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Data & Sync -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardDataSync"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <com.google.android.material.switchmaterial.SwitchMaterial
                    android:id="@+id/switchAutoSync"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="همگام‌سازی خودکار" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnBackupData"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="پشتیبان‌گیری" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnClearCache"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="پاک کردن کش" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- About -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardAbout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/tvAppVersion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="نسخه: 1.0.0" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnContactSupport"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ارتباط با پشتیبانی" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnLogout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="خروج" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView> 