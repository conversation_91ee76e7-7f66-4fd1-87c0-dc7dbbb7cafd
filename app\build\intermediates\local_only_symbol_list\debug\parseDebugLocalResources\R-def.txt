R_DEF: Internal format may change without notice
local
array business_partners
array expense_categories
array payment_types
color black
color color_background
color color_error
color color_info
color color_on_background
color color_on_primary
color color_on_secondary
color color_on_surface
color color_on_surface_variant
color color_primary
color color_primary_dark
color color_primary_variant
color color_secondary
color color_secondary_variant
color color_success
color color_surface
color color_surface_variant
color color_tertiary
color color_warning
color danger_red
color divider
color expense
color expense_red
color gradient_end
color gradient_start
color income_green
color info
color info_blue
color md_theme_background
color md_theme_error
color md_theme_errorContainer
color md_theme_onBackground
color md_theme_onError
color md_theme_onErrorContainer
color md_theme_onPrimary
color md_theme_onPrimaryContainer
color md_theme_onSecondary
color md_theme_onSecondaryContainer
color md_theme_onSurface
color md_theme_onSurfaceVariant
color md_theme_onTertiary
color md_theme_onTertiaryContainer
color md_theme_outline
color md_theme_outlineVariant
color md_theme_primary
color md_theme_primaryContainer
color md_theme_secondary
color md_theme_secondaryContainer
color md_theme_surface
color md_theme_surfaceVariant
color md_theme_tertiary
color md_theme_tertiaryContainer
color neutral_gray
color primary
color sale
color secondary
color shadow_dark
color shadow_light
color shadow_medium
color success
color success_green
color transfer_blue
color warning_orange
color white
dimen activity_horizontal_margin
dimen activity_vertical_margin
dimen button_corner_radius
dimen button_height
dimen card_corner_radius
dimen card_elevation
dimen dashboard_card_height
dimen dashboard_icon_size
dimen nav_header_height
dimen nav_header_vertical_spacing
dimen spacing_extra_large
dimen spacing_large
dimen spacing_medium
dimen spacing_small
dimen text_size_extra_large
dimen text_size_large
dimen text_size_medium
dimen text_size_small
drawable bg_unread_indicator
drawable category_background
drawable circle_background
drawable circle_background_primary
drawable circle_background_white
drawable gradient_primary
drawable ic_add
drawable ic_apple
drawable ic_calendar
drawable ic_camera
drawable ic_clear_all
drawable ic_dashboard
drawable ic_edit
drawable ic_email
drawable ic_empty_state
drawable ic_expense
drawable ic_export
drawable ic_filter
drawable ic_google
drawable ic_image
drawable ic_income
drawable ic_launcher
drawable ic_launcher_foreground
drawable ic_launcher_round
drawable ic_location
drawable ic_lock
drawable ic_logo
drawable ic_logout
drawable ic_notifications
drawable ic_person
drawable ic_phone
drawable ic_profit
drawable ic_recurring
drawable ic_reports
drawable ic_sales
drawable ic_share
drawable ic_time
drawable ic_transactions
drawable ic_transfer
drawable profile_placeholder
drawable status_background
drawable subcategory_background
id btnAppleSignIn
id btnApprove
id btnBackupData
id btnCancel
id btnChangePassword
id btnChangePhoto
id btnChangeTheme
id btnClearCache
id btnContactSupport
id btnEditProfile
id btnExportReport
id btnFilter
id btnForgotPassword
id btnGoogleSignIn
id btnLogin
id btnLogout
id btnRegisterTransaction
id btnReject
id btnReports
id btnSave
id btnSelectImage
id btnShareReport
id btnSignUp
id btnTakePhoto
id cardAbout
id cardAppearance
id cardCategoryAnalysis
id cardDailyReport
id cardDataSync
id cardFinancialSummary
id cardMonthlyReport
id cardNotification
id cardNotifications
id cardPartnerComparison
id cardProfileInfo
id cardSecurity
id categoryLayout
id cbRememberMe
id chipGroupTags
id chipNotificationType
id dashboardFragment
id dateTimeLayout
id drawer_layout
id emptyStateLayout
id etAmount
id etDescription
id etEmail
id etPassword
id etUsername
id fabAddTransaction
id fabCamera
id headerLayout
id indicatorUnread
id ivLogo
id ivProfileImage
id ivRecurring
id ivSenderProfile
id ivTransactionType
id locationLayout
id loginCard
id nav_connection_test
id nav_dashboard
id nav_graph
id nav_host_fragment
id nav_logout
id nav_notifications
id nav_profile
id nav_reports
id nav_transactions
id nav_view
id notificationsFragment
id profileFragment
id progressIndicator
id rbCapital
id rbExpense
id rbSale
id rbWithdrawal
id recyclerViewNotifications
id recyclerViewTransactions
id reportsFragment
id rgTransactionType
id statusIndicator
id switchAutoLock
id switchAutoSync
id switchBiometric
id switchPushNotifications
id switchSoundVibration
id switchTransactionAlerts
id tilEmail
id tilPassword
id toolbar
id transactionsFragment
id tvAmount
id tvAppVersion
id tvCategory
id tvCurrentTheme
id tvDate
id tvDescription
id tvEmptyState
id tvError
id tvHeaderUserEmail
id tvHeaderUserName
id tvInventoryCount
id tvLocation
id tvMyShare
id tvNetProfit
id tvNotificationMessage
id tvNotificationTime
id tvNotificationTitle
id tvPartnerShare
id tvSenderName
id tvSubcategory
id tvTime
id tvTotalExpenses
id tvTotalSales
id tvUserEmail
id tvUserName
id tvUserPhone
id tvUserRole
layout activity_login
layout activity_main
layout activity_profile_image
layout dialog_create_transaction
layout fragment_dashboard
layout fragment_login
layout fragment_notifications
layout fragment_profile
layout fragment_reports
layout fragment_settings
layout fragment_transactions
layout item_notification
layout item_transaction
layout nav_header_main
menu activity_main_drawer
navigation nav_graph
string app_logo
string app_name
string app_version
string back
string cancel
string category_analysis
string change_password
string change_photo
string clear_all
string confirm
string continue_with_apple
string continue_with_google
string daily_report
string daily_summary
string dashboard_title
string date
string delete
string detailed_report
string dont_have_account
string edit
string edit_profile
string email
string error_general
string error_network
string expenses
string export
string export_report
string filter
string financial_summary
string forgot_password
string loading
string location
string login
string login_button
string login_subtitle
string login_title
string monthly_report
string monthly_summary
string my_share
string nav_dashboard
string nav_logout
string nav_notifications
string nav_profile
string nav_reports
string nav_transactions
string net_profit
string no_notifications
string no_transactions
string notifications_title
string or
string other
string partner_comparison
string partner_share
string password
string password_hint
string personal_info
string profile_title
string profit
string recurring_transaction
string register_transaction
string remember_me
string reports
string reports_title
string sales
string sample_email
string sample_password
string save
string share
string share_report
string sign_up
string status_approved
string status_pending
string status_rejected
string success
string total_expenses
string total_sales
string transaction_expense
string transaction_sale
string transaction_type
string transaction_withdrawal
string username_hint
style TextAppearance.MA.BodyLarge
style TextAppearance.MA.BodyMedium
style TextAppearance.MA.BodySmall
style TextAppearance.MA.Button
style TextAppearance.MA.Caption
style TextAppearance.MA.HeadlineLarge
style TextAppearance.MA.HeadlineMedium
style TextAppearance.MA.TitleLarge
style TextAppearance.MA.TitleMedium
style TextAppearance.Material3.Button
style Theme.MA
style Theme.MA.AppBarOverlay
style Theme.MA.PopupOverlay
style Widget.MA.Button
style Widget.MA.Button.Secondary
style Widget.MA.Card
style Widget.MA.TextInputLayout
style Widget.Material3.Button.FilledTonalButton
xml backup_rules
xml data_extraction_rules
xml file_paths
