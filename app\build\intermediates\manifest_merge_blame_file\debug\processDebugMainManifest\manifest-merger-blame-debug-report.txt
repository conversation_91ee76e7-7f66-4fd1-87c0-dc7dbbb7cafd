1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.ma"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission
14-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:8:5-9:38
15        android:name="android.permission.READ_EXTERNAL_STORAGE"
15-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:8:22-77
16        android:maxSdkVersion="32" />
16-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:9:9-35
17    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
17-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:10:5-76
17-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:10:22-73
18    <uses-permission
18-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:11:5-12:38
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:11:22-78
20        android:maxSdkVersion="28" />
20-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:12:9-35
21    <uses-permission android:name="android.permission.CAMERA" />
21-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:13:5-65
21-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:13:22-62
22    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
22-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:14:5-72
22-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:14:22-69
23    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
23-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:15:5-74
23-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:15:22-71
24
25    <permission
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
26        android:name="com.example.ma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.example.ma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
30
31    <application
31-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:17:5-73:19
32        android:name="com.example.ma.MAApplication"
32-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:18:9-38
33        android:allowBackup="true"
33-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:19:9-35
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
35        android:dataExtractionRules="@xml/data_extraction_rules"
35-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:20:9-65
36        android:debuggable="true"
37        android:extractNativeLibs="false"
38        android:fullBackupContent="@xml/backup_rules"
38-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:21:9-54
39        android:icon="@drawable/ic_launcher"
39-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:22:9-45
40        android:label="@string/app_name"
40-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:23:9-41
41        android:roundIcon="@drawable/ic_launcher_round"
41-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:24:9-56
42        android:supportsRtl="true"
42-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:25:9-35
43        android:testOnly="true"
44        android:theme="@style/Theme.MA" >
44-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:26:9-40
45
46        <!-- Login Activity -->
47        <activity
47-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:30:9-39:20
48            android:name="com.example.ma.presentation.auth.LoginActivity"
48-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:31:13-60
49            android:exported="true"
49-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:32:13-36
50            android:screenOrientation="portrait"
50-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:33:13-49
51            android:windowSoftInputMode="adjustResize" >
51-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:34:13-55
52            <intent-filter>
52-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:35:13-38:29
53                <action android:name="android.intent.action.MAIN" />
53-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:36:17-69
53-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:36:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:37:17-77
55-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:37:27-74
56            </intent-filter>
57        </activity>
58
59        <!-- Main Activity -->
60        <activity
60-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:42:9-45:52
61            android:name="com.example.ma.presentation.main.MainActivity"
61-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:43:13-59
62            android:exported="false"
62-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:44:13-37
63            android:screenOrientation="portrait" />
63-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:45:13-49
64
65        <!-- Profile Activity -->
66        <activity
66-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:48:9-51:52
67            android:name="com.example.ma.presentation.profile.ProfileImageActivity"
67-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:49:13-70
68            android:exported="false"
68-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:50:13-37
69            android:screenOrientation="portrait" />
69-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:51:13-49
70
71        <!-- Connection Test Activity (Debug Only) -->
72        <activity
72-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:54:9-58:50
73            android:name="com.example.ma.presentation.test.ConnectionTestActivity"
73-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:55:13-69
74            android:exported="false"
74-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:56:13-37
75            android:label="تست اتصال Supabase"
75-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:58:13-47
76            android:screenOrientation="portrait" />
76-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:57:13-49
77
78        <!-- File Provider for Image Cropper -->
79        <provider
80            android:name="androidx.core.content.FileProvider"
80-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:64:13-62
81            android:authorities="com.example.ma.fileprovider"
81-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:65:13-64
82            android:exported="false"
82-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:66:13-37
83            android:grantUriPermissions="true" >
83-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:67:13-47
84            <meta-data
84-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:68:13-70:54
85                android:name="android.support.FILE_PROVIDER_PATHS"
85-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:69:17-67
86                android:resource="@xml/file_paths" />
86-->C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:70:17-51
87        </provider>
88
89        <service
89-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
90            android:name="androidx.room.MultiInstanceInvalidationService"
90-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
91            android:directBootAware="true"
91-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
92            android:exported="false" />
92-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
93
94        <provider
94-->[io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:9:9-17:20
95            android:name="androidx.startup.InitializationProvider"
95-->[io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:10:13-67
96            android:authorities="com.example.ma.androidx-startup"
96-->[io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:11:13-68
97            android:exported="false" >
97-->[io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:12:13-37
98            <meta-data
98-->[io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:14:13-16:52
99                android:name="io.github.jan.supabase.storage.SupabaseInitializer"
99-->[io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:15:17-82
100                android:value="androidx.startup" />
100-->[io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:16:17-49
101            <meta-data
101-->[io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:14:13-16:52
102                android:name="io.github.jan.supabase.auth.SupabaseInitializer"
102-->[io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:15:17-79
103                android:value="androidx.startup" />
103-->[io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:16:17-49
104            <meta-data
104-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
105                android:name="androidx.emoji2.text.EmojiCompatInitializer"
105-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
106                android:value="androidx.startup" />
106-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
107            <meta-data
107-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
108-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
109                android:value="androidx.startup" />
109-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
110            <meta-data
110-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
111                android:name="com.russhwolf.settings.SettingsInitializer"
111-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
112                android:value="androidx.startup" />
112-->[com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
113            <meta-data
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
114                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
114-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
115                android:value="androidx.startup" />
115-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
116        </provider>
117
118        <uses-library
118-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
119            android:name="androidx.window.extensions"
119-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
120            android:required="false" />
120-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
121        <uses-library
121-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
122            android:name="androidx.window.sidecar"
122-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
123            android:required="false" />
123-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
124
125        <receiver
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
126            android:name="androidx.profileinstaller.ProfileInstallReceiver"
126-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
127            android:directBootAware="false"
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
128            android:enabled="true"
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
129            android:exported="true"
129-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
130            android:permission="android.permission.DUMP" >
130-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
131            <intent-filter>
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
132                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
132-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
132-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
133            </intent-filter>
134            <intent-filter>
134-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
135                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
135-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
135-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
136            </intent-filter>
137            <intent-filter>
137-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
138                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
138-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
138-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
139            </intent-filter>
140            <intent-filter>
140-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
141                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
141-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
141-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
142            </intent-filter>
143        </receiver>
144    </application>
145
146</manifest>
