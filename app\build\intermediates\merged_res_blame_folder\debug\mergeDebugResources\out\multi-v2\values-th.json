{"logs": [{"outputFile": "com.example.ma.app-mergeDebugResources-53:/values-th/values-th.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fbf17065ba87837a4f638cc34db08c6d\\transformed\\appcompat-1.6.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,303,411,496,598,708,786,863,954,1047,1138,1232,1332,1425,1520,1614,1705,1796,1877,1980,2078,2176,2279,2385,2486,2639,2734", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "205,298,406,491,593,703,781,858,949,1042,1133,1227,1327,1420,1515,1609,1700,1791,1872,1975,2073,2171,2274,2380,2481,2634,2729,2811"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "335,440,533,641,726,828,938,1016,1093,1184,1277,1368,1462,1562,1655,1750,1844,1935,2026,2107,2210,2308,2406,2509,2615,2716,2869,9659", "endColumns": "104,92,107,84,101,109,77,76,90,92,90,93,99,92,94,93,90,90,80,102,97,97,102,105,100,152,94,81", "endOffsets": "435,528,636,721,823,933,1011,1088,1179,1272,1363,1457,1557,1650,1745,1839,1930,2021,2102,2205,2303,2401,2504,2610,2711,2864,2959,9736"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ba4c34321842e3ae1e1f73fa0eba9cee\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "48,51,52,53", "startColumns": "4,4,4,4", "startOffsets": "4370,4633,4732,4843", "endColumns": "102,98,110,97", "endOffsets": "4468,4727,4838,4936"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\eb4d761af10aeed51fe1f849fc010c93\\transformed\\navigation-ui-2.7.7\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,159", "endColumns": "103,113", "endOffsets": "154,268"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9364,9468", "endColumns": "103,113", "endOffsets": "9463,9577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\625ad6195f2a7f30267b2379d326a89d\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "38,39,40,41,42,43,44,115", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3363,3459,3562,3660,3758,3861,3966,9741", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "3454,3557,3655,3753,3856,3961,4073,9837"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\0d81e12e2f4d36f2b1cdaf3882f97537\\transformed\\material-1.11.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,285,358,430,513,598,684,783,896,976,1046,1136,1206,1266,1353,1419,1484,1545,1609,1670,1724,1825,1886,1946,2000,2070,2181,2268,2349,2492,2571,2653,2785,2877,2955,3009,3062,3128,3198,3276,3362,3442,3514,3592,3661,3730,3828,3910,3998,4091,4185,4259,4328,4423,4475,4558,4626,4711,4799,4861,4925,4988,5058,5158,5254,5351,5444,5502,5559", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,131,91,77,53,52,65,69,77,85,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76", "endOffsets": "280,353,425,508,593,679,778,891,971,1041,1131,1201,1261,1348,1414,1479,1540,1604,1665,1719,1820,1881,1941,1995,2065,2176,2263,2344,2487,2566,2648,2780,2872,2950,3004,3057,3123,3193,3271,3357,3437,3509,3587,3656,3725,3823,3905,3993,4086,4180,4254,4323,4418,4470,4553,4621,4706,4794,4856,4920,4983,5053,5153,5249,5346,5439,5497,5554,5631"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,49,50,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2964,3037,3109,3192,3277,4078,4177,4290,4473,4543,4941,5011,5071,5158,5224,5289,5350,5414,5475,5529,5630,5691,5751,5805,5875,5986,6073,6154,6297,6376,6458,6590,6682,6760,6814,6867,6933,7003,7081,7167,7247,7319,7397,7466,7535,7633,7715,7803,7896,7990,8064,8133,8228,8280,8363,8431,8516,8604,8666,8730,8793,8863,8963,9059,9156,9249,9307,9582", "endLines": "5,33,34,35,36,37,45,46,47,49,50,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113", "endColumns": "12,72,71,82,84,85,98,112,79,69,89,69,59,86,65,64,60,63,60,53,100,60,59,53,69,110,86,80,142,78,81,131,91,77,53,52,65,69,77,85,79,71,77,68,68,97,81,87,92,93,73,68,94,51,82,67,84,87,61,63,62,69,99,95,96,92,57,56,76", "endOffsets": "330,3032,3104,3187,3272,3358,4172,4285,4365,4538,4628,5006,5066,5153,5219,5284,5345,5409,5470,5524,5625,5686,5746,5800,5870,5981,6068,6149,6292,6371,6453,6585,6677,6755,6809,6862,6928,6998,7076,7162,7242,7314,7392,7461,7530,7628,7710,7798,7891,7985,8059,8128,8223,8275,8358,8426,8511,8599,8661,8725,8788,8858,8958,9054,9151,9244,9302,9359,9654"}}]}]}