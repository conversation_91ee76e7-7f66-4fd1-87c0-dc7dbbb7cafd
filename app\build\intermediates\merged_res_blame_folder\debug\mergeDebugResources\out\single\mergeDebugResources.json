[{"merged": "com.example.ma.app-debug-55:/drawable_ic_filter.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_filter.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_fragment_login.xml.flat", "source": "com.example.ma.app-main-57:/layout/fragment_login.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_email.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_email.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_launcher_round.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_launcher_round.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_edit.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_edit.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_logout.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_logout.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_camera.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_camera.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_fragment_settings.xml.flat", "source": "com.example.ma.app-main-57:/layout/fragment_settings.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_empty_state.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_empty_state.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_gradient_primary.xml.flat", "source": "com.example.ma.app-main-57:/drawable/gradient_primary.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_add.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_add.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_apple.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_apple.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_activity_login.xml.flat", "source": "com.example.ma.app-main-57:/layout/activity_login.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_time.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_time.xml"}, {"merged": "com.example.ma.app-debug-55:/xml_data_extraction_rules.xml.flat", "source": "com.example.ma.app-main-57:/xml/data_extraction_rules.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_logo.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_logo.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_fragment_reports.xml.flat", "source": "com.example.ma.app-main-57:/layout/fragment_reports.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_income.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_income.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_reports.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_reports.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_phone.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_phone.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_lock.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_lock.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_google.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_google.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_notifications.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_notifications.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_fragment_profile.xml.flat", "source": "com.example.ma.app-main-57:/layout/fragment_profile.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_clear_all.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_clear_all.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_item_transaction.xml.flat", "source": "com.example.ma.app-main-57:/layout/item_transaction.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_transfer.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_transfer.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_person.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_person.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_location.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_location.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_calendar.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_calendar.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_dashboard.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_dashboard.xml"}, {"merged": "com.example.ma.app-debug-55:/xml_file_paths.xml.flat", "source": "com.example.ma.app-main-57:/xml/file_paths.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_circle_background.xml.flat", "source": "com.example.ma.app-main-57:/drawable/circle_background.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_bg_unread_indicator.xml.flat", "source": "com.example.ma.app-main-57:/drawable/bg_unread_indicator.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_image.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_image.xml"}, {"merged": "com.example.ma.app-debug-55:/navigation_nav_graph.xml.flat", "source": "com.example.ma.app-main-57:/navigation/nav_graph.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_fragment_dashboard.xml.flat", "source": "com.example.ma.app-main-57:/layout/fragment_dashboard.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_sales.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_sales.xml"}, {"merged": "com.example.ma.app-debug-55:/menu_activity_main_drawer.xml.flat", "source": "com.example.ma.app-main-57:/menu/activity_main_drawer.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_profile_placeholder.xml.flat", "source": "com.example.ma.app-main-57:/drawable/profile_placeholder.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_launcher.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_launcher.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_transactions.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_transactions.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_subcategory_background.xml.flat", "source": "com.example.ma.app-main-57:/drawable/subcategory_background.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_category_background.xml.flat", "source": "com.example.ma.app-main-57:/drawable/category_background.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_fragment_notifications.xml.flat", "source": "com.example.ma.app-main-57:/layout/fragment_notifications.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_fragment_transactions.xml.flat", "source": "com.example.ma.app-main-57:/layout/fragment_transactions.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_circle_background_white.xml.flat", "source": "com.example.ma.app-main-57:/drawable/circle_background_white.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_profit.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_profit.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_share.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_share.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_activity_profile_image.xml.flat", "source": "com.example.ma.app-main-57:/layout/activity_profile_image.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_nav_header_main.xml.flat", "source": "com.example.ma.app-main-57:/layout/nav_header_main.xml"}, {"merged": "com.example.ma.app-debug-55:/xml_backup_rules.xml.flat", "source": "com.example.ma.app-main-57:/xml/backup_rules.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_dialog_create_transaction.xml.flat", "source": "com.example.ma.app-main-57:/layout/dialog_create_transaction.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_circle_background_primary.xml.flat", "source": "com.example.ma.app-main-57:/drawable/circle_background_primary.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_expense.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_expense.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_item_notification.xml.flat", "source": "com.example.ma.app-main-57:/layout/item_notification.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_recurring.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_recurring.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_ic_export.xml.flat", "source": "com.example.ma.app-main-57:/drawable/ic_export.xml"}, {"merged": "com.example.ma.app-debug-55:/layout_activity_main.xml.flat", "source": "com.example.ma.app-main-57:/layout/activity_main.xml"}, {"merged": "com.example.ma.app-debug-55:/drawable_status_background.xml.flat", "source": "com.example.ma.app-main-57:/drawable/status_background.xml"}]