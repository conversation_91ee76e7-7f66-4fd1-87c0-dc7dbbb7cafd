<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:colorBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <!-- Header -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="گزارشات مالی"
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="?android:textColorPrimary"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="24dp" />

        <!-- Financial Summary Card -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="?colorSurface"
            android:layout_marginBottom="24dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="خلاصه مالی"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="?android:textColorPrimary"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/tvTotalSales"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="کل فروش: ۰ تومان"
                    android:textSize="16sp"
                    android:textColor="@color/sale"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvTotalExpenses"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="کل هزینه: ۰ تومان"
                    android:textSize="16sp"
                    android:textColor="@color/expense"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvNetProfit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="سود خالص: ۰ تومان"
                    android:textSize="16sp"
                    android:textColor="@color/success"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvMyShare"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="سهم من: ۰ تومان"
                    android:textSize="16sp"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvPartnerShare"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="سهم شریک: ۰ تومان"
                    android:textSize="16sp"
                    android:textColor="@color/secondary"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvInventoryCount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="موجودی انبار: ۰ بطری"
                    android:textSize="16sp"
                    android:textColor="@color/info" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Placeholder cards for navigation (optional) -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardDailyReport"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="8dp" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardMonthlyReport"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="8dp" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardPartnerComparison"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="8dp" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardCategoryAnalysis"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            app:cardElevation="2dp"
            app:cardCornerRadius="8dp" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnExportReport"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="خروجی PDF"
                style="@style/Widget.MA.Button"
                android:layout_marginEnd="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnShareReport"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="اشتراک‌گذاری"
                style="@style/Widget.MA.Button.Secondary"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView> 