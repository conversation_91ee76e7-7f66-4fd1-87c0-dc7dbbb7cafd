<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layoutDirection="rtl">

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/nav_transactions"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface" />

            <!-- Filter <PERSON>ton -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnFilter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/filter"
                android:textSize="12sp"
                style="@style/Widget.Material3.Button.OutlinedButton"
                app:icon="@drawable/ic_filter"
                app:iconSize="16dp" />

        </LinearLayout>

        <!-- RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewTransactions"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:padding="8dp"
            android:clipToPadding="false" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/emptyStateLayout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:src="@drawable/ic_empty_state"
                android:alpha="0.5"
                android:layout_marginBottom="16dp" />

            <TextView
                android:id="@+id/tvEmptyState"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_transactions"
                android:textSize="16sp"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:gravity="center" />

        </LinearLayout>

    </LinearLayout>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddTransaction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        app:tint="@color/color_on_primary" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 