<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/cardNotification"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="?colorSurface">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Sender Profile Image -->
        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/ivSenderProfile"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_person"
            android:layout_marginEnd="12dp"
            app:civ_border_width="1dp"
            app:civ_border_color="?colorOutline" />

        <!-- Notification Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Sender Name and Time Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/tvSenderName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="نام فرستنده"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="?colorPrimary" />

                <TextView
                    android:id="@+id/tvNotificationTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="زمان"
                    android:textSize="12sp"
                    android:textColor="?android:textColorSecondary" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvNotificationTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="عنوان اعلان"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?android:textColorPrimary"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tvNotificationMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="متن اعلان"
                android:textSize="14sp"
                android:textColor="?android:textColorSecondary"
                android:layout_marginBottom="8dp" />

            <!-- Notification Type Badge -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chipNotificationType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="نوع اعلان"
                android:textSize="10sp"
                style="@style/Widget.Material3.Chip.Assist" />

            <!-- Action Buttons for Transaction Request -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end"
                android:layout_marginTop="8dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnReject"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="رد"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:visibility="gone" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnApprove"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="تایید"
                    style="@style/Widget.Material3.Button.FilledTonalButton"
                    android:layout_marginStart="8dp"
                    android:visibility="gone" />
            </LinearLayout>

        </LinearLayout>

        <!-- Unread indicator -->
        <View
            android:id="@+id/indicatorUnread"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_marginStart="8dp"
            android:background="@drawable/bg_unread_indicator" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView> 