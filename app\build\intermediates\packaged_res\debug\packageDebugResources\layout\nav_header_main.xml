<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="176dp"
    android:background="@drawable/gradient_primary"
    android:gravity="bottom"
    android:orientation="vertical"
    android:padding="16dp"
    android:theme="@style/ThemeOverlay.Material3.Dark">

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/ivProfileImage"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_marginBottom="8dp"
        android:src="@drawable/ic_person"
        app:civ_border_color="@color/color_on_primary"
        app:civ_border_width="2dp" />

    <TextView
        android:id="@+id/tvHeaderUserName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:text="نام کاربر"
        android:textAppearance="@style/TextAppearance.MA.TitleLarge"
        android:textColor="@color/color_on_primary" />

    <TextView
        android:id="@+id/tvHeaderUserEmail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="ایمیل کاربر"
        android:textAppearance="@style/TextAppearance.MA.BodyMedium"
        android:textColor="@color/color_on_primary"
        android:alpha="0.8" />

</LinearLayout> 