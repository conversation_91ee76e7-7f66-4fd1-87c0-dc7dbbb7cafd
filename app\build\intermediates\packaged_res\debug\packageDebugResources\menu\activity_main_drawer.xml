<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="navigation_view">

    <group android:checkableBehavior="single">
        
        <!-- Dashboard -->
        <item
            android:id="@+id/nav_dashboard"
            android:icon="@drawable/ic_dashboard"
            android:title="@string/nav_dashboard" />

        <!-- Transactions -->
        <item
            android:id="@+id/nav_transactions"
            android:icon="@drawable/ic_transactions"
            android:title="@string/nav_transactions" />

        <!-- Profile -->
        <item
            android:id="@+id/nav_profile"
            android:icon="@drawable/ic_person"
            android:title="@string/nav_profile" />

        <!-- Notifications -->
        <item
            android:id="@+id/nav_notifications"
            android:icon="@drawable/ic_notifications"
            android:title="@string/nav_notifications" />

        <!-- Reports -->
        <item
            android:id="@+id/nav_reports"
            android:icon="@drawable/ic_reports"
            android:title="@string/nav_reports" />

    </group>

    <item android:title="@string/other">
        <menu>
            <!-- Connection Test (Debug Only) -->
            <item
                android:id="@+id/nav_connection_test"
                android:icon="@drawable/ic_dashboard"
                android:title="تست اتصال Supabase"
                android:visible="false" />

            <!-- Logout -->
            <item
                android:id="@+id/nav_logout"
                android:icon="@drawable/ic_logout"
                android:title="@string/nav_logout" />
        </menu>
    </item>

</menu> 