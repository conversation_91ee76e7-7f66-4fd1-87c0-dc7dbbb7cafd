<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="TextAppearance.MA.BodyLarge" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textSize">16sp</item>
    </style>
    <style name="TextAppearance.MA.BodyMedium" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textSize">14sp</item>
    </style>
    <style name="TextAppearance.MA.Button" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.MA.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textSize">12sp</item>
    </style>
    <style name="TextAppearance.MA.HeadlineLarge" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.MA.HeadlineMedium" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textSize">28sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.MA.TitleLarge" parent="TextAppearance.Material3.TitleLarge">
        <item name="android:textSize">22sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.MA.TitleMedium" parent="TextAppearance.Material3.TitleMedium">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="TextAppearance.Material3.Button" parent="TextAppearance.Material3.LabelLarge">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.1</item>
    </style>
    <style name="Theme.MA" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/md_theme_primary</item>
        <item name="colorOnPrimary">@color/md_theme_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_onPrimaryContainer</item>
        
        
        <item name="colorSecondary">@color/md_theme_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_onSecondaryContainer</item>
        
        
        <item name="colorTertiary">@color/md_theme_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_onTertiaryContainer</item>
        
        
        <item name="colorError">@color/md_theme_error</item>
        <item name="colorOnError">@color/md_theme_onError</item>
        <item name="colorErrorContainer">@color/md_theme_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_onErrorContainer</item>
        
        
        <item name="colorSurface">@color/md_theme_surface</item>
        <item name="colorOnSurface">@color/md_theme_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_outlineVariant</item>
        
        
        <item name="android:colorBackground">@color/md_theme_background</item>
        <item name="colorOnBackground">@color/md_theme_onBackground</item>
        
        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
        
        
        <item name="elevationOverlayEnabled">false</item>
        <item name="android:windowSplashScreenBackground">@color/md_theme_primary</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/ic_logo</item>
    </style>
    <style name="Widget.MA.Button" parent="Widget.Material3.Button">
        <item name="android:textAppearance">@style/TextAppearance.MA.Button</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:minHeight">56dp</item>
    </style>
    <style name="Widget.MA.Button.Secondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textAppearance">@style/TextAppearance.MA.Button</item>
        <item name="cornerRadius">24dp</item>
        <item name="android:minHeight">56dp</item>
    </style>
    <style name="Widget.MA.Card" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">8dp</item>
        <item name="contentPadding">16dp</item>
    </style>
    <style name="Widget.MA.TextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusTopStart">16dp</item>
        <item name="boxCornerRadiusTopEnd">16dp</item>
        <item name="boxCornerRadiusBottomStart">16dp</item>
        <item name="boxCornerRadiusBottomEnd">16dp</item>
    </style>
</resources>