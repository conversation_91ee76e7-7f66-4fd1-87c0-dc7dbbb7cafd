com.example.ma.app-lifecycle-livedata-core-2.9.1-0 C:\Users\<USER>\.gradle\caches\transforms-4\02c495e1c56cbd289ac6df46f27d8bf2\transformed\lifecycle-livedata-core-2.9.1\res
com.example.ma.app-fragment-1.6.2-1 C:\Users\<USER>\.gradle\caches\transforms-4\0588c00a6aae7c230ba4096f1634a4f3\transformed\fragment-1.6.2\res
com.example.ma.app-material-1.11.0-2 C:\Users\<USER>\.gradle\caches\transforms-4\0d81e12e2f4d36f2b1cdaf3882f97537\transformed\material-1.11.0\res
com.example.ma.app-navigation-fragment-2.7.7-3 C:\Users\<USER>\.gradle\caches\transforms-4\175452cc7ca4443efe808e6d3551918c\transformed\navigation-fragment-2.7.7\res
com.example.ma.app-sqlite-framework-2.4.0-4 C:\Users\<USER>\.gradle\caches\transforms-4\245a6f12d9bb7d6d4ec681c05d22c3b7\transformed\sqlite-framework-2.4.0\res
com.example.ma.app-savedstate-release-5 C:\Users\<USER>\.gradle\caches\transforms-4\27e2a12b5011d1f156205bfab33d92c7\transformed\savedstate-release\res
com.example.ma.app-core-viewtree-1.0.0-6 C:\Users\<USER>\.gradle\caches\transforms-4\2ba88f78c55bfd41baf55bd6151fbefa\transformed\core-viewtree-1.0.0\res
com.example.ma.app-glide-4.16.0-7 C:\Users\<USER>\.gradle\caches\transforms-4\2fe034d3e5fce316307e3cff25aaa8a0\transformed\glide-4.16.0\res
com.example.ma.app-activity-ktx-1.8.0-8 C:\Users\<USER>\.gradle\caches\transforms-4\32619ee7040a86f9cc4da58a341603d8\transformed\activity-ktx-1.8.0\res
com.example.ma.app-sqlite-2.4.0-9 C:\Users\<USER>\.gradle\caches\transforms-4\3297e24f42a8fc46aa558945b8a33631\transformed\sqlite-2.4.0\res
com.example.ma.app-navigation-ui-ktx-2.7.7-10 C:\Users\<USER>\.gradle\caches\transforms-4\3bd5b176891388c64e5fed16b65471a6\transformed\navigation-ui-ktx-2.7.7\res
com.example.ma.app-emoji2-views-helper-1.2.0-11 C:\Users\<USER>\.gradle\caches\transforms-4\48c70a7d92348dce59ec3db1b9b9c44c\transformed\emoji2-views-helper-1.2.0\res
com.example.ma.app-viewpager2-1.0.0-12 C:\Users\<USER>\.gradle\caches\transforms-4\49396cd5d753e1375674c109ffee9fac\transformed\viewpager2-1.0.0\res
com.example.ma.app-lifecycle-viewmodel-ktx-2.9.1-13 C:\Users\<USER>\.gradle\caches\transforms-4\4a8ee24aea1d536ebbbbb0d8a287d4ad\transformed\lifecycle-viewmodel-ktx-2.9.1\res
com.example.ma.app-lifecycle-runtime-release-14 C:\Users\<USER>\.gradle\caches\transforms-4\533957e9ae632d38b1c94d9d6b18d949\transformed\lifecycle-runtime-release\res
com.example.ma.app-savedstate-ktx-1.3.0-15 C:\Users\<USER>\.gradle\caches\transforms-4\5352ded26b53aa8d58872eca3a3f2caf\transformed\savedstate-ktx-1.3.0\res
com.example.ma.app-lifecycle-viewmodel-savedstate-release-16 C:\Users\<USER>\.gradle\caches\transforms-4\5b9ae86805fb8e9a37872c18cdecb1f9\transformed\lifecycle-viewmodel-savedstate-release\res
com.example.ma.app-core-1.13.1-17 C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\res
com.example.ma.app-lifecycle-livedata-core-ktx-2.9.1-18 C:\Users\<USER>\.gradle\caches\transforms-4\659ee171d45b834e6a1b7b5004a05c96\transformed\lifecycle-livedata-core-ktx-2.9.1\res
com.example.ma.app-activity-1.8.0-19 C:\Users\<USER>\.gradle\caches\transforms-4\69ba5ce729968e2ee7ab8b82a67c78b0\transformed\activity-1.8.0\res
com.example.ma.app-transition-1.4.1-20 C:\Users\<USER>\.gradle\caches\transforms-4\6c4a549c0fcb5184444ecdbb727c1eff\transformed\transition-1.4.1\res
com.example.ma.app-drawerlayout-1.1.1-21 C:\Users\<USER>\.gradle\caches\transforms-4\6e1412d1cd0514a67971c65dfa4e9b37\transformed\drawerlayout-1.1.1\res
com.example.ma.app-lifecycle-livedata-2.9.1-22 C:\Users\<USER>\.gradle\caches\transforms-4\73ced35e3630413692bdeefc02b556cc\transformed\lifecycle-livedata-2.9.1\res
com.example.ma.app-slidingpanelayout-1.2.0-23 C:\Users\<USER>\.gradle\caches\transforms-4\79149c71739ee85a160c93a31f0ecd88\transformed\slidingpanelayout-1.2.0\res
com.example.ma.app-startup-runtime-1.2.0-24 C:\Users\<USER>\.gradle\caches\transforms-4\7f8d1be861d42d424df615aa0140665d\transformed\startup-runtime-1.2.0\res
com.example.ma.app-navigation-common-ktx-2.7.7-25 C:\Users\<USER>\.gradle\caches\transforms-4\82650470d203ebfdc84f0d1c5df98e37\transformed\navigation-common-ktx-2.7.7\res
com.example.ma.app-room-runtime-2.6.1-26 C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\res
com.example.ma.app-navigation-runtime-2.7.7-27 C:\Users\<USER>\.gradle\caches\transforms-4\83671367fcfe98fd56fcd5d2746e1e44\transformed\navigation-runtime-2.7.7\res
com.example.ma.app-core-runtime-2.2.0-28 C:\Users\<USER>\.gradle\caches\transforms-4\8565accac280c1e6af7afbf161de866c\transformed\core-runtime-2.2.0\res
com.example.ma.app-navigation-fragment-ktx-2.7.7-29 C:\Users\<USER>\.gradle\caches\transforms-4\89bcf65a07ec9edac2e4a98b3cb7a420\transformed\navigation-fragment-ktx-2.7.7\res
com.example.ma.app-fragment-ktx-1.6.2-30 C:\Users\<USER>\.gradle\caches\transforms-4\8c5ca0c8294ac3c5f9289716c5cb0b41\transformed\fragment-ktx-1.6.2\res
com.example.ma.app-profileinstaller-1.4.0-31 C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\res
com.example.ma.app-navigation-runtime-ktx-2.7.7-32 C:\Users\<USER>\.gradle\caches\transforms-4\94ba6f67aa15562c6be1fd96841edb28\transformed\navigation-runtime-ktx-2.7.7\res
com.example.ma.app-window-1.0.0-33 C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\res
com.example.ma.app-lifecycle-process-2.9.1-34 C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\res
com.example.ma.app-emoji2-1.2.0-35 C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\res
com.example.ma.app-room-ktx-2.6.1-36 C:\Users\<USER>\.gradle\caches\transforms-4\b2f8f6e1e47bc61d8d879aad374d5fb0\transformed\room-ktx-2.6.1\res
com.example.ma.app-annotation-experimental-1.4.0-37 C:\Users\<USER>\.gradle\caches\transforms-4\b9a25b670ae2a6b206fd5eae4cf8d211\transformed\annotation-experimental-1.4.0\res
com.example.ma.app-browser-1.8.0-38 C:\Users\<USER>\.gradle\caches\transforms-4\ba4c34321842e3ae1e1f73fa0eba9cee\transformed\browser-1.8.0\res
com.example.ma.app-cardview-1.0.0-39 C:\Users\<USER>\.gradle\caches\transforms-4\c2311b84ee723f76ed8a96457ff3450b\transformed\cardview-1.0.0\res
com.example.ma.app-coordinatorlayout-1.1.0-40 C:\Users\<USER>\.gradle\caches\transforms-4\c47e1afe72ec7f662742705ab85b7e47\transformed\coordinatorlayout-1.1.0\res
com.example.ma.app-lifecycle-viewmodel-release-41 C:\Users\<USER>\.gradle\caches\transforms-4\cc8bf85be3f60a0a37338c6249e32a3d\transformed\lifecycle-viewmodel-release\res
com.example.ma.app-navigation-common-2.7.7-42 C:\Users\<USER>\.gradle\caches\transforms-4\ce9f38721912d7bab32402ab78c7c668\transformed\navigation-common-2.7.7\res
com.example.ma.app-constraintlayout-2.1.4-43 C:\Users\<USER>\.gradle\caches\transforms-4\d9705675c16350e3378da7b5a4167dcb\transformed\constraintlayout-2.1.4\res
com.example.ma.app-core-ktx-1.13.1-44 C:\Users\<USER>\.gradle\caches\transforms-4\db549af82889374bb10b08efc21aee70\transformed\core-ktx-1.13.1\res
com.example.ma.app-circleimageview-3.1.0-45 C:\Users\<USER>\.gradle\caches\transforms-4\e0e018314bb634f1e5e8dd8ca476558b\transformed\circleimageview-3.1.0\res
com.example.ma.app-appcompat-resources-1.6.1-46 C:\Users\<USER>\.gradle\caches\transforms-4\e6dd766b691c07225eceb547e8954604\transformed\appcompat-resources-1.6.1\res
com.example.ma.app-navigation-ui-2.7.7-47 C:\Users\<USER>\.gradle\caches\transforms-4\eb4d761af10aeed51fe1f849fc010c93\transformed\navigation-ui-2.7.7\res
com.example.ma.app-lifecycle-livedata-ktx-2.9.1-48 C:\Users\<USER>\.gradle\caches\transforms-4\ee23681f1ad3785932bc3e77c4fe0319\transformed\lifecycle-livedata-ktx-2.9.1\res
com.example.ma.app-recyclerview-1.1.0-49 C:\Users\<USER>\.gradle\caches\transforms-4\fb0bfaf494db5c944fb65baf2e1eddb3\transformed\recyclerview-1.1.0\res
com.example.ma.app-appcompat-1.6.1-50 C:\Users\<USER>\.gradle\caches\transforms-4\fbf17065ba87837a4f638cc34db08c6d\transformed\appcompat-1.6.1\res
com.example.ma.app-pngs-51 C:\Users\<USER>\AndroidStudioProjects\MA\app\build\generated\res\pngs\debug
com.example.ma.app-resValues-52 C:\Users\<USER>\AndroidStudioProjects\MA\app\build\generated\res\resValues\debug
com.example.ma.app-packageDebugResources-53 C:\Users\<USER>\AndroidStudioProjects\MA\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.ma.app-packageDebugResources-54 C:\Users\<USER>\AndroidStudioProjects\MA\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.ma.app-debug-55 C:\Users\<USER>\AndroidStudioProjects\MA\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.ma.app-debug-56 C:\Users\<USER>\AndroidStudioProjects\MA\app\src\debug\res
com.example.ma.app-main-57 C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\res
