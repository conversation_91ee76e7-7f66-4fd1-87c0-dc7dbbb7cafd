-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:63:9-71:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:67:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:65:13-64
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:66:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:64:13-62
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-75:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-75:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-75:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:1-75:12
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\4d87a905e970bdcf6809765682e792ed\transformed\viewbinding-8.5.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\ce9f38721912d7bab32402ab78c7c668\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\82650470d203ebfdc84f0d1c5df98e37\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\83671367fcfe98fd56fcd5d2746e1e44\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\94ba6f67aa15562c6be1fd96841edb28\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\175452cc7ca4443efe808e6d3551918c\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\89bcf65a07ec9edac2e4a98b3cb7a420\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\3bd5b176891388c64e5fed16b65471a6\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\eb4d761af10aeed51fe1f849fc010c93\transformed\navigation-ui-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-4\0d81e12e2f4d36f2b1cdaf3882f97537\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\d9705675c16350e3378da7b5a4167dcb\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\e6dd766b691c07225eceb547e8954604\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fbf17065ba87837a4f638cc34db08c6d\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:hilt-android:2.51.1] C:\Users\<USER>\.gradle\caches\transforms-4\f1b3888d66d7392dbf176119a98d80fe\transformed\hilt-android-2.51.1\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2f8f6e1e47bc61d8d879aad374d5fb0\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\2fe034d3e5fce316307e3cff25aaa8a0\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\49396cd5d753e1375674c109ffee9fac\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8c5ca0c8294ac3c5f9289716c5cb0b41\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0588c00a6aae7c230ba4096f1634a4f3\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\32619ee7040a86f9cc4da58a341603d8\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\69ba5ce729968e2ee7ab8b82a67c78b0\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\245a6f12d9bb7d6d4ec681c05d22c3b7\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\3297e24f42a8fc46aa558945b8a33631\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\10817ac490f7f7294ea6bcab6f04d926\transformed\realtime-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\16febd1a426b90dfd115083775903ac0\transformed\postgrest-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:2:1-20:12
MERGED from [io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\79149c71739ee85a160c93a31f0ecd88\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba4c34321842e3ae1e1f73fa0eba9cee\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\48c70a7d92348dce59ec3db1b9b9c44c\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1412d1cd0514a67971c65dfa4e9b37\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c47e1afe72ec7f662742705ab85b7e47\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e53bba8c624a089b080514f93740e9ac\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\652f4f983cbb6d657ece6302f4035e31\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ecf99ef46d914aa92f5e63b250832063\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fb0bfaf494db5c944fb65baf2e1eddb3\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\997d06c576199bf2f6eb9440152a0f16\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\2211382b2873ce8b25ba856959965f78\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f373d23a4eda5712146b065d29eaf5d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d5a23e5e557d7d2e6a99baf8a86ab34d\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\6c4a549c0fcb5184444ecdbb727c1eff\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\35391b49d4d5dfb6538f403dfa3cba2f\transformed\supabase-kt-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\af4feac0e8c1c314fa7d6fd6343e04ff\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\cc8bf85be3f60a0a37338c6249e32a3d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\a456f3b2de4961f8a48e5f3374edb918\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\5352ded26b53aa8d58872eca3a3f2caf\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\27e2a12b5011d1f156205bfab33d92c7\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\533957e9ae632d38b1c94d9d6b18d949\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\4a8ee24aea1d536ebbbbb0d8a287d4ad\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\ee23681f1ad3785932bc3e77c4fe0319\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\659ee171d45b834e6a1b7b5004a05c96\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\02c495e1c56cbd289ac6df46f27d8bf2\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\73ced35e3630413692bdeefc02b556cc\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\5b9ae86805fb8e9a37872c18cdecb1f9\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\db549af82889374bb10b08efc21aee70\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b9a25b670ae2a6b206fd5eae4cf8d211\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:17:1-35:12
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b64d4e5461e3797b906c5b59a305645d\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:2:1-7:12
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3cfd406f0226ea0d3f9f087f8b8ef20\transformed\multiplatform-settings-debug\AndroidManifest.xml:2:1-7:12
MERGED from [co.touchlab:kermit-android-debug:2.0.6] C:\Users\<USER>\.gradle\caches\transforms-4\7e06063ea4138ef6cc726d6db2c55662\transformed\kermit-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2ba88f78c55bfd41baf55bd6151fbefa\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [co.touchlab:kermit-core-android-debug:2.0.6] C:\Users\<USER>\.gradle\caches\transforms-4\bc9352e1e469f9b85b961c87f11e85a3\transformed\kermit-core-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\60679dda1318bb901732fa59c5916b31\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2311b84ee723f76ed8a96457ff3450b\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8565accac280c1e6af7afbf161de866c\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\970cb15f4effe73c2108eb2d1dff8799\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\bec46eb47a968a9008c6f6c36a5eed27\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f8d1be861d42d424df615aa0140665d\transformed\startup-runtime-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e9bd4acc4712d60a895a657a2987bdcb\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\302c8494f632b23dbef616612cb5eb23\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\727042c43b5fbd2e982554cb26fe49e4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc92347d1e89cbd49e6074035ad4ae34\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c2b159475fb2f177daaeaa26ce1a90f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3a6c1d1663458cc2dab18561ec284aa2\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0e018314bb634f1e5e8dd8ca476558b\transformed\circleimageview-3.1.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.dagger:dagger-lint-aar:2.51.1] C:\Users\<USER>\.gradle\caches\transforms-4\017f61c1478b0d6961f1b0b7f8306511\transformed\dagger-lint-aar-2.51.1\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:8:5-9:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:9:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:8:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:10:5-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:10:22-73
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:11:5-12:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:11:22-78
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:13:5-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:13:22-62
uses-permission#android.permission.USE_BIOMETRIC
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:14:5-72
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:14:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:15:5-74
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:15:22-71
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:17:5-73:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:17:5-73:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-4\0d81e12e2f4d36f2b1cdaf3882f97537\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-4\0d81e12e2f4d36f2b1cdaf3882f97537\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\d9705675c16350e3378da7b5a4167dcb\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\d9705675c16350e3378da7b5a4167dcb\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:8:5-18:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f8d1be861d42d424df615aa0140665d\transformed\startup-runtime-1.2.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f8d1be861d42d424df615aa0140665d\transformed\startup-runtime-1.2.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\727042c43b5fbd2e982554cb26fe49e4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\727042c43b5fbd2e982554cb26fe49e4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0e018314bb634f1e5e8dd8ca476558b\transformed\circleimageview-3.1.0\AndroidManifest.xml:9:5-20
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0e018314bb634f1e5e8dd8ca476558b\transformed\circleimageview-3.1.0\AndroidManifest.xml:9:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:25:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:23:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:21:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:24:9-56
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:27:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:22:9-45
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:19:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:26:9-40
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:20:9-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:18:9-38
activity#com.example.ma.presentation.auth.LoginActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:30:9-39:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:33:13-49
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:34:13-55
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:32:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:31:13-60
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:35:13-38:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:36:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:36:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:37:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:37:27-74
activity#com.example.ma.presentation.main.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:42:9-45:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:45:13-49
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:44:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:43:13-59
activity#com.example.ma.presentation.profile.ProfileImageActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:48:9-51:52
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:51:13-49
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:50:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:49:13-70
activity#com.example.ma.presentation.test.ConnectionTestActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:54:9-58:50
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:57:13-49
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:58:13-47
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:56:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:55:13-69
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:68:13-70:54
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:70:17-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml:69:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\4d87a905e970bdcf6809765682e792ed\transformed\viewbinding-8.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\4d87a905e970bdcf6809765682e792ed\transformed\viewbinding-8.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\ce9f38721912d7bab32402ab78c7c668\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\ce9f38721912d7bab32402ab78c7c668\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\82650470d203ebfdc84f0d1c5df98e37\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\82650470d203ebfdc84f0d1c5df98e37\transformed\navigation-common-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\83671367fcfe98fd56fcd5d2746e1e44\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\83671367fcfe98fd56fcd5d2746e1e44\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\94ba6f67aa15562c6be1fd96841edb28\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\94ba6f67aa15562c6be1fd96841edb28\transformed\navigation-runtime-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\175452cc7ca4443efe808e6d3551918c\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\175452cc7ca4443efe808e6d3551918c\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\89bcf65a07ec9edac2e4a98b3cb7a420\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-fragment-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\89bcf65a07ec9edac2e4a98b3cb7a420\transformed\navigation-fragment-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\3bd5b176891388c64e5fed16b65471a6\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui-ktx:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\3bd5b176891388c64e5fed16b65471a6\transformed\navigation-ui-ktx-2.7.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\eb4d761af10aeed51fe1f849fc010c93\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\eb4d761af10aeed51fe1f849fc010c93\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-4\0d81e12e2f4d36f2b1cdaf3882f97537\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-4\0d81e12e2f4d36f2b1cdaf3882f97537\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\d9705675c16350e3378da7b5a4167dcb\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\d9705675c16350e3378da7b5a4167dcb\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\e6dd766b691c07225eceb547e8954604\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\e6dd766b691c07225eceb547e8954604\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fbf17065ba87837a4f638cc34db08c6d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fbf17065ba87837a4f638cc34db08c6d\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:hilt-android:2.51.1] C:\Users\<USER>\.gradle\caches\transforms-4\f1b3888d66d7392dbf176119a98d80fe\transformed\hilt-android-2.51.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.51.1] C:\Users\<USER>\.gradle\caches\transforms-4\f1b3888d66d7392dbf176119a98d80fe\transformed\hilt-android-2.51.1\AndroidManifest.xml:18:3-42
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2f8f6e1e47bc61d8d879aad374d5fb0\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\b2f8f6e1e47bc61d8d879aad374d5fb0\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\2fe034d3e5fce316307e3cff25aaa8a0\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\2fe034d3e5fce316307e3cff25aaa8a0\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\49396cd5d753e1375674c109ffee9fac\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\49396cd5d753e1375674c109ffee9fac\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8c5ca0c8294ac3c5f9289716c5cb0b41\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8c5ca0c8294ac3c5f9289716c5cb0b41\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0588c00a6aae7c230ba4096f1634a4f3\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\0588c00a6aae7c230ba4096f1634a4f3\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\32619ee7040a86f9cc4da58a341603d8\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\32619ee7040a86f9cc4da58a341603d8\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\69ba5ce729968e2ee7ab8b82a67c78b0\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\69ba5ce729968e2ee7ab8b82a67c78b0\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\245a6f12d9bb7d6d4ec681c05d22c3b7\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\245a6f12d9bb7d6d4ec681c05d22c3b7\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\3297e24f42a8fc46aa558945b8a33631\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\3297e24f42a8fc46aa558945b8a33631\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\10817ac490f7f7294ea6bcab6f04d926\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:realtime-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\10817ac490f7f7294ea6bcab6f04d926\transformed\realtime-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\16febd1a426b90dfd115083775903ac0\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:postgrest-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\16febd1a426b90dfd115083775903ac0\transformed\postgrest-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:6:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\79149c71739ee85a160c93a31f0ecd88\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\79149c71739ee85a160c93a31f0ecd88\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba4c34321842e3ae1e1f73fa0eba9cee\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba4c34321842e3ae1e1f73fa0eba9cee\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\48c70a7d92348dce59ec3db1b9b9c44c\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\48c70a7d92348dce59ec3db1b9b9c44c\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1412d1cd0514a67971c65dfa4e9b37\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\6e1412d1cd0514a67971c65dfa4e9b37\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c47e1afe72ec7f662742705ab85b7e47\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\c47e1afe72ec7f662742705ab85b7e47\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e53bba8c624a089b080514f93740e9ac\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e53bba8c624a089b080514f93740e9ac\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\652f4f983cbb6d657ece6302f4035e31\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\652f4f983cbb6d657ece6302f4035e31\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ecf99ef46d914aa92f5e63b250832063\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ecf99ef46d914aa92f5e63b250832063\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fb0bfaf494db5c944fb65baf2e1eddb3\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\fb0bfaf494db5c944fb65baf2e1eddb3\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\997d06c576199bf2f6eb9440152a0f16\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\997d06c576199bf2f6eb9440152a0f16\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\2211382b2873ce8b25ba856959965f78\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\2211382b2873ce8b25ba856959965f78\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f373d23a4eda5712146b065d29eaf5d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f373d23a4eda5712146b065d29eaf5d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d5a23e5e557d7d2e6a99baf8a86ab34d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d5a23e5e557d7d2e6a99baf8a86ab34d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\6c4a549c0fcb5184444ecdbb727c1eff\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\6c4a549c0fcb5184444ecdbb727c1eff\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\35391b49d4d5dfb6538f403dfa3cba2f\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [io.github.jan-tennert.supabase:supabase-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\35391b49d4d5dfb6538f403dfa3cba2f\transformed\supabase-kt-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\af4feac0e8c1c314fa7d6fd6343e04ff\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\af4feac0e8c1c314fa7d6fd6343e04ff\transformed\lifecycle-viewmodel-2.9.1\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\cc8bf85be3f60a0a37338c6249e32a3d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\cc8bf85be3f60a0a37338c6249e32a3d\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\a456f3b2de4961f8a48e5f3374edb918\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\a456f3b2de4961f8a48e5f3374edb918\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\5352ded26b53aa8d58872eca3a3f2caf\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\5352ded26b53aa8d58872eca3a3f2caf\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\27e2a12b5011d1f156205bfab33d92c7\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\27e2a12b5011d1f156205bfab33d92c7\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\533957e9ae632d38b1c94d9d6b18d949\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\533957e9ae632d38b1c94d9d6b18d949\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\4a8ee24aea1d536ebbbbb0d8a287d4ad\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\4a8ee24aea1d536ebbbbb0d8a287d4ad\transformed\lifecycle-viewmodel-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\ee23681f1ad3785932bc3e77c4fe0319\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\ee23681f1ad3785932bc3e77c4fe0319\transformed\lifecycle-livedata-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\659ee171d45b834e6a1b7b5004a05c96\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\659ee171d45b834e6a1b7b5004a05c96\transformed\lifecycle-livedata-core-ktx-2.9.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\02c495e1c56cbd289ac6df46f27d8bf2\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\02c495e1c56cbd289ac6df46f27d8bf2\transformed\lifecycle-livedata-core-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\73ced35e3630413692bdeefc02b556cc\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\73ced35e3630413692bdeefc02b556cc\transformed\lifecycle-livedata-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\5b9ae86805fb8e9a37872c18cdecb1f9\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\5b9ae86805fb8e9a37872c18cdecb1f9\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\db549af82889374bb10b08efc21aee70\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\db549af82889374bb10b08efc21aee70\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b9a25b670ae2a6b206fd5eae4cf8d211\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b9a25b670ae2a6b206fd5eae4cf8d211\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:21:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b64d4e5461e3797b906c5b59a305645d\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-coroutines-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b64d4e5461e3797b906c5b59a305645d\transformed\multiplatform-settings-coroutines-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3cfd406f0226ea0d3f9f087f8b8ef20\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [com.russhwolf:multiplatform-settings-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3cfd406f0226ea0d3f9f087f8b8ef20\transformed\multiplatform-settings-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.6] C:\Users\<USER>\.gradle\caches\transforms-4\7e06063ea4138ef6cc726d6db2c55662\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-android-debug:2.0.6] C:\Users\<USER>\.gradle\caches\transforms-4\7e06063ea4138ef6cc726d6db2c55662\transformed\kermit-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2ba88f78c55bfd41baf55bd6151fbefa\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2ba88f78c55bfd41baf55bd6151fbefa\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.6] C:\Users\<USER>\.gradle\caches\transforms-4\bc9352e1e469f9b85b961c87f11e85a3\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [co.touchlab:kermit-core-android-debug:2.0.6] C:\Users\<USER>\.gradle\caches\transforms-4\bc9352e1e469f9b85b961c87f11e85a3\transformed\kermit-core-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\60679dda1318bb901732fa59c5916b31\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\60679dda1318bb901732fa59c5916b31\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2311b84ee723f76ed8a96457ff3450b\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c2311b84ee723f76ed8a96457ff3450b\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8565accac280c1e6af7afbf161de866c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\8565accac280c1e6af7afbf161de866c\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\970cb15f4effe73c2108eb2d1dff8799\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\970cb15f4effe73c2108eb2d1dff8799\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\bec46eb47a968a9008c6f6c36a5eed27\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\bec46eb47a968a9008c6f6c36a5eed27\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f8d1be861d42d424df615aa0140665d\transformed\startup-runtime-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f8d1be861d42d424df615aa0140665d\transformed\startup-runtime-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e9bd4acc4712d60a895a657a2987bdcb\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e9bd4acc4712d60a895a657a2987bdcb\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\302c8494f632b23dbef616612cb5eb23\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\302c8494f632b23dbef616612cb5eb23\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\727042c43b5fbd2e982554cb26fe49e4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\727042c43b5fbd2e982554cb26fe49e4\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc92347d1e89cbd49e6074035ad4ae34\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc92347d1e89cbd49e6074035ad4ae34\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c2b159475fb2f177daaeaa26ce1a90f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c2b159475fb2f177daaeaa26ce1a90f\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3a6c1d1663458cc2dab18561ec284aa2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3a6c1d1663458cc2dab18561ec284aa2\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0e018314bb634f1e5e8dd8ca476558b\transformed\circleimageview-3.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [de.hdodenhof:circleimageview:3.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\e0e018314bb634f1e5e8dd8ca476558b\transformed\circleimageview-3.1.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.dagger:dagger-lint-aar:2.51.1] C:\Users\<USER>\.gradle\caches\transforms-4\017f61c1478b0d6961f1b0b7f8306511\transformed\dagger-lint-aar-2.51.1\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.51.1] C:\Users\<USER>\.gradle\caches\transforms-4\017f61c1478b0d6961f1b0b7f8306511\transformed\dagger-lint-aar-2.51.1\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\MA\app\src\main\AndroidManifest.xml
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\82f6c2975ba73c19deb11e926b91c892\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
provider#androidx.startup.InitializationProvider
ADDED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:9:9-17:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f8d1be861d42d424df615aa0140665d\transformed\startup-runtime-1.2.0\AndroidManifest.xml:24:9-28:34
MERGED from [androidx.startup:startup-runtime:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f8d1be861d42d424df615aa0140665d\transformed\startup-runtime-1.2.0\AndroidManifest.xml:24:9-28:34
	tools:node
		ADDED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:13:13-31
	android:authorities
		ADDED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:11:13-68
	android:exported
		ADDED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:10:13-67
meta-data#io.github.jan.supabase.storage.SupabaseInitializer
ADDED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:14:13-16:52
	android:value
		ADDED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:16:17-49
	android:name
		ADDED from [io.github.jan-tennert.supabase:storage-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\d3cb44e7e5ddca23b2860a21eec94669\transformed\storage-kt-debug\AndroidManifest.xml:15:17-82
meta-data#io.github.jan.supabase.auth.SupabaseInitializer
ADDED from [io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:14:13-16:52
	android:value
		ADDED from [io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:16:17-49
	android:name
		ADDED from [io.github.jan-tennert.supabase:auth-kt-android-debug:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\bde40099c11c69d883888b8462b8d632\transformed\auth-kt-debug\AndroidManifest.xml:15:17-79
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ed1ca57b11d127df7cc87d406892778\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\b088a96ef4c110078c691d6eee2a8258\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\9fbd856524f853e8a10f0445c14b16c8\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.ma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.ma.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-4\625ad6195f2a7f30267b2379d326a89d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#com.russhwolf.settings.SettingsInitializer
ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [com.russhwolf:multiplatform-settings-no-arg-android-debug:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\65c9d866d9d270d45870b1bd929171a3\transformed\multiplatform-settings-no-arg-debug\AndroidManifest.xml:30:17-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\920e1d2a2b568e11eabdc27b2934bcab\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
