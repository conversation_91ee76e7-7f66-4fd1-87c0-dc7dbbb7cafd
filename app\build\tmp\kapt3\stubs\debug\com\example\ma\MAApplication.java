package com.example.ma;

@dagger.hilt.android.HiltAndroidApp()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\b\u0010\n\u001a\u00020\u000bH\u0016R\u001e\u0010\u0004\u001a\u00020\u00058\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0006\u0010\u0007\"\u0004\b\b\u0010\t\u00a8\u0006\f"}, d2 = {"Lcom/example/ma/MAApplication;", "Landroid/app/Application;", "<init>", "()V", "analyticsManager", "Lcom/example/ma/utils/AnalyticsManager;", "getAnalyticsManager", "()Lcom/example/ma/utils/AnalyticsManager;", "setAnalyticsManager", "(Lcom/example/ma/utils/AnalyticsManager;)V", "onCreate", "", "app_debug"})
public final class MAApplication extends android.app.Application {
    @javax.inject.Inject()
    public com.example.ma.utils.AnalyticsManager analyticsManager;
    
    public MAApplication() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.utils.AnalyticsManager getAnalyticsManager() {
        return null;
    }
    
    public final void setAnalyticsManager(@org.jetbrains.annotations.NotNull()
    com.example.ma.utils.AnalyticsManager p0) {
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
}