package com.example.ma.core.base;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\b&\u0018\u0000*\b\b\u0000\u0010\u0001*\u00020\u0002*\u0010\b\u0001\u0010\u0003*\n\u0012\u0002\b\u0003\u0012\u0002\b\u00030\u00042\u00020\u0005B\u0007\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u001f\u0010\u0010\u001a\u00028\u00002\u0006\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0014H$\u00a2\u0006\u0002\u0010\u0015J$\u0010\u0016\u001a\u00020\u00172\u0006\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u00142\b\u0010\u0018\u001a\u0004\u0018\u00010\u0019H\u0016J\u001a\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u00172\b\u0010\u0018\u001a\u0004\u0018\u00010\u0019H\u0016J\b\u0010\u001d\u001a\u00020\u001bH\u0014J\b\u0010\u001e\u001a\u00020\u001bH\u0014J\b\u0010\u001f\u001a\u00020\u001bH\u0014J\b\u0010 \u001a\u00020\u001bH\u0002J\b\u0010!\u001a\u00020\u001bH\u0002J\u0010\u0010\"\u001a\u00020\u001b2\u0006\u0010#\u001a\u00020$H\u0014J\u0010\u0010%\u001a\u00020\u001b2\u0006\u0010&\u001a\u00020\'H\u0014J\u0010\u0010(\u001a\u00020\u001b2\u0006\u0010)\u001a\u00020\'H\u0004JA\u0010*\u001a\u00020\u001b\"\u0004\b\u0002\u0010+*\b\u0012\u0004\u0012\u0002H+0,2\"\u0010-\u001a\u001e\b\u0001\u0012\u0004\u0012\u0002H+\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001b0/\u0012\u0006\u0012\u0004\u0018\u0001000.H\u0004\u00a2\u0006\u0002\u00101J\b\u00102\u001a\u00020\u001bH\u0016R\u0012\u0010\b\u001a\u0004\u0018\u00018\u0000X\u0082\u000e\u00a2\u0006\u0004\n\u0002\u0010\tR\u0014\u0010\n\u001a\u00028\u00008DX\u0084\u0004\u00a2\u0006\u0006\u001a\u0004\b\u000b\u0010\fR\u0012\u0010\r\u001a\u00028\u0001X\u00a4\u0004\u00a2\u0006\u0006\u001a\u0004\b\u000e\u0010\u000f\u00a8\u00063"}, d2 = {"Lcom/example/ma/core/base/BaseFragment;", "VB", "Landroidx/viewbinding/ViewBinding;", "VM", "Lcom/example/ma/core/base/BaseViewModel;", "Landroidx/fragment/app/Fragment;", "<init>", "()V", "_binding", "Landroidx/viewbinding/ViewBinding;", "binding", "getBinding", "()Landroidx/viewbinding/ViewBinding;", "viewModel", "getViewModel", "()Lcom/example/ma/core/base/BaseViewModel;", "getViewBinding", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "(Landroid/view/LayoutInflater;Landroid/view/ViewGroup;)Landroidx/viewbinding/ViewBinding;", "onCreateView", "Landroid/view/View;", "savedInstanceState", "Landroid/os/Bundle;", "onViewCreated", "", "view", "setupViews", "observeViewModel", "setupListeners", "observeLoading", "observeError", "onLoadingChanged", "isLoading", "", "onError", "error", "", "showErrorSnackbar", "message", "collectWithLifecycle", "T", "Lkotlinx/coroutines/flow/StateFlow;", "action", "Lkotlin/Function2;", "Lkotlin/coroutines/Continuation;", "", "(Lkotlinx/coroutines/flow/StateFlow;Lkotlin/jvm/functions/Function2;)V", "onDestroyView", "app_debug"})
public abstract class BaseFragment<VB extends androidx.viewbinding.ViewBinding, VM extends com.example.ma.core.base.BaseViewModel<?, ?>> extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.Nullable()
    private VB _binding;
    
    public BaseFragment() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    protected final VB getBinding() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    protected abstract VM getViewModel();
    
    @org.jetbrains.annotations.NotNull()
    protected abstract VB getViewBinding(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container);
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    protected void setupViews() {
    }
    
    protected void observeViewModel() {
    }
    
    protected void setupListeners() {
    }
    
    private final void observeLoading() {
    }
    
    private final void observeError() {
    }
    
    protected void onLoadingChanged(boolean isLoading) {
    }
    
    protected void onError(@org.jetbrains.annotations.NotNull()
    java.lang.String error) {
    }
    
    protected final void showErrorSnackbar(@org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
    
    protected final <T extends java.lang.Object>void collectWithLifecycle(@org.jetbrains.annotations.NotNull()
    kotlinx.coroutines.flow.StateFlow<? extends T> $this$collectWithLifecycle, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function2<? super T, ? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> action) {
    }
    
    @java.lang.Override()
    public void onDestroyView() {
    }
}