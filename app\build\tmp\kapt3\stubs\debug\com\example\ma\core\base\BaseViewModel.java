package com.example.ma.core.base;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b&\u0018\u0000*\b\b\u0000\u0010\u0001*\u00020\u0002*\b\b\u0001\u0010\u0003*\u00020\u00022\u00020\u0004B\u0007\u00a2\u0006\u0004\b\u0005\u0010\u0006J\r\u0010\u0014\u001a\u00028\u0000H&\u00a2\u0006\u0002\u0010\u0015J!\u0010\u0016\u001a\u00020\u00172\u0017\u0010\u0018\u001a\u0013\u0012\u0004\u0012\u00028\u0000\u0012\u0004\u0012\u00028\u00000\u0019\u00a2\u0006\u0002\b\u001aH\u0004J\u0010\u0010\u001b\u001a\u00020\u00172\u0006\u0010\u001c\u001a\u00020\u000eH\u0004J\u0012\u0010\u001d\u001a\u00020\u00172\b\u0010\u0012\u001a\u0004\u0018\u00010\u0011H\u0004J+\u0010\u001e\u001a\u00020\u00172\u001c\u0010\u001f\u001a\u0018\b\u0001\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00170 \u0012\u0006\u0012\u0004\u0018\u00010\u00020\u0019H\u0004\u00a2\u0006\u0002\u0010!J\u0014\u0010\"\u001a\u00020\u00172\n\u0010\u0012\u001a\u00060#j\u0002`$H\u0014J\u0015\u0010%\u001a\u00020\u00172\u0006\u0010&\u001a\u00028\u0001H&\u00a2\u0006\u0002\u0010\'R\u0014\u0010\u0007\u001a\b\u0012\u0004\u0012\u00028\u00000\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\t\u001a\b\u0012\u0004\u0012\u00028\u00000\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000e0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u000e0\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\fR\u0016\u0010\u0010\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00110\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00110\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\f\u00a8\u0006("}, d2 = {"Lcom/example/ma/core/base/BaseViewModel;", "State", "", "Event", "Landroidx/lifecycle/ViewModel;", "<init>", "()V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "_isLoading", "", "isLoading", "_error", "", "error", "getError", "createInitialState", "()Ljava/lang/Object;", "setState", "", "reduce", "Lkotlin/Function1;", "Lkotlin/ExtensionFunctionType;", "setLoading", "loading", "setError", "launchWithErrorHandling", "block", "Lkotlin/coroutines/Continuation;", "(Lkotlin/jvm/functions/Function1;)V", "handleError", "Ljava/lang/Exception;", "Lkotlin/Exception;", "onEvent", "event", "(Ljava/lang/Object;)V", "app_debug"})
public abstract class BaseViewModel<State extends java.lang.Object, Event extends java.lang.Object> extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<State> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<State> uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> error = null;
    
    public BaseViewModel() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<State> getUiState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract State createInitialState();
    
    protected final void setState(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super State, ? extends State> reduce) {
    }
    
    protected final void setLoading(boolean loading) {
    }
    
    protected final void setError(@org.jetbrains.annotations.Nullable()
    java.lang.String error) {
    }
    
    protected final void launchWithErrorHandling(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super kotlin.coroutines.Continuation<? super kotlin.Unit>, ? extends java.lang.Object> block) {
    }
    
    protected void handleError(@org.jetbrains.annotations.NotNull()
    java.lang.Exception error) {
    }
    
    public abstract void onEvent(@org.jetbrains.annotations.NotNull()
    Event event);
}