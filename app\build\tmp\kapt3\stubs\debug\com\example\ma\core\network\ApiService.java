package com.example.ma.core.network;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\f\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\u0010\u0006\n\u0000\bf\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u0004H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001e\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\b\b\u0001\u0010\t\u001a\u00020\nH\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u001e\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u0004H\u00a7@\u00a2\u0006\u0002\u0010\u0006JR\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00130\u00120\u00032\b\b\u0003\u0010\u0014\u001a\u00020\u00152\b\b\u0003\u0010\u0016\u001a\u00020\u00152\n\b\u0003\u0010\u0017\u001a\u0004\u0018\u00010\u00182\n\b\u0003\u0010\u0019\u001a\u0004\u0018\u00010\u00182\n\b\u0003\u0010\u001a\u001a\u0004\u0018\u00010\u0018H\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u001e\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00130\u00032\b\b\u0001\u0010\u001d\u001a\u00020\u0013H\u00a7@\u00a2\u0006\u0002\u0010\u001eJ(\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00130\u00032\b\b\u0001\u0010 \u001a\u00020\u00182\b\b\u0001\u0010\u001d\u001a\u00020\u0013H\u00a7@\u00a2\u0006\u0002\u0010!J\u001e\u0010\"\u001a\b\u0012\u0004\u0012\u00020\r0\u00032\b\b\u0001\u0010 \u001a\u00020\u0018H\u00a7@\u00a2\u0006\u0002\u0010#J(\u0010$\u001a\b\u0012\u0004\u0012\u00020%0\u00032\b\b\u0001\u0010\u0019\u001a\u00020\u00182\b\b\u0001\u0010\u001a\u001a\u00020\u0018H\u00a7@\u00a2\u0006\u0002\u0010&J4\u0010\'\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0018\u0012\u0004\u0012\u00020)0(0\u00032\b\b\u0001\u0010\u0019\u001a\u00020\u00182\b\b\u0001\u0010\u001a\u001a\u00020\u0018H\u00a7@\u00a2\u0006\u0002\u0010&\u00a8\u0006*\u00c0\u0006\u0003"}, d2 = {"Lcom/example/ma/core/network/ApiService;", "", "signUp", "Lcom/example/ma/core/network/NetworkResult;", "Lcom/example/ma/domain/model/User;", "user", "(Lcom/example/ma/domain/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "signIn", "Lcom/example/ma/core/network/SignInResponse;", "credentials", "Lcom/example/ma/core/network/SignInRequest;", "(Lcom/example/ma/core/network/SignInRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "signOut", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUserProfile", "updateUserProfile", "getTransactions", "", "Lcom/example/ma/domain/model/Transaction;", "page", "", "limit", "category", "", "startDate", "endDate", "(IILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createTransaction", "transaction", "(Lcom/example/ma/domain/model/Transaction;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTransaction", "id", "(Ljava/lang/String;Lcom/example/ma/domain/model/Transaction;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteTransaction", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getFinancialReport", "Lcom/example/ma/domain/model/FinancialReport;", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCategoryReport", "", "", "app_debug"})
public abstract interface ApiService {
    
    @retrofit2.http.POST(value = "auth/signup")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object signUp(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.User user, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.core.network.NetworkResult<com.example.ma.domain.model.User>> $completion);
    
    @retrofit2.http.POST(value = "auth/signin")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object signIn(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.example.ma.core.network.SignInRequest credentials, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.core.network.NetworkResult<com.example.ma.core.network.SignInResponse>> $completion);
    
    @retrofit2.http.POST(value = "auth/signout")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object signOut(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.core.network.NetworkResult<kotlin.Unit>> $completion);
    
    @retrofit2.http.GET(value = "users/profile")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getUserProfile(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.core.network.NetworkResult<com.example.ma.domain.model.User>> $completion);
    
    @retrofit2.http.PUT(value = "users/profile")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateUserProfile(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.User user, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.core.network.NetworkResult<com.example.ma.domain.model.User>> $completion);
    
    @retrofit2.http.GET(value = "transactions")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTransactions(@retrofit2.http.Query(value = "page")
    int page, @retrofit2.http.Query(value = "limit")
    int limit, @retrofit2.http.Query(value = "category")
    @org.jetbrains.annotations.Nullable()
    java.lang.String category, @retrofit2.http.Query(value = "start_date")
    @org.jetbrains.annotations.Nullable()
    java.lang.String startDate, @retrofit2.http.Query(value = "end_date")
    @org.jetbrains.annotations.Nullable()
    java.lang.String endDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.core.network.NetworkResult<java.util.List<com.example.ma.domain.model.Transaction>>> $completion);
    
    @retrofit2.http.POST(value = "transactions")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object createTransaction(@retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.Transaction transaction, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.core.network.NetworkResult<com.example.ma.domain.model.Transaction>> $completion);
    
    @retrofit2.http.PUT(value = "transactions/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateTransaction(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull()
    java.lang.String id, @retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.Transaction transaction, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.core.network.NetworkResult<com.example.ma.domain.model.Transaction>> $completion);
    
    @retrofit2.http.DELETE(value = "transactions/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteTransaction(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.core.network.NetworkResult<kotlin.Unit>> $completion);
    
    @retrofit2.http.GET(value = "reports/financial")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getFinancialReport(@retrofit2.http.Query(value = "start_date")
    @org.jetbrains.annotations.NotNull()
    java.lang.String startDate, @retrofit2.http.Query(value = "end_date")
    @org.jetbrains.annotations.NotNull()
    java.lang.String endDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.core.network.NetworkResult<com.example.ma.domain.model.FinancialReport>> $completion);
    
    @retrofit2.http.GET(value = "reports/categories")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCategoryReport(@retrofit2.http.Query(value = "start_date")
    @org.jetbrains.annotations.NotNull()
    java.lang.String startDate, @retrofit2.http.Query(value = "end_date")
    @org.jetbrains.annotations.NotNull()
    java.lang.String endDate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.core.network.NetworkResult<java.util.Map<java.lang.String, java.lang.Double>>> $completion);
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}