package com.example.ma.core.utils;

@kotlin.Metadata(mv = {2, 2, 0}, k = 2, xi = 48, d1 = {"\u0000h\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010 \n\u0002\b\u0005\n\u0002\u0010\u001e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\u001c\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u0006\u001a\u0012\u0010\u0007\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u0004\u001a\n\u0010\b\u001a\u00020\u0001*\u00020\t\u001a\n\u0010\n\u001a\u00020\u0001*\u00020\t\u001a\n\u0010\u000b\u001a\u00020\u0001*\u00020\t\u001a\n\u0010\f\u001a\u00020\r*\u00020\t\u001a\n\u0010\u000e\u001a\u00020\r*\u00020\t\u001a\n\u0010\u000f\u001a\u00020\r*\u00020\t\u001a\u0012\u0010\u0010\u001a\u00020\u0001*\u00020\t2\u0006\u0010\u0011\u001a\u00020\r\u001a\u0012\u0010\u0012\u001a\u00020\u0001*\u00020\t2\u0006\u0010\u0011\u001a\u00020\r\u001a:\u0010\u0013\u001a\u00020\u0001*\u00020\u00142\u0006\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00062\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00042\u0010\b\u0002\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0017\u001a8\u0010\u0018\u001a\u00020\u0001*\u00020\u00142\u0006\u0010\u0003\u001a\u00020\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0015\u001a\u00020\u00042\u0010\b\u0002\u0010\u0016\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0017\u001a\n\u0010\u0019\u001a\u00020\r*\u00020\u0004\u001a\n\u0010\u001a\u001a\u00020\r*\u00020\u0004\u001a\n\u0010\u001b\u001a\u00020\u0004*\u00020\u0004\u001a\u001c\u0010\u001c\u001a\u00020\u0004*\u00020\u00042\u0006\u0010\u001d\u001a\u00020\u00062\b\b\u0002\u0010\u001e\u001a\u00020\u0004\u001a\u0014\u0010\u001f\u001a\u00020\u0004*\u00020 2\b\b\u0002\u0010!\u001a\u00020\u0004\u001a\n\u0010\"\u001a\u00020\u0004*\u00020 \u001a\n\u0010#\u001a\u00020\u0004*\u00020$\u001a\u0014\u0010%\u001a\u00020\u0004*\u00020&2\b\b\u0002\u0010\'\u001a\u00020\u0004\u001a\u0014\u0010(\u001a\u00020\u0004*\u00020&2\b\b\u0002\u0010\'\u001a\u00020\u0004\u001a\u0014\u0010)\u001a\u00020\u0004*\u00020&2\b\b\u0002\u0010\'\u001a\u00020\u0004\u001a\n\u0010*\u001a\u00020\r*\u00020&\u001a\n\u0010+\u001a\u00020\r*\u00020&\u001a\n\u0010,\u001a\u00020\r*\u00020&\u001a\n\u0010-\u001a\u00020\r*\u00020&\u001a\n\u0010.\u001a\u00020\r*\u00020&\u001a%\u0010/\u001a\u0004\u0018\u0001H0\"\u0004\b\u0000\u00100*\b\u0012\u0004\u0012\u0002H0012\u0006\u00102\u001a\u00020\u0006\u00a2\u0006\u0002\u00103\u001a%\u00104\u001a\u0004\u0018\u0001H0\"\u0004\b\u0000\u00100*\b\u0012\u0004\u0012\u0002H0012\u0006\u00102\u001a\u00020\u0006\u00a2\u0006\u0002\u00103\u001a\u0016\u00105\u001a\u00020\r\"\u0004\b\u0000\u00100*\b\u0012\u0004\u0012\u0002H001\u001a\u0016\u00106\u001a\u00020\r\"\u0004\b\u0000\u00100*\b\u0012\u0004\u0012\u0002H001\u001a\u0018\u00106\u001a\u00020\r\"\u0004\b\u0000\u00100*\n\u0012\u0004\u0012\u0002H0\u0018\u000107\u001a\u0018\u00108\u001a\u00020\r\"\u0004\b\u0000\u00100*\n\u0012\u0004\u0012\u0002H0\u0018\u000107\u001a\n\u00109\u001a\u00020\u0006*\u00020\r\u001a\n\u0010:\u001a\u00020\u0006*\u00020\r\u001a\u0012\u0010;\u001a\u00020\u0006*\u00020\u00022\u0006\u0010<\u001a\u00020\u0006\u001a\u0014\u0010=\u001a\u0004\u0018\u00010>*\u00020\u00022\u0006\u0010?\u001a\u00020\u0006\u001a\n\u0010@\u001a\u00020\u0004*\u00020$\u001a\n\u0010@\u001a\u00020\u0004*\u00020&\u00a8\u0006A"}, d2 = {"showToast", "", "Landroid/content/Context;", "message", "", "duration", "", "showLongToast", "show", "Landroid/view/View;", "hide", "invisible", "isVisible", "", "isHidden", "isInvisible", "showIf", "condition", "hideIf", "showSnackbar", "Landroidx/fragment/app/Fragment;", "actionText", "action", "Lkotlin/Function0;", "showErrorSnackbar", "isValidEmail", "isValidPhone", "capitalizeWords", "truncate", "maxLength", "suffix", "formatCurrency", "", "currencyCode", "formatPercentage", "formatFileSize", "", "formatDate", "Ljava/util/Date;", "pattern", "formatTime", "formatDateTime", "isToday", "isYesterday", "isThisWeek", "isThisMonth", "isThisYear", "safeGet", "T", "", "index", "(Ljava/util/List;I)Ljava/lang/Object;", "getOrNull", "isNotEmptyOrNull", "isNullOrEmpty", "", "isNotNullOrEmpty", "toVisibility", "toInverseVisibility", "getColorCompat", "colorRes", "getDrawableCompat", "Landroid/graphics/drawable/Drawable;", "drawableRes", "formatRelativeTime", "app_debug"})
public final class ExtensionsKt {
    
    public static final void showToast(@org.jetbrains.annotations.NotNull()
    android.content.Context $this$showToast, @org.jetbrains.annotations.NotNull()
    java.lang.String message, int duration) {
    }
    
    public static final void showLongToast(@org.jetbrains.annotations.NotNull()
    android.content.Context $this$showLongToast, @org.jetbrains.annotations.NotNull()
    java.lang.String message) {
    }
    
    public static final void show(@org.jetbrains.annotations.NotNull()
    android.view.View $this$show) {
    }
    
    public static final void hide(@org.jetbrains.annotations.NotNull()
    android.view.View $this$hide) {
    }
    
    public static final void invisible(@org.jetbrains.annotations.NotNull()
    android.view.View $this$invisible) {
    }
    
    public static final boolean isVisible(@org.jetbrains.annotations.NotNull()
    android.view.View $this$isVisible) {
        return false;
    }
    
    public static final boolean isHidden(@org.jetbrains.annotations.NotNull()
    android.view.View $this$isHidden) {
        return false;
    }
    
    public static final boolean isInvisible(@org.jetbrains.annotations.NotNull()
    android.view.View $this$isInvisible) {
        return false;
    }
    
    public static final void showIf(@org.jetbrains.annotations.NotNull()
    android.view.View $this$showIf, boolean condition) {
    }
    
    public static final void hideIf(@org.jetbrains.annotations.NotNull()
    android.view.View $this$hideIf, boolean condition) {
    }
    
    public static final void showSnackbar(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showSnackbar, @org.jetbrains.annotations.NotNull()
    java.lang.String message, int duration, @org.jetbrains.annotations.Nullable()
    java.lang.String actionText, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> action) {
    }
    
    public static final void showErrorSnackbar(@org.jetbrains.annotations.NotNull()
    androidx.fragment.app.Fragment $this$showErrorSnackbar, @org.jetbrains.annotations.NotNull()
    java.lang.String message, int duration, @org.jetbrains.annotations.NotNull()
    java.lang.String actionText, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> action) {
    }
    
    public static final boolean isValidEmail(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$isValidEmail) {
        return false;
    }
    
    public static final boolean isValidPhone(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$isValidPhone) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String capitalizeWords(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$capitalizeWords) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String truncate(@org.jetbrains.annotations.NotNull()
    java.lang.String $this$truncate, int maxLength, @org.jetbrains.annotations.NotNull()
    java.lang.String suffix) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatCurrency(double $this$formatCurrency, @org.jetbrains.annotations.NotNull()
    java.lang.String currencyCode) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatPercentage(double $this$formatPercentage) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatFileSize(long $this$formatFileSize) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatDate(@org.jetbrains.annotations.NotNull()
    java.util.Date $this$formatDate, @org.jetbrains.annotations.NotNull()
    java.lang.String pattern) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatTime(@org.jetbrains.annotations.NotNull()
    java.util.Date $this$formatTime, @org.jetbrains.annotations.NotNull()
    java.lang.String pattern) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatDateTime(@org.jetbrains.annotations.NotNull()
    java.util.Date $this$formatDateTime, @org.jetbrains.annotations.NotNull()
    java.lang.String pattern) {
        return null;
    }
    
    public static final boolean isToday(@org.jetbrains.annotations.NotNull()
    java.util.Date $this$isToday) {
        return false;
    }
    
    public static final boolean isYesterday(@org.jetbrains.annotations.NotNull()
    java.util.Date $this$isYesterday) {
        return false;
    }
    
    public static final boolean isThisWeek(@org.jetbrains.annotations.NotNull()
    java.util.Date $this$isThisWeek) {
        return false;
    }
    
    public static final boolean isThisMonth(@org.jetbrains.annotations.NotNull()
    java.util.Date $this$isThisMonth) {
        return false;
    }
    
    public static final boolean isThisYear(@org.jetbrains.annotations.NotNull()
    java.util.Date $this$isThisYear) {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final <T extends java.lang.Object>T safeGet(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends T> $this$safeGet, int index) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final <T extends java.lang.Object>T getOrNull(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends T> $this$getOrNull, int index) {
        return null;
    }
    
    public static final <T extends java.lang.Object>boolean isNotEmptyOrNull(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends T> $this$isNotEmptyOrNull) {
        return false;
    }
    
    public static final <T extends java.lang.Object>boolean isNullOrEmpty(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends T> $this$isNullOrEmpty) {
        return false;
    }
    
    public static final <T extends java.lang.Object>boolean isNullOrEmpty(@org.jetbrains.annotations.Nullable()
    java.util.Collection<? extends T> $this$isNullOrEmpty) {
        return false;
    }
    
    public static final <T extends java.lang.Object>boolean isNotNullOrEmpty(@org.jetbrains.annotations.Nullable()
    java.util.Collection<? extends T> $this$isNotNullOrEmpty) {
        return false;
    }
    
    public static final int toVisibility(boolean $this$toVisibility) {
        return 0;
    }
    
    public static final int toInverseVisibility(boolean $this$toInverseVisibility) {
        return 0;
    }
    
    public static final int getColorCompat(@org.jetbrains.annotations.NotNull()
    android.content.Context $this$getColorCompat, int colorRes) {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public static final android.graphics.drawable.Drawable getDrawableCompat(@org.jetbrains.annotations.NotNull()
    android.content.Context $this$getDrawableCompat, int drawableRes) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatRelativeTime(long $this$formatRelativeTime) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String formatRelativeTime(@org.jetbrains.annotations.NotNull()
    java.util.Date $this$formatRelativeTime) {
        return null;
    }
}