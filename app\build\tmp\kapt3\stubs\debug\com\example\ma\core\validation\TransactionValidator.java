package com.example.ma.core.validation;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u000e\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tJ\u001e\u0010\n\u001a\u00020\u00072\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000eJ\u000e\u0010\u0010\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/example/ma/core/validation/TransactionValidator;", "", "validator", "Lcom/example/ma/core/validation/Validator;", "<init>", "(Lcom/example/ma/core/validation/Validator;)V", "validateTransaction", "Lcom/example/ma/core/validation/ValidationResult;", "transaction", "Lcom/example/ma/domain/model/Transaction;", "validateTransactionData", "amount", "", "category", "", "description", "validateRecurringTransaction", "app_debug"})
public final class TransactionValidator {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.core.validation.Validator validator = null;
    
    @javax.inject.Inject()
    public TransactionValidator(@org.jetbrains.annotations.NotNull()
    com.example.ma.core.validation.Validator validator) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validateTransaction(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.Transaction transaction) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validateTransactionData(double amount, @org.jetbrains.annotations.NotNull()
    java.lang.String category, @org.jetbrains.annotations.NotNull()
    java.lang.String description) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validateRecurringTransaction(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.Transaction transaction) {
        return null;
    }
}