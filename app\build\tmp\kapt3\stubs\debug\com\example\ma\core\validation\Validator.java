package com.example.ma.core.validation;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000b\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\t\b\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u000e\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007J\u000e\u0010\b\u001a\u00020\u00052\u0006\u0010\t\u001a\u00020\u0007J\u000e\u0010\n\u001a\u00020\u00052\u0006\u0010\u000b\u001a\u00020\u0007J\u0018\u0010\f\u001a\u00020\u00052\u0006\u0010\r\u001a\u00020\u00072\b\b\u0002\u0010\u000e\u001a\u00020\u0007J\u000e\u0010\u000f\u001a\u00020\u00052\u0006\u0010\u0010\u001a\u00020\u0007J\u000e\u0010\u0011\u001a\u00020\u00052\u0006\u0010\u0012\u001a\u00020\u0013J\u0016\u0010\u0014\u001a\u00020\u00052\u0006\u0010\u0015\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\u0007J&\u0010\u0016\u001a\u00020\u00052\u0006\u0010\u0015\u001a\u00020\u00072\u0006\u0010\u000e\u001a\u00020\u00072\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u0018J\u000e\u0010\u001a\u001a\u00020\u00052\u0006\u0010\u001b\u001a\u00020\u0007J\u0018\u0010\u001c\u001a\u00020\u00052\u0006\u0010\u001d\u001a\u00020\u001e2\b\b\u0002\u0010\u000e\u001a\u00020\u0007\u00a8\u0006\u001f"}, d2 = {"Lcom/example/ma/core/validation/Validator;", "", "<init>", "()V", "validateEmail", "Lcom/example/ma/core/validation/ValidationResult;", "email", "", "validateUsername", "username", "validatePassword", "password", "validateName", "name", "fieldName", "validatePhoneNumber", "phone", "validateAmount", "amount", "", "validateRequiredField", "value", "validateFieldLength", "minLength", "", "maxLength", "validateUrl", "url", "validateDate", "date", "Ljava/util/Date;", "app_debug"})
public final class Validator {
    
    @javax.inject.Inject()
    public Validator() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validateEmail(@org.jetbrains.annotations.NotNull()
    java.lang.String email) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validateUsername(@org.jetbrains.annotations.NotNull()
    java.lang.String username) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validatePassword(@org.jetbrains.annotations.NotNull()
    java.lang.String password) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validateName(@org.jetbrains.annotations.NotNull()
    java.lang.String name, @org.jetbrains.annotations.NotNull()
    java.lang.String fieldName) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validatePhoneNumber(@org.jetbrains.annotations.NotNull()
    java.lang.String phone) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validateAmount(double amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validateRequiredField(@org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    java.lang.String fieldName) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validateFieldLength(@org.jetbrains.annotations.NotNull()
    java.lang.String value, @org.jetbrains.annotations.NotNull()
    java.lang.String fieldName, int minLength, int maxLength) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validateUrl(@org.jetbrains.annotations.NotNull()
    java.lang.String url) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.core.validation.ValidationResult validateDate(@org.jetbrains.annotations.NotNull()
    java.util.Date date, @org.jetbrains.annotations.NotNull()
    java.lang.String fieldName) {
        return null;
    }
}