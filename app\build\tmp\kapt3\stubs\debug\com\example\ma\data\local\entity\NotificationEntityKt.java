package com.example.ma.data.local.entity;

@kotlin.Metadata(mv = {2, 2, 0}, k = 2, xi = 48, d1 = {"\u0000\f\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\u001a\n\u0010\u0000\u001a\u00020\u0001*\u00020\u0002\u00a8\u0006\u0003"}, d2 = {"toEntity", "Lcom/example/ma/data/local/entity/NotificationEntity;", "Lcom/example/ma/domain/model/Notification;", "app_debug"})
public final class NotificationEntityKt {
    
    @org.jetbrains.annotations.NotNull()
    public static final com.example.ma.data.local.entity.NotificationEntity toEntity(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.Notification $this$toEntity) {
        return null;
    }
}