package com.example.ma.data.local.entity;

@kotlin.Metadata(mv = {2, 2, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\n\u0010\u0000\u001a\u00020\u0001*\u00020\u0002\u001a\n\u0010\u0003\u001a\u00020\u0002*\u00020\u0001\u00a8\u0006\u0004"}, d2 = {"toDomain", "Lcom/example/ma/domain/model/Transaction;", "Lcom/example/ma/data/local/entity/TransactionEntity;", "toEntity", "app_debug"})
public final class TransactionEntityKt {
    
    @org.jetbrains.annotations.NotNull()
    public static final com.example.ma.domain.model.Transaction toDomain(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.local.entity.TransactionEntity $this$toDomain) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final com.example.ma.data.local.entity.TransactionEntity toEntity(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.Transaction $this$toEntity) {
        return null;
    }
}