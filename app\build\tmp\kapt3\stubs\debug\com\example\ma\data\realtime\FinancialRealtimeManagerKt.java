package com.example.ma.data.realtime;

@kotlin.Metadata(mv = {2, 2, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0010\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a(\u0010\u0000\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u00042\u0014\u0010\u0005\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\u0007\u0012\u0004\u0012\u00020\u00010\u0006\u001a(\u0010\b\u001a\u00020\u0001*\u00020\u00022\u0006\u0010\u0003\u001a\u00020\u00042\u0014\u0010\u0005\u001a\u0010\u0012\u0006\u0012\u0004\u0018\u00010\t\u0012\u0004\u0012\u00020\u00010\u0006\u00a8\u0006\n"}, d2 = {"observeFinancialSummary", "", "Lcom/example/ma/data/realtime/FinancialRealtimeManager;", "scope", "Lkotlinx/coroutines/CoroutineScope;", "onUpdate", "Lkotlin/Function1;", "Lcom/example/ma/domain/model/BusinessFinancialSummary;", "observeQuickStats", "Lcom/example/ma/domain/model/QuickStats;", "app_debug"})
public final class FinancialRealtimeManagerKt {
    
    /**
     * Extension functions برای راحتی استفاده
     */
    public static final void observeFinancialSummary(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.realtime.FinancialRealtimeManager $this$observeFinancialSummary, @org.jetbrains.annotations.NotNull()
    kotlinx.coroutines.CoroutineScope scope, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.ma.domain.model.BusinessFinancialSummary, kotlin.Unit> onUpdate) {
    }
    
    public static final void observeQuickStats(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.realtime.FinancialRealtimeManager $this$observeQuickStats, @org.jetbrains.annotations.NotNull()
    kotlinx.coroutines.CoroutineScope scope, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.ma.domain.model.QuickStats, kotlin.Unit> onUpdate) {
    }
}