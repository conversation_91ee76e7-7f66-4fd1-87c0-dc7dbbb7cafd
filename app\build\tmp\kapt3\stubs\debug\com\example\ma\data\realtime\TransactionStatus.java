package com.example.ma.data.realtime;

/**
 * وضعیت تراکنش
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\t\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\t\u00a8\u0006\n"}, d2 = {"Lcom/example/ma/data/realtime/TransactionStatus;", "", "<init>", "(Ljava/lang/String;I)V", "PENDING", "APPROVED", "REJECTED", "COMPLETED", "CANCELLED", "FAILED", "app_debug"})
public enum TransactionStatus {
    /*public static final*/ PENDING /* = new PENDING() */,
    /*public static final*/ APPROVED /* = new APPROVED() */,
    /*public static final*/ REJECTED /* = new REJECTED() */,
    /*public static final*/ COMPLETED /* = new COMPLETED() */,
    /*public static final*/ CANCELLED /* = new CANCELLED() */,
    /*public static final*/ FAILED /* = new FAILED() */;
    
    TransactionStatus() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.ma.data.realtime.TransactionStatus> getEntries() {
        return null;
    }
}