package com.example.ma.data.remote;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\r\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J&\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\f\u001a\u00020\b2\u0006\u0010\r\u001a\u00020\bH\u0086@\u00a2\u0006\u0004\b\u000e\u0010\u000fJ\u0016\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\nH\u0086@\u00a2\u0006\u0004\b\u0012\u0010\u0013J\u0018\u0010\u0014\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000b0\nH\u0086@\u00a2\u0006\u0004\b\u0015\u0010\u0013J\u001e\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u000b0\n2\u0006\u0010\u0017\u001a\u00020\u000bH\u0086@\u00a2\u0006\u0004\b\u0018\u0010\u0019J&\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00110\n2\u0006\u0010\u001b\u001a\u00020\b2\u0006\u0010\u001c\u001a\u00020\bH\u0086@\u00a2\u0006\u0004\b\u001d\u0010\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\b\u0012\u0004\u0012\u00020\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001e"}, d2 = {"Lcom/example/ma/data/remote/AuthRemoteDataSource;", "", "supabaseClient", "Lcom/example/ma/data/remote/SupabaseClient;", "<init>", "(Lcom/example/ma/data/remote/SupabaseClient;)V", "authorizedUsers", "", "", "login", "Lkotlin/Result;", "Lcom/example/ma/domain/model/User;", "username", "password", "login-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "logout", "", "logout-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCurrentUser", "getCurrentUser-IoAF18A", "updateProfile", "user", "updateProfile-gIAlu-s", "(Lcom/example/ma/domain/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "changePassword", "oldPassword", "newPassword", "changePassword-0E7RQCE", "app_debug"})
public final class AuthRemoteDataSource {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.data.remote.SupabaseClient supabaseClient = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.Map<java.lang.String, java.lang.String> authorizedUsers = null;
    
    @javax.inject.Inject()
    public AuthRemoteDataSource(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.remote.SupabaseClient supabaseClient) {
        super();
    }
}