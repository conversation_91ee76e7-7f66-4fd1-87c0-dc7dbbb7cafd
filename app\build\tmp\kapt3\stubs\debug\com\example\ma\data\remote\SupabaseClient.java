package com.example.ma.data.remote;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u00002\u00020\u0001B\t\b\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0011\u0010\u0006\u001a\u00020\u00078F\u00a2\u0006\u0006\u001a\u0004\b\b\u0010\tR\u0011\u0010\n\u001a\u00020\u000b8F\u00a2\u0006\u0006\u001a\u0004\b\f\u0010\rR\u0011\u0010\u000e\u001a\u00020\u000f8F\u00a2\u0006\u0006\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0012\u001a\u00020\u00138F\u00a2\u0006\u0006\u001a\u0004\b\u0014\u0010\u0015\u00a8\u0006\u0016"}, d2 = {"Lcom/example/ma/data/remote/SupabaseClient;", "", "<init>", "()V", "client", "Lio/github/jan/supabase/SupabaseClient;", "auth", "Lio/github/jan/supabase/auth/Auth;", "getAuth", "()Lio/github/jan/supabase/auth/Auth;", "database", "Lio/github/jan/supabase/postgrest/Postgrest;", "getDatabase", "()Lio/github/jan/supabase/postgrest/Postgrest;", "realtime", "Lio/github/jan/supabase/realtime/Realtime;", "getRealtime", "()Lio/github/jan/supabase/realtime/Realtime;", "storage", "Lio/github/jan/supabase/storage/Storage;", "getStorage", "()Lio/github/jan/supabase/storage/Storage;", "app_debug"})
public final class SupabaseClient {
    @org.jetbrains.annotations.NotNull()
    private final io.github.jan.supabase.SupabaseClient client = null;
    
    @javax.inject.Inject()
    public SupabaseClient() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final io.github.jan.supabase.auth.Auth getAuth() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final io.github.jan.supabase.postgrest.Postgrest getDatabase() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final io.github.jan.supabase.realtime.Realtime getRealtime() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final io.github.jan.supabase.storage.Storage getStorage() {
        return null;
    }
}