package com.example.ma.data.remote;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010$\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u001e\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u0010\t\u001a\u00020\bH\u0086@\u00a2\u0006\u0004\b\n\u0010\u000bJ \u0010\f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\b0\u00072\u0006\u0010\r\u001a\u00020\u000eH\u0086@\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u0012\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00130\u0012J\u001a\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00130\u00122\u0006\u0010\u0015\u001a\u00020\u0016J\u001a\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00130\u00122\u0006\u0010\u0018\u001a\u00020\u0019J\u001a\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\b0\u00130\u00122\u0006\u0010\u001b\u001a\u00020\u000eJ&\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\b0\u00072\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0018\u001a\u00020\u0019H\u0086@\u00a2\u0006\u0004\b\u001d\u0010\u001eJ\u0016\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020 0\u0007H\u0087@\u00a2\u0006\u0004\b!\u0010\"J\u0016\u0010#\u001a\b\u0012\u0004\u0012\u00020 0\u0007H\u0086@\u00a2\u0006\u0004\b$\u0010\"J\u001e\u0010%\u001a\u00020\b2\u0014\u0010&\u001a\u0010\u0012\u0004\u0012\u00020\u000e\u0012\u0006\u0012\u0004\u0018\u00010\u00010\'H\u0002J\u001a\u0010(\u001a\u0010\u0012\u0004\u0012\u00020\u000e\u0012\u0006\u0012\u0004\u0018\u00010\u00010\'*\u00020\bH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006)"}, d2 = {"Lcom/example/ma/data/remote/TransactionRemoteDataSource;", "", "supabaseClient", "Lcom/example/ma/data/remote/SupabaseClient;", "<init>", "(Lcom/example/ma/data/remote/SupabaseClient;)V", "createTransaction", "Lkotlin/Result;", "Lcom/example/ma/domain/model/Transaction;", "transaction", "createTransaction-gIAlu-s", "(Lcom/example/ma/domain/model/Transaction;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTransactionById", "id", "", "getTransactionById-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllTransactions", "Lkotlinx/coroutines/flow/Flow;", "", "getTransactionsByType", "type", "Lcom/example/ma/domain/model/TransactionType;", "getTransactionsByStatus", "status", "Lcom/example/ma/domain/model/TransactionStatus;", "getTransactionsByUserId", "userId", "updateTransactionStatus", "updateTransactionStatus-0E7RQCE", "(Ljava/lang/String;Lcom/example/ma/domain/model/TransactionStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getFinancialSummary", "Lcom/example/ma/domain/model/FinancialSummary;", "getFinancialSummary-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getFinancialSummaryRpc", "getFinancialSummaryRpc-IoAF18A", "mapToTransaction", "data", "", "toRemoteMap", "app_debug"})
public final class TransactionRemoteDataSource {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.data.remote.SupabaseClient supabaseClient = null;
    
    @javax.inject.Inject()
    public TransactionRemoteDataSource(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.remote.SupabaseClient supabaseClient) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.domain.model.Transaction>> getAllTransactions() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.domain.model.Transaction>> getTransactionsByType(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TransactionType type) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.domain.model.Transaction>> getTransactionsByStatus(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TransactionStatus status) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.domain.model.Transaction>> getTransactionsByUserId(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
        return null;
    }
    
    private final com.example.ma.domain.model.Transaction mapToTransaction(java.util.Map<java.lang.String, ? extends java.lang.Object> data) {
        return null;
    }
    
    private final java.util.Map<java.lang.String, java.lang.Object> toRemoteMap(com.example.ma.domain.model.Transaction $this$toRemoteMap) {
        return null;
    }
}