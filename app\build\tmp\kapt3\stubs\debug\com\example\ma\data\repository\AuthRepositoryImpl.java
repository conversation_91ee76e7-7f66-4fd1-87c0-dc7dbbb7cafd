package com.example.ma.data.repository;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u000e\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J&\u0010\n\u001a\b\u0012\u0004\u0012\u00020\t0\u000b2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\rH\u0096@\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u0016\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\u000bH\u0096@\u00a2\u0006\u0004\b\u0013\u0010\u0014J\u000e\u0010\u0015\u001a\u00020\u0012H\u0096@\u00a2\u0006\u0002\u0010\u0014J\u0016\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\t0\u000bH\u0096@\u00a2\u0006\u0004\b\u0017\u0010\u0014J\u001e\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\t0\u000b2\u0006\u0010\u0019\u001a\u00020\tH\u0096@\u00a2\u0006\u0004\b\u001a\u0010\u001bJ&\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00120\u000b2\u0006\u0010\u001d\u001a\u00020\r2\u0006\u0010\u001e\u001a\u00020\rH\u0096@\u00a2\u0006\u0004\b\u001f\u0010\u0010R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\b\u001a\u0004\u0018\u00010\tX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006 "}, d2 = {"Lcom/example/ma/data/repository/AuthRepositoryImpl;", "Lcom/example/ma/domain/repository/AuthRepository;", "userDao", "Lcom/example/ma/data/local/dao/UserDao;", "remoteDataSource", "Lcom/example/ma/data/remote/AuthRemoteDataSource;", "<init>", "(Lcom/example/ma/data/local/dao/UserDao;Lcom/example/ma/data/remote/AuthRemoteDataSource;)V", "currentUser", "Lcom/example/ma/domain/model/User;", "login", "Lkotlin/Result;", "username", "", "password", "login-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "logout", "", "logout-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isLoggedIn", "getCurrentUser", "getCurrentUser-IoAF18A", "updateProfile", "user", "updateProfile-gIAlu-s", "(Lcom/example/ma/domain/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "changePassword", "oldPassword", "newPassword", "changePassword-0E7RQCE", "app_debug"})
public final class AuthRepositoryImpl implements com.example.ma.domain.repository.AuthRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.data.local.dao.UserDao userDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.data.remote.AuthRemoteDataSource remoteDataSource = null;
    @org.jetbrains.annotations.Nullable()
    private com.example.ma.domain.model.User currentUser;
    
    @javax.inject.Inject()
    public AuthRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.local.dao.UserDao userDao, @org.jetbrains.annotations.NotNull()
    com.example.ma.data.remote.AuthRemoteDataSource remoteDataSource) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object isLoggedIn(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
}