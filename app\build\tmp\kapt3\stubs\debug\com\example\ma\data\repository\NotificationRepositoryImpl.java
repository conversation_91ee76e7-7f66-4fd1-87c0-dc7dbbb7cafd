package com.example.ma.data.repository;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\b\n\u0002\u0010\b\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u0014\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u0007H\u0016J\u001c\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u00072\u0006\u0010\u000b\u001a\u00020\fH\u0016J \u0010\r\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u000e2\u0006\u0010\u000f\u001a\u00020\fH\u0096@\u00a2\u0006\u0004\b\u0010\u0010\u0011J\u001e\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u000e2\u0006\u0010\u0014\u001a\u00020\tH\u0096@\u00a2\u0006\u0004\b\u0015\u0010\u0016J\u001e\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00130\u000e2\u0006\u0010\u000f\u001a\u00020\fH\u0096@\u00a2\u0006\u0004\b\u0018\u0010\u0011J\u001e\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00130\u000e2\u0006\u0010\u000f\u001a\u00020\fH\u0096@\u00a2\u0006\u0004\b\u001a\u0010\u0011J\u0016\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001c0\u000eH\u0096@\u00a2\u0006\u0004\b\u001d\u0010\u001eR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/example/ma/data/repository/NotificationRepositoryImpl;", "Lcom/example/ma/domain/repository/NotificationRepository;", "notificationDao", "Lcom/example/ma/data/local/dao/NotificationDao;", "<init>", "(Lcom/example/ma/data/local/dao/NotificationDao;)V", "getAllNotifications", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/ma/domain/model/Notification;", "getNotificationsByUserId", "userId", "", "getNotificationById", "Lkotlin/Result;", "id", "getNotificationById-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createNotification", "", "notification", "createNotification-gIAlu-s", "(Lcom/example/ma/domain/model/Notification;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markAsRead", "markAsRead-gIAlu-s", "deleteNotification", "deleteNotification-gIAlu-s", "getUnreadCount", "", "getUnreadCount-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class NotificationRepositoryImpl implements com.example.ma.domain.repository.NotificationRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.data.local.dao.NotificationDao notificationDao = null;
    
    @javax.inject.Inject()
    public NotificationRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.local.dao.NotificationDao notificationDao) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.domain.model.Notification>> getAllNotifications() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.domain.model.Notification>> getNotificationsByUserId(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
        return null;
    }
}