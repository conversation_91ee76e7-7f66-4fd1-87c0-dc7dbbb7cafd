package com.example.ma.data.repository;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000j\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010$\n\u0002\u0010\u0006\n\u0002\b\u0012\b\u0007\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0014\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\tH\u0016J\u001e\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r2\u0006\u0010\u000e\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0004\b\u000f\u0010\u0010J\u001e\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000b0\r2\u0006\u0010\u0012\u001a\u00020\u0013H\u0096@\u00a2\u0006\u0004\b\u0014\u0010\u0015J\u001e\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u000b0\r2\u0006\u0010\u000e\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0004\b\u0017\u0010\u0010J\u001e\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00190\r2\u0006\u0010\u0012\u001a\u00020\u0013H\u0096@\u00a2\u0006\u0004\b\u001a\u0010\u0015J\u001c\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\rH\u0096@\u00a2\u0006\u0004\b\u001c\u0010\u001dJ$\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\r2\u0006\u0010\u001f\u001a\u00020 H\u0096@\u00a2\u0006\u0004\b!\u0010\"J$\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\r2\u0006\u0010$\u001a\u00020%H\u0096@\u00a2\u0006\u0004\b&\u0010\'J&\u0010(\u001a\b\u0012\u0004\u0012\u00020\u000b0\r2\u0006\u0010\u0012\u001a\u00020\u00132\u0006\u0010$\u001a\u00020%H\u0096@\u00a2\u0006\u0004\b)\u0010*J\u0016\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00190\rH\u0096@\u00a2\u0006\u0004\b,\u0010\u001dJ\u001c\u0010-\u001a\u00020\u00192\f\u0010.\u001a\b\u0012\u0004\u0012\u00020\u000b0\nH\u0096@\u00a2\u0006\u0002\u0010/J\u000e\u00100\u001a\u00020\u0019H\u0096@\u00a2\u0006\u0002\u0010\u001dJT\u00101\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\r2\u0006\u00102\u001a\u0002032\u0006\u00104\u001a\u0002032\b\u00105\u001a\u0004\u0018\u00010\u00132\b\u0010\u001f\u001a\u0004\u0018\u00010 2\b\u00106\u001a\u0004\u0018\u0001072\b\u00108\u001a\u0004\u0018\u000107H\u0096@\u00a2\u0006\u0004\b9\u0010:J$\u0010;\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\r2\u0006\u0010<\u001a\u00020\u0013H\u0096@\u00a2\u0006\u0004\b=\u0010\u0015J2\u0010>\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0013\u0012\u0004\u0012\u00020@0?0\r2\u0006\u00106\u001a\u0002072\u0006\u00108\u001a\u000207H\u0096@\u00a2\u0006\u0004\bA\u0010BJ,\u0010C\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\r2\u0006\u00106\u001a\u0002072\u0006\u00108\u001a\u000207H\u0096@\u00a2\u0006\u0004\bD\u0010BJ&\u0010E\u001a\b\u0012\u0004\u0012\u00020@0\r2\u0006\u00106\u001a\u0002072\u0006\u00108\u001a\u000207H\u0096@\u00a2\u0006\u0004\bF\u0010BJ&\u0010G\u001a\b\u0012\u0004\u0012\u00020@0\r2\u0006\u00106\u001a\u0002072\u0006\u00108\u001a\u000207H\u0096@\u00a2\u0006\u0004\bH\u0010BJ&\u0010I\u001a\b\u0012\u0004\u0012\u00020@0\r2\u0006\u00106\u001a\u0002072\u0006\u00108\u001a\u000207H\u0096@\u00a2\u0006\u0004\bJ\u0010BJ\u001c\u0010K\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\rH\u0096@\u00a2\u0006\u0004\bL\u0010\u001dJ\u001e\u0010M\u001a\b\u0012\u0004\u0012\u00020\u000b0\r2\u0006\u0010\u000e\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0004\bN\u0010\u0010J\u001e\u0010O\u001a\b\u0012\u0004\u0012\u00020\u000b0\r2\u0006\u0010\u000e\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0004\bP\u0010\u0010J\u0010\u0010Q\u001a\u0004\u0018\u000107H\u0096@\u00a2\u0006\u0002\u0010\u001dR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006R"}, d2 = {"Lcom/example/ma/data/repository/TransactionRepositoryImpl;", "Lcom/example/ma/domain/repository/TransactionRepository;", "localDataSource", "Lcom/example/ma/data/local/dao/TransactionDao;", "remoteDataSource", "Lcom/example/ma/data/remote/TransactionRemoteDataSource;", "<init>", "(Lcom/example/ma/data/local/dao/TransactionDao;Lcom/example/ma/data/remote/TransactionRemoteDataSource;)V", "getTransactionsFlow", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/ma/domain/model/Transaction;", "createTransaction", "Lkotlin/Result;", "transaction", "createTransaction-gIAlu-s", "(Lcom/example/ma/domain/model/Transaction;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTransactionById", "id", "", "getTransactionById-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTransaction", "updateTransaction-gIAlu-s", "deleteTransaction", "", "deleteTransaction-gIAlu-s", "getAllTransactions", "getAllTransactions-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTransactionsByType", "type", "Lcom/example/ma/domain/model/TransactionType;", "getTransactionsByType-gIAlu-s", "(Lcom/example/ma/domain/model/TransactionType;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTransactionsByStatus", "status", "Lcom/example/ma/domain/model/TransactionStatus;", "getTransactionsByStatus-gIAlu-s", "(Lcom/example/ma/domain/model/TransactionStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTransactionStatus", "updateTransactionStatus-0E7RQCE", "(Ljava/lang/String;Lcom/example/ma/domain/model/TransactionStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "syncTransactions", "syncTransactions-IoAF18A", "saveTransactionsLocally", "transactions", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearLocalTransactions", "getTransactions", "page", "", "limit", "category", "startDate", "Ljava/util/Date;", "endDate", "getTransactions-bMdYcbs", "(IILjava/lang/String;Lcom/example/ma/domain/model/TransactionType;Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchTransactions", "query", "searchTransactions-gIAlu-s", "getTransactionsByCategory", "", "", "getTransactionsByCategory-0E7RQCE", "(Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTransactionsByDateRange", "getTransactionsByDateRange-0E7RQCE", "getTotalIncome", "getTotalIncome-0E7RQCE", "getTotalExpense", "getTotalExpense-0E7RQCE", "getNetBalance", "getNetBalance-0E7RQCE", "getRecurringTransactions", "getRecurringTransactions-IoAF18A", "createRecurringTransaction", "createRecurringTransaction-gIAlu-s", "updateRecurringTransaction", "updateRecurringTransaction-gIAlu-s", "getLastSyncTimestamp", "app_debug"})
public final class TransactionRepositoryImpl implements com.example.ma.domain.repository.TransactionRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.data.local.dao.TransactionDao localDataSource = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.data.remote.TransactionRemoteDataSource remoteDataSource = null;
    
    @javax.inject.Inject()
    public TransactionRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.local.dao.TransactionDao localDataSource, @org.jetbrains.annotations.NotNull()
    com.example.ma.data.remote.TransactionRemoteDataSource remoteDataSource) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.domain.model.Transaction>> getTransactionsFlow() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object saveTransactionsLocally(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.ma.domain.model.Transaction> transactions, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object clearLocalTransactions(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getLastSyncTimestamp(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.Date> $completion) {
        return null;
    }
}