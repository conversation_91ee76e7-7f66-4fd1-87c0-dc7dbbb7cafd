package com.example.ma.data.repository;

@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0007\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0016\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0096@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\t0\u000eH\u0096@\u00a2\u0006\u0002\u0010\u000fJ\u001e\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\t0\u00112\u0006\u0010\u0012\u001a\u00020\tH\u0096@\u00a2\u0006\u0004\b\u0013\u0010\u0014J\u0010\u0010\u0015\u001a\u0004\u0018\u00010\tH\u0096@\u00a2\u0006\u0002\u0010\u000fJ\u0014\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\t0\u000eH\u0082@\u00a2\u0006\u0002\u0010\u000fJ\u0010\u0010\u0017\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0018"}, d2 = {"Lcom/example/ma/data/repository/UserRepositoryImpl;", "Lcom/example/ma/domain/repository/UserRepository;", "userDao", "Lcom/example/ma/data/local/dao/UserDao;", "preferencesManager", "Lcom/example/ma/utils/PreferencesManager;", "<init>", "(Lcom/example/ma/data/local/dao/UserDao;Lcom/example/ma/utils/PreferencesManager;)V", "getUserById", "Lcom/example/ma/domain/model/User;", "id", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllUsers", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUser", "Lkotlin/Result;", "user", "updateUser-gIAlu-s", "(Lcom/example/ma/domain/model/User;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCurrentUser", "createDefaultUsers", "createDummyUser", "app_debug"})
public final class UserRepositoryImpl implements com.example.ma.domain.repository.UserRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.data.local.dao.UserDao userDao = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.utils.PreferencesManager preferencesManager = null;
    
    @javax.inject.Inject()
    public UserRepositoryImpl(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.local.dao.UserDao userDao, @org.jetbrains.annotations.NotNull()
    com.example.ma.utils.PreferencesManager preferencesManager) {
        super();
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getUserById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.domain.model.User> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getAllUsers(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.ma.domain.model.User>> $completion) {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object getCurrentUser(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.domain.model.User> $completion) {
        return null;
    }
    
    private final java.lang.Object createDefaultUsers(kotlin.coroutines.Continuation<? super java.util.List<com.example.ma.domain.model.User>> $completion) {
        return null;
    }
    
    private final com.example.ma.domain.model.User createDummyUser(java.lang.String id) {
        return null;
    }
}