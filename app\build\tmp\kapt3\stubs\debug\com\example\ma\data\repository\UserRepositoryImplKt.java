package com.example.ma.data.repository;

@kotlin.Metadata(mv = {2, 2, 0}, k = 2, xi = 48, d1 = {"\u0000\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a\f\u0010\u0000\u001a\u00020\u0001*\u00020\u0002H\u0002\u001a\f\u0010\u0003\u001a\u00020\u0002*\u00020\u0001H\u0002\u00a8\u0006\u0004"}, d2 = {"toEntity", "Lcom/example/ma/data/local/entity/UserEntity;", "Lcom/example/ma/domain/model/User;", "toDomain", "app_debug"})
public final class UserRepositoryImplKt {
    
    private static final com.example.ma.data.local.entity.UserEntity toEntity(com.example.ma.domain.model.User $this$toEntity) {
        return null;
    }
    
    private static final com.example.ma.domain.model.User toDomain(com.example.ma.data.local.entity.UserEntity $this$toDomain) {
        return null;
    }
}