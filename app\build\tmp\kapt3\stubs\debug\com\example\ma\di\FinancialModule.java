package com.example.ma.di;

/**
 * Hilt Module برای محاسبات مالی
 */
@dagger.Module()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u00c7\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0018\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u0007J\u0010\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\u0005H\u0007J\u0010\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0006\u001a\u00020\u0007H\u0007J\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0006\u001a\u00020\u0007H\u0007\u00a8\u0006\u0011"}, d2 = {"Lcom/example/ma/di/FinancialModule;", "", "<init>", "()V", "provideFinancialCalculator", "Lcom/example/ma/domain/calculator/FinancialCalculator;", "transactionRepository", "Lcom/example/ma/domain/repository/TransactionRepository;", "userRepository", "Lcom/example/ma/domain/repository/UserRepository;", "provideGetFinancialSummaryUseCase", "Lcom/example/ma/domain/usecase/GetFinancialSummaryUseCase;", "financialCalculator", "provideGetPartnerBalanceUseCase", "Lcom/example/ma/domain/usecase/GetPartnerBalanceUseCase;", "provideGetQuickStatsUseCase", "Lcom/example/ma/domain/usecase/GetQuickStatsUseCase;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class FinancialModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.ma.di.FinancialModule INSTANCE = null;
    
    private FinancialModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.calculator.FinancialCalculator provideFinancialCalculator(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.TransactionRepository transactionRepository, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.UserRepository userRepository) {
        return null;
    }
    
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.usecase.GetFinancialSummaryUseCase provideGetFinancialSummaryUseCase(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.calculator.FinancialCalculator financialCalculator) {
        return null;
    }
    
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.usecase.GetPartnerBalanceUseCase provideGetPartnerBalanceUseCase(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.TransactionRepository transactionRepository) {
        return null;
    }
    
    @dagger.Provides()
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.usecase.GetQuickStatsUseCase provideGetQuickStatsUseCase(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.TransactionRepository transactionRepository) {
        return null;
    }
}