package com.example.ma.di;

@dagger.Module()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\'\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0010\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\'J\u0010\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\'J\u0010\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fH\'J\u0010\u0010\u0010\u001a\u00020\u00112\u0006\u0010\u0012\u001a\u00020\u0013H\'\u00a8\u0006\u0014"}, d2 = {"Lcom/example/ma/di/RepositoryModule;", "", "<init>", "()V", "bindAuthRepository", "Lcom/example/ma/domain/repository/AuthRepository;", "authRepositoryImpl", "Lcom/example/ma/data/repository/AuthRepositoryImpl;", "bindTransactionRepository", "Lcom/example/ma/domain/repository/TransactionRepository;", "transactionRepositoryImpl", "Lcom/example/ma/data/repository/TransactionRepositoryImpl;", "bindNotificationRepository", "Lcom/example/ma/domain/repository/NotificationRepository;", "notificationRepositoryImpl", "Lcom/example/ma/data/repository/NotificationRepositoryImpl;", "bindUserRepository", "Lcom/example/ma/domain/repository/UserRepository;", "userRepositoryImpl", "Lcom/example/ma/data/repository/UserRepositoryImpl;", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public abstract class RepositoryModule {
    
    public RepositoryModule() {
        super();
    }
    
    @dagger.Binds()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.ma.domain.repository.AuthRepository bindAuthRepository(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.repository.AuthRepositoryImpl authRepositoryImpl);
    
    @dagger.Binds()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.ma.domain.repository.TransactionRepository bindTransactionRepository(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.repository.TransactionRepositoryImpl transactionRepositoryImpl);
    
    @dagger.Binds()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.ma.domain.repository.NotificationRepository bindNotificationRepository(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.repository.NotificationRepositoryImpl notificationRepositoryImpl);
    
    @dagger.Binds()
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.ma.domain.repository.UserRepository bindUserRepository(@org.jetbrains.annotations.NotNull()
    com.example.ma.data.repository.UserRepositoryImpl userRepositoryImpl);
}