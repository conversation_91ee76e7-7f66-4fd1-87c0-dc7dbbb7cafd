package com.example.ma.domain.model;

/**
 * خلاصه مالی کامل کسب‌وکار
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u000b\n\u0002\u0010\t\n\u0002\b#\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001By\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0006\u0012\u0006\u0010\n\u001a\u00020\u0006\u0012\u0006\u0010\u000b\u001a\u00020\u0006\u0012\u0006\u0010\f\u001a\u00020\u0006\u0012\u0006\u0010\r\u001a\u00020\u0006\u0012\u0006\u0010\u000e\u001a\u00020\u0006\u0012\u0006\u0010\u000f\u001a\u00020\u0006\u0012\u0006\u0010\u0010\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\u0004\b\u0013\u0010\u0014J\t\u0010&\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\'\u001a\u00020\u0003H\u00c6\u0003J\t\u0010(\u001a\u00020\u0006H\u00c6\u0003J\t\u0010)\u001a\u00020\u0006H\u00c6\u0003J\t\u0010*\u001a\u00020\u0006H\u00c6\u0003J\t\u0010+\u001a\u00020\u0006H\u00c6\u0003J\t\u0010,\u001a\u00020\u0006H\u00c6\u0003J\t\u0010-\u001a\u00020\u0006H\u00c6\u0003J\t\u0010.\u001a\u00020\u0006H\u00c6\u0003J\t\u0010/\u001a\u00020\u0006H\u00c6\u0003J\t\u00100\u001a\u00020\u0006H\u00c6\u0003J\t\u00101\u001a\u00020\u0006H\u00c6\u0003J\t\u00102\u001a\u00020\u0006H\u00c6\u0003J\t\u00103\u001a\u00020\u0012H\u00c6\u0003J\u0095\u0001\u00104\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\t\u001a\u00020\u00062\b\b\u0002\u0010\n\u001a\u00020\u00062\b\b\u0002\u0010\u000b\u001a\u00020\u00062\b\b\u0002\u0010\f\u001a\u00020\u00062\b\b\u0002\u0010\r\u001a\u00020\u00062\b\b\u0002\u0010\u000e\u001a\u00020\u00062\b\b\u0002\u0010\u000f\u001a\u00020\u00062\b\b\u0002\u0010\u0010\u001a\u00020\u00062\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u00c6\u0001J\u0006\u00105\u001a\u000206J\u0013\u00107\u001a\u0002082\b\u00109\u001a\u0004\u0018\u00010:H\u00d6\u0003J\t\u0010;\u001a\u000206H\u00d6\u0001J\t\u0010<\u001a\u00020=H\u00d6\u0001J\u0016\u0010>\u001a\u00020?2\u0006\u0010@\u001a\u00020A2\u0006\u0010B\u001a\u000206R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0016R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0019R\u0011\u0010\b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0019R\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0019R\u0011\u0010\n\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0019R\u0011\u0010\u000b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0019R\u0011\u0010\f\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0019R\u0011\u0010\r\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0019R\u0011\u0010\u000e\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u0019R\u0011\u0010\u000f\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0019R\u0011\u0010\u0010\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0019R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%\u00a8\u0006C"}, d2 = {"Lcom/example/ma/domain/model/BusinessFinancialSummary;", "Landroid/os/Parcelable;", "partner1Balance", "Lcom/example/ma/domain/model/PartnerBalance;", "partner2Balance", "totalCashInBusiness", "", "totalCardInBusiness", "totalBusinessBalance", "totalSales", "totalCashSales", "totalCardSales", "totalExpenses", "totalWithdrawals", "netProfit", "profitMargin", "cashToCardRatio", "lastUpdated", "", "<init>", "(Lcom/example/ma/domain/model/PartnerBalance;Lcom/example/ma/domain/model/PartnerBalance;DDDDDDDDDDDJ)V", "getPartner1Balance", "()Lcom/example/ma/domain/model/PartnerBalance;", "getPartner2Balance", "getTotalCashInBusiness", "()D", "getTotalCardInBusiness", "getTotalBusinessBalance", "getTotalSales", "getTotalCashSales", "getTotalCardSales", "getTotalExpenses", "getTotalWithdrawals", "getNetProfit", "getProfitMargin", "getCashToCardRatio", "getLastUpdated", "()J", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "component12", "component13", "component14", "copy", "describeContents", "", "equals", "", "other", "", "hashCode", "toString", "", "writeToParcel", "", "dest", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class BusinessFinancialSummary implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.model.PartnerBalance partner1Balance = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.model.PartnerBalance partner2Balance = null;
    private final double totalCashInBusiness = 0.0;
    private final double totalCardInBusiness = 0.0;
    private final double totalBusinessBalance = 0.0;
    private final double totalSales = 0.0;
    private final double totalCashSales = 0.0;
    private final double totalCardSales = 0.0;
    private final double totalExpenses = 0.0;
    private final double totalWithdrawals = 0.0;
    private final double netProfit = 0.0;
    private final double profitMargin = 0.0;
    private final double cashToCardRatio = 0.0;
    private final long lastUpdated = 0L;
    
    /**
     * خلاصه مالی کامل کسب‌وکار
     */
    @java.lang.Override()
    public final int describeContents() {
        return 0;
    }
    
    /**
     * خلاصه مالی کامل کسب‌وکار
     */
    @java.lang.Override()
    public final void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel dest, int flags) {
    }
    
    public BusinessFinancialSummary(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.PartnerBalance partner1Balance, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.PartnerBalance partner2Balance, double totalCashInBusiness, double totalCardInBusiness, double totalBusinessBalance, double totalSales, double totalCashSales, double totalCardSales, double totalExpenses, double totalWithdrawals, double netProfit, double profitMargin, double cashToCardRatio, long lastUpdated) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.PartnerBalance getPartner1Balance() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.PartnerBalance getPartner2Balance() {
        return null;
    }
    
    public final double getTotalCashInBusiness() {
        return 0.0;
    }
    
    public final double getTotalCardInBusiness() {
        return 0.0;
    }
    
    public final double getTotalBusinessBalance() {
        return 0.0;
    }
    
    public final double getTotalSales() {
        return 0.0;
    }
    
    public final double getTotalCashSales() {
        return 0.0;
    }
    
    public final double getTotalCardSales() {
        return 0.0;
    }
    
    public final double getTotalExpenses() {
        return 0.0;
    }
    
    public final double getTotalWithdrawals() {
        return 0.0;
    }
    
    public final double getNetProfit() {
        return 0.0;
    }
    
    public final double getProfitMargin() {
        return 0.0;
    }
    
    public final double getCashToCardRatio() {
        return 0.0;
    }
    
    public final long getLastUpdated() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.PartnerBalance component1() {
        return null;
    }
    
    public final double component10() {
        return 0.0;
    }
    
    public final double component11() {
        return 0.0;
    }
    
    public final double component12() {
        return 0.0;
    }
    
    public final double component13() {
        return 0.0;
    }
    
    public final long component14() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.PartnerBalance component2() {
        return null;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    public final double component6() {
        return 0.0;
    }
    
    public final double component7() {
        return 0.0;
    }
    
    public final double component8() {
        return 0.0;
    }
    
    public final double component9() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.BusinessFinancialSummary copy(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.PartnerBalance partner1Balance, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.PartnerBalance partner2Balance, double totalCashInBusiness, double totalCardInBusiness, double totalBusinessBalance, double totalSales, double totalCashSales, double totalCardSales, double totalExpenses, double totalWithdrawals, double netProfit, double profitMargin, double cashToCardRatio, long lastUpdated) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}