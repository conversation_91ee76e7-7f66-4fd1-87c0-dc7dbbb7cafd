package com.example.ma.domain.model;

/**
 * گزارش نقدی/کارتی
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u001d\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B_\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0003\u0012\u0006\u0010\u0007\u001a\u00020\u0003\u0012\u0006\u0010\b\u001a\u00020\u0003\u0012\u0006\u0010\t\u001a\u00020\u0003\u0012\u0006\u0010\n\u001a\u00020\u0003\u0012\u0006\u0010\u000b\u001a\u00020\u0003\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\r\u00a2\u0006\u0004\b\u000f\u0010\u0010J\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0003H\u00c6\u0003J\t\u0010#\u001a\u00020\u0003H\u00c6\u0003J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\t\u0010%\u001a\u00020\u0003H\u00c6\u0003J\t\u0010&\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\'\u001a\u00020\rH\u00c6\u0003J\t\u0010(\u001a\u00020\rH\u00c6\u0003Jw\u0010)\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00032\b\b\u0002\u0010\b\u001a\u00020\u00032\b\b\u0002\u0010\t\u001a\u00020\u00032\b\b\u0002\u0010\n\u001a\u00020\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00032\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\rH\u00c6\u0001J\u0006\u0010*\u001a\u00020+J\u0013\u0010,\u001a\u00020-2\b\u0010.\u001a\u0004\u0018\u00010/H\u00d6\u0003J\t\u00100\u001a\u00020+H\u00d6\u0001J\t\u00101\u001a\u000202H\u00d6\u0001J\u0016\u00103\u001a\u0002042\u0006\u00105\u001a\u0002062\u0006\u00107\u001a\u00020+R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0012R\u0011\u0010\u0005\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0012R\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0012R\u0011\u0010\u0007\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0012R\u0011\u0010\b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0012R\u0011\u0010\t\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0012R\u0011\u0010\n\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0012R\u0011\u0010\u000b\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0012R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u000e\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001c\u00a8\u00068"}, d2 = {"Lcom/example/ma/domain/model/CashCardReport;", "Landroid/os/Parcelable;", "totalCashSales", "", "totalCardSales", "totalCashWithdrawals", "totalCardWithdrawals", "cashToCardRatio", "partner1CashShare", "partner1CardShare", "partner2CashShare", "partner2CardShare", "cashFlowTrend", "Lcom/example/ma/domain/model/TrendDirection;", "cardFlowTrend", "<init>", "(DDDDDDDDDLcom/example/ma/domain/model/TrendDirection;Lcom/example/ma/domain/model/TrendDirection;)V", "getTotalCashSales", "()D", "getTotalCardSales", "getTotalCashWithdrawals", "getTotalCardWithdrawals", "getCashToCardRatio", "getPartner1CashShare", "getPartner1CardShare", "getPartner2CashShare", "getPartner2CardShare", "getCashFlowTrend", "()Lcom/example/ma/domain/model/TrendDirection;", "getCardFlowTrend", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "copy", "describeContents", "", "equals", "", "other", "", "hashCode", "toString", "", "writeToParcel", "", "dest", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class CashCardReport implements android.os.Parcelable {
    private final double totalCashSales = 0.0;
    private final double totalCardSales = 0.0;
    private final double totalCashWithdrawals = 0.0;
    private final double totalCardWithdrawals = 0.0;
    private final double cashToCardRatio = 0.0;
    private final double partner1CashShare = 0.0;
    private final double partner1CardShare = 0.0;
    private final double partner2CashShare = 0.0;
    private final double partner2CardShare = 0.0;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.model.TrendDirection cashFlowTrend = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.model.TrendDirection cardFlowTrend = null;
    
    /**
     * گزارش نقدی/کارتی
     */
    @java.lang.Override()
    public final int describeContents() {
        return 0;
    }
    
    /**
     * گزارش نقدی/کارتی
     */
    @java.lang.Override()
    public final void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel dest, int flags) {
    }
    
    public CashCardReport(double totalCashSales, double totalCardSales, double totalCashWithdrawals, double totalCardWithdrawals, double cashToCardRatio, double partner1CashShare, double partner1CardShare, double partner2CashShare, double partner2CardShare, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TrendDirection cashFlowTrend, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TrendDirection cardFlowTrend) {
        super();
    }
    
    public final double getTotalCashSales() {
        return 0.0;
    }
    
    public final double getTotalCardSales() {
        return 0.0;
    }
    
    public final double getTotalCashWithdrawals() {
        return 0.0;
    }
    
    public final double getTotalCardWithdrawals() {
        return 0.0;
    }
    
    public final double getCashToCardRatio() {
        return 0.0;
    }
    
    public final double getPartner1CashShare() {
        return 0.0;
    }
    
    public final double getPartner1CardShare() {
        return 0.0;
    }
    
    public final double getPartner2CashShare() {
        return 0.0;
    }
    
    public final double getPartner2CardShare() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.TrendDirection getCashFlowTrend() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.TrendDirection getCardFlowTrend() {
        return null;
    }
    
    public final double component1() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.TrendDirection component10() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.TrendDirection component11() {
        return null;
    }
    
    public final double component2() {
        return 0.0;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    public final double component6() {
        return 0.0;
    }
    
    public final double component7() {
        return 0.0;
    }
    
    public final double component8() {
        return 0.0;
    }
    
    public final double component9() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.CashCardReport copy(double totalCashSales, double totalCardSales, double totalCashWithdrawals, double totalCardWithdrawals, double cashToCardRatio, double partner1CashShare, double partner1CardShare, double partner2CashShare, double partner2CardShare, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TrendDirection cashFlowTrend, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TrendDirection cardFlowTrend) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}