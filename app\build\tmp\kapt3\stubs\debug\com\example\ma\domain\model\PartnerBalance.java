package com.example.ma.domain.model;

/**
 * مدل ترازنامه یک شریک بر اساس منطق حسابداری جدید
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0015\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B7\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\u0006\u00a2\u0006\u0004\b\n\u0010\u000bJ\t\u0010\u0014\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0016\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0006H\u00c6\u0003JE\u0010\u001a\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\t\u001a\u00020\u0006H\u00c6\u0001J\u0006\u0010\u001b\u001a\u00020\u001cJ\u0013\u0010\u001d\u001a\u00020\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010 H\u00d6\u0003J\t\u0010!\u001a\u00020\u001cH\u00d6\u0001J\t\u0010\"\u001a\u00020\u0003H\u00d6\u0001J\u0016\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020&2\u0006\u0010\'\u001a\u00020\u001cR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\rR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010R\u0011\u0010\b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010\u00a8\u0006("}, d2 = {"Lcom/example/ma/domain/model/PartnerBalance;", "Landroid/os/Parcelable;", "partnerId", "", "partnerName", "totalBalance", "", "totalCapitalContribution", "totalWithdrawals", "profitShare", "<init>", "(Ljava/lang/String;Ljava/lang/String;DDDD)V", "getPartnerId", "()Ljava/lang/String;", "getPartnerName", "getTotalBalance", "()D", "getTotalCapitalContribution", "getTotalWithdrawals", "getProfitShare", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "describeContents", "", "equals", "", "other", "", "hashCode", "toString", "writeToParcel", "", "dest", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class PartnerBalance implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String partnerId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String partnerName = null;
    private final double totalBalance = 0.0;
    private final double totalCapitalContribution = 0.0;
    private final double totalWithdrawals = 0.0;
    private final double profitShare = 0.0;
    
    /**
     * مدل ترازنامه یک شریک بر اساس منطق حسابداری جدید
     */
    @java.lang.Override()
    public final int describeContents() {
        return 0;
    }
    
    /**
     * مدل ترازنامه یک شریک بر اساس منطق حسابداری جدید
     */
    @java.lang.Override()
    public final void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel dest, int flags) {
    }
    
    public PartnerBalance(@org.jetbrains.annotations.NotNull()
    java.lang.String partnerId, @org.jetbrains.annotations.NotNull()
    java.lang.String partnerName, double totalBalance, double totalCapitalContribution, double totalWithdrawals, double profitShare) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPartnerId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPartnerName() {
        return null;
    }
    
    public final double getTotalBalance() {
        return 0.0;
    }
    
    public final double getTotalCapitalContribution() {
        return 0.0;
    }
    
    public final double getTotalWithdrawals() {
        return 0.0;
    }
    
    public final double getProfitShare() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    public final double component6() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.PartnerBalance copy(@org.jetbrains.annotations.NotNull()
    java.lang.String partnerId, @org.jetbrains.annotations.NotNull()
    java.lang.String partnerName, double totalBalance, double totalCapitalContribution, double totalWithdrawals, double profitShare) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}