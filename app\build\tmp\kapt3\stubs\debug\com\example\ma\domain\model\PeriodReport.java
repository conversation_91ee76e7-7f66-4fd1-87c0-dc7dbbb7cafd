package com.example.ma.domain.model;

/**
 * گزارش دوره‌ای
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b \n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B_\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\b\u0012\u0006\u0010\n\u001a\u00020\b\u0012\u0006\u0010\u000b\u001a\u00020\b\u0012\u0006\u0010\f\u001a\u00020\b\u0012\u0006\u0010\r\u001a\u00020\u000e\u0012\u0006\u0010\u000f\u001a\u00020\b\u0012\u0006\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\u0004\b\u0012\u0010\u0013J\t\u0010$\u001a\u00020\u0003H\u00c6\u0003J\t\u0010%\u001a\u00020\u0005H\u00c6\u0003J\t\u0010&\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\'\u001a\u00020\bH\u00c6\u0003J\t\u0010(\u001a\u00020\bH\u00c6\u0003J\t\u0010)\u001a\u00020\bH\u00c6\u0003J\t\u0010*\u001a\u00020\bH\u00c6\u0003J\t\u0010+\u001a\u00020\bH\u00c6\u0003J\t\u0010,\u001a\u00020\u000eH\u00c6\u0003J\t\u0010-\u001a\u00020\bH\u00c6\u0003J\t\u0010.\u001a\u00020\u0011H\u00c6\u0003Jw\u0010/\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\b2\b\b\u0002\u0010\n\u001a\u00020\b2\b\b\u0002\u0010\u000b\u001a\u00020\b2\b\b\u0002\u0010\f\u001a\u00020\b2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\b2\b\b\u0002\u0010\u0010\u001a\u00020\u0011H\u00c6\u0001J\u0006\u00100\u001a\u00020\u000eJ\u0013\u00101\u001a\u0002022\b\u00103\u001a\u0004\u0018\u000104H\u00d6\u0003J\t\u00105\u001a\u00020\u000eH\u00d6\u0001J\t\u00106\u001a\u00020\u0003H\u00d6\u0001J\u0016\u00107\u001a\u0002082\u0006\u00109\u001a\u00020:2\u0006\u0010;\u001a\u00020\u000eR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0017R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\t\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001aR\u0011\u0010\n\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001aR\u0011\u0010\u000b\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001aR\u0011\u0010\f\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001aR\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010 R\u0011\u0010\u000f\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\u001aR\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#\u00a8\u0006<"}, d2 = {"Lcom/example/ma/domain/model/PeriodReport;", "Landroid/os/Parcelable;", "period", "", "startDate", "", "endDate", "sales", "", "expenses", "profit", "profitMargin", "growth", "transactionCount", "", "averageTransactionAmount", "trend", "Lcom/example/ma/domain/model/TrendDirection;", "<init>", "(Ljava/lang/String;JJDDDDDIDLcom/example/ma/domain/model/TrendDirection;)V", "getPeriod", "()Ljava/lang/String;", "getStartDate", "()J", "getEndDate", "getSales", "()D", "getExpenses", "getProfit", "getProfitMargin", "getGrowth", "getTransactionCount", "()I", "getAverageTransactionAmount", "getTrend", "()Lcom/example/ma/domain/model/TrendDirection;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "component10", "component11", "copy", "describeContents", "equals", "", "other", "", "hashCode", "toString", "writeToParcel", "", "dest", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class PeriodReport implements android.os.Parcelable {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String period = null;
    private final long startDate = 0L;
    private final long endDate = 0L;
    private final double sales = 0.0;
    private final double expenses = 0.0;
    private final double profit = 0.0;
    private final double profitMargin = 0.0;
    private final double growth = 0.0;
    private final int transactionCount = 0;
    private final double averageTransactionAmount = 0.0;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.model.TrendDirection trend = null;
    
    /**
     * گزارش دوره‌ای
     */
    @java.lang.Override()
    public final int describeContents() {
        return 0;
    }
    
    /**
     * گزارش دوره‌ای
     */
    @java.lang.Override()
    public final void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel dest, int flags) {
    }
    
    public PeriodReport(@org.jetbrains.annotations.NotNull()
    java.lang.String period, long startDate, long endDate, double sales, double expenses, double profit, double profitMargin, double growth, int transactionCount, double averageTransactionAmount, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TrendDirection trend) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPeriod() {
        return null;
    }
    
    public final long getStartDate() {
        return 0L;
    }
    
    public final long getEndDate() {
        return 0L;
    }
    
    public final double getSales() {
        return 0.0;
    }
    
    public final double getExpenses() {
        return 0.0;
    }
    
    public final double getProfit() {
        return 0.0;
    }
    
    public final double getProfitMargin() {
        return 0.0;
    }
    
    public final double getGrowth() {
        return 0.0;
    }
    
    public final int getTransactionCount() {
        return 0;
    }
    
    public final double getAverageTransactionAmount() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.TrendDirection getTrend() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final double component10() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.TrendDirection component11() {
        return null;
    }
    
    public final long component2() {
        return 0L;
    }
    
    public final long component3() {
        return 0L;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    public final double component6() {
        return 0.0;
    }
    
    public final double component7() {
        return 0.0;
    }
    
    public final double component8() {
        return 0.0;
    }
    
    public final int component9() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.PeriodReport copy(@org.jetbrains.annotations.NotNull()
    java.lang.String period, long startDate, long endDate, double sales, double expenses, double profit, double profitMargin, double growth, int transactionCount, double averageTransactionAmount, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TrendDirection trend) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}