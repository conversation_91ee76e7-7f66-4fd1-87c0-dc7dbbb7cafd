package com.example.ma.domain.model;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u000f\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0007H\u00c6\u0003J\'\u0010\u0013\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u00c6\u0001J\u0013\u0010\u0014\u001a\u00020\u00072\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001a"}, d2 = {"Lcom/example/ma/domain/model/PrivacySettings;", "", "profileVisibility", "Lcom/example/ma/domain/model/ProfileVisibility;", "transactionHistory", "Lcom/example/ma/domain/model/TransactionVisibility;", "analyticsSharing", "", "<init>", "(Lcom/example/ma/domain/model/ProfileVisibility;Lcom/example/ma/domain/model/TransactionVisibility;Z)V", "getProfileVisibility", "()Lcom/example/ma/domain/model/ProfileVisibility;", "getTransactionHistory", "()Lcom/example/ma/domain/model/TransactionVisibility;", "getAnalyticsSharing", "()Z", "component1", "component2", "component3", "copy", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
public final class PrivacySettings {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.model.ProfileVisibility profileVisibility = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.model.TransactionVisibility transactionHistory = null;
    private final boolean analyticsSharing = false;
    
    public PrivacySettings(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.ProfileVisibility profileVisibility, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TransactionVisibility transactionHistory, boolean analyticsSharing) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.ProfileVisibility getProfileVisibility() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.TransactionVisibility getTransactionHistory() {
        return null;
    }
    
    public final boolean getAnalyticsSharing() {
        return false;
    }
    
    public PrivacySettings() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.ProfileVisibility component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.TransactionVisibility component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.PrivacySettings copy(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.ProfileVisibility profileVisibility, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TransactionVisibility transactionHistory, boolean analyticsSharing) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}