package com.example.ma.domain.model;

/**
 * فیلتر تراکنش‌ها
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b \n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001Bs\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\n\u00a2\u0006\u0004\b\u0010\u0010\u0011J\u0010\u0010!\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u0010\u0010\"\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u000b\u0010#\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\bH\u00c6\u0003J\u000b\u0010%\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u0010\u0010\'\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u0010\u0010(\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u000b\u0010)\u001a\u0004\u0018\u00010\nH\u00c6\u0003Jz\u0010*\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\nH\u00c6\u0001\u00a2\u0006\u0002\u0010+J\u0006\u0010,\u001a\u00020-J\u0013\u0010.\u001a\u00020/2\b\u00100\u001a\u0004\u0018\u000101H\u00d6\u0003J\t\u00102\u001a\u00020-H\u00d6\u0001J\t\u00103\u001a\u00020\nH\u00d6\u0001J\u0016\u00104\u001a\u0002052\u0006\u00106\u001a\u0002072\u0006\u00108\u001a\u00020-R\u0015\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\n\n\u0002\u0010\u0014\u001a\u0004\b\u0012\u0010\u0013R\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\n\n\u0002\u0010\u0014\u001a\u0004\b\u0015\u0010\u0013R\u0013\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0013\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019R\u0013\u0010\t\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0013\u0010\u000b\u001a\u0004\u0018\u00010\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0015\u0010\r\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\n\n\u0002\u0010\u0014\u001a\u0004\b\u001e\u0010\u0013R\u0015\u0010\u000e\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\n\n\u0002\u0010\u0014\u001a\u0004\b\u001f\u0010\u0013R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\n\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001b\u00a8\u00069"}, d2 = {"Lcom/example/ma/domain/model/TransactionFilter;", "Landroid/os/Parcelable;", "startDate", "", "endDate", "transactionType", "Lcom/example/ma/domain/model/TransactionType;", "paymentMethod", "Lcom/example/ma/domain/model/PaymentMethod;", "partnerId", "", "status", "Lcom/example/ma/domain/model/TransactionStatus;", "minAmount", "maxAmount", "category", "<init>", "(Ljava/lang/Long;Ljava/lang/Long;Lcom/example/ma/domain/model/TransactionType;Lcom/example/ma/domain/model/PaymentMethod;Ljava/lang/String;Lcom/example/ma/domain/model/TransactionStatus;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/String;)V", "getStartDate", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getEndDate", "getTransactionType", "()Lcom/example/ma/domain/model/TransactionType;", "getPaymentMethod", "()Lcom/example/ma/domain/model/PaymentMethod;", "getPartnerId", "()Ljava/lang/String;", "getStatus", "()Lcom/example/ma/domain/model/TransactionStatus;", "getMinAmount", "getMaxAmount", "getCategory", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(Ljava/lang/Long;Ljava/lang/Long;Lcom/example/ma/domain/model/TransactionType;Lcom/example/ma/domain/model/PaymentMethod;Ljava/lang/String;Lcom/example/ma/domain/model/TransactionStatus;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/String;)Lcom/example/ma/domain/model/TransactionFilter;", "describeContents", "", "equals", "", "other", "", "hashCode", "toString", "writeToParcel", "", "dest", "Landroid/os/Parcel;", "flags", "app_debug"})
@kotlinx.parcelize.Parcelize()
public final class TransactionFilter implements android.os.Parcelable {
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long startDate = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long endDate = null;
    @org.jetbrains.annotations.Nullable()
    private final com.example.ma.domain.model.TransactionType transactionType = null;
    @org.jetbrains.annotations.Nullable()
    private final com.example.ma.domain.model.PaymentMethod paymentMethod = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String partnerId = null;
    @org.jetbrains.annotations.Nullable()
    private final com.example.ma.domain.model.TransactionStatus status = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long minAmount = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long maxAmount = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String category = null;
    
    /**
     * فیلتر تراکنش‌ها
     */
    @java.lang.Override()
    public final int describeContents() {
        return 0;
    }
    
    /**
     * فیلتر تراکنش‌ها
     */
    @java.lang.Override()
    public final void writeToParcel(@org.jetbrains.annotations.NotNull()
    android.os.Parcel dest, int flags) {
    }
    
    public TransactionFilter(@org.jetbrains.annotations.Nullable()
    java.lang.Long startDate, @org.jetbrains.annotations.Nullable()
    java.lang.Long endDate, @org.jetbrains.annotations.Nullable()
    com.example.ma.domain.model.TransactionType transactionType, @org.jetbrains.annotations.Nullable()
    com.example.ma.domain.model.PaymentMethod paymentMethod, @org.jetbrains.annotations.Nullable()
    java.lang.String partnerId, @org.jetbrains.annotations.Nullable()
    com.example.ma.domain.model.TransactionStatus status, @org.jetbrains.annotations.Nullable()
    java.lang.Long minAmount, @org.jetbrains.annotations.Nullable()
    java.lang.Long maxAmount, @org.jetbrains.annotations.Nullable()
    java.lang.String category) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getStartDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getEndDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.ma.domain.model.TransactionType getTransactionType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.ma.domain.model.PaymentMethod getPaymentMethod() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getPartnerId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.ma.domain.model.TransactionStatus getStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getMinAmount() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getMaxAmount() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getCategory() {
        return null;
    }
    
    public TransactionFilter() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.ma.domain.model.TransactionType component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.ma.domain.model.PaymentMethod component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.ma.domain.model.TransactionStatus component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.domain.model.TransactionFilter copy(@org.jetbrains.annotations.Nullable()
    java.lang.Long startDate, @org.jetbrains.annotations.Nullable()
    java.lang.Long endDate, @org.jetbrains.annotations.Nullable()
    com.example.ma.domain.model.TransactionType transactionType, @org.jetbrains.annotations.Nullable()
    com.example.ma.domain.model.PaymentMethod paymentMethod, @org.jetbrains.annotations.Nullable()
    java.lang.String partnerId, @org.jetbrains.annotations.Nullable()
    com.example.ma.domain.model.TransactionStatus status, @org.jetbrains.annotations.Nullable()
    java.lang.Long minAmount, @org.jetbrains.annotations.Nullable()
    java.lang.Long maxAmount, @org.jetbrains.annotations.Nullable()
    java.lang.String category) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}