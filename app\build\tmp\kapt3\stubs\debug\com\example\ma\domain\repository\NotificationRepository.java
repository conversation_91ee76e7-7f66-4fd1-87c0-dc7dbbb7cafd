package com.example.ma.domain.repository;

/**
 * Repository interface برای اعلانات
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0002\b\b\n\u0002\u0010\b\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J\u0014\u0010\u0002\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u0003H&J\u001c\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00040\u00032\u0006\u0010\u0007\u001a\u00020\bH&J \u0010\t\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00050\n2\u0006\u0010\u000b\u001a\u00020\bH\u00a6@\u00a2\u0006\u0004\b\f\u0010\rJ\u001e\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\n2\u0006\u0010\u0010\u001a\u00020\u0005H\u00a6@\u00a2\u0006\u0004\b\u0011\u0010\u0012J\u001e\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u000f0\n2\u0006\u0010\u000b\u001a\u00020\bH\u00a6@\u00a2\u0006\u0004\b\u0014\u0010\rJ\u001e\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u000f0\n2\u0006\u0010\u000b\u001a\u00020\bH\u00a6@\u00a2\u0006\u0004\b\u0016\u0010\rJ\u0016\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\nH\u00a6@\u00a2\u0006\u0004\b\u0019\u0010\u001a\u00a8\u0006\u001b\u00c0\u0006\u0003"}, d2 = {"Lcom/example/ma/domain/repository/NotificationRepository;", "", "getAllNotifications", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/ma/domain/model/Notification;", "getNotificationsByUserId", "userId", "", "getNotificationById", "Lkotlin/Result;", "id", "getNotificationById-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createNotification", "", "notification", "createNotification-gIAlu-s", "(Lcom/example/ma/domain/model/Notification;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markAsRead", "markAsRead-gIAlu-s", "deleteNotification", "deleteNotification-gIAlu-s", "getUnreadCount", "", "getUnreadCount-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface NotificationRepository {
    
    /**
     * دریافت تمام اعلانات
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.domain.model.Notification>> getAllNotifications();
    
    /**
     * دریافت اعلانات کاربر
     */
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.domain.model.Notification>> getNotificationsByUserId(@org.jetbrains.annotations.NotNull()
    java.lang.String userId);
}