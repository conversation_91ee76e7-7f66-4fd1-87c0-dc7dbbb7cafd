package com.example.ma.domain.repository;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000b\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010$\n\u0002\u0010\u0006\n\u0002\b\u0011\n\u0002\u0018\u0002\n\u0002\b\b\bf\u0018\u00002\u00020\u0001J\u001e\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u001e\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0004\b\u000b\u0010\fJ\u001e\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0004\b\u000e\u0010\u0007J\u001e\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\u00032\u0006\u0010\t\u001a\u00020\nH\u00a6@\u00a2\u0006\u0004\b\u0011\u0010\fJ\u001c\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00130\u0003H\u00a6@\u00a2\u0006\u0004\b\u0014\u0010\u0015J$\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00130\u00032\u0006\u0010\u0017\u001a\u00020\u0018H\u00a6@\u00a2\u0006\u0004\b\u0019\u0010\u001aJ$\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00130\u00032\u0006\u0010\u001c\u001a\u00020\u001dH\u00a6@\u00a2\u0006\u0004\b\u001e\u0010\u001fJ&\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u001c\u001a\u00020\u001dH\u00a6@\u00a2\u0006\u0004\b!\u0010\"J`\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00130\u00032\b\b\u0002\u0010$\u001a\u00020%2\b\b\u0002\u0010&\u001a\u00020%2\n\b\u0002\u0010\'\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00182\n\b\u0002\u0010(\u001a\u0004\u0018\u00010)2\n\b\u0002\u0010*\u001a\u0004\u0018\u00010)H\u00a6@\u00a2\u0006\u0004\b+\u0010,J$\u0010-\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00130\u00032\u0006\u0010.\u001a\u00020\nH\u00a6@\u00a2\u0006\u0004\b/\u0010\fJ2\u00100\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\n\u0012\u0004\u0012\u000202010\u00032\u0006\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020)H\u00a6@\u00a2\u0006\u0004\b3\u00104J,\u00105\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00130\u00032\u0006\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020)H\u00a6@\u00a2\u0006\u0004\b6\u00104J&\u00107\u001a\b\u0012\u0004\u0012\u0002020\u00032\u0006\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020)H\u00a6@\u00a2\u0006\u0004\b8\u00104J&\u00109\u001a\b\u0012\u0004\u0012\u0002020\u00032\u0006\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020)H\u00a6@\u00a2\u0006\u0004\b:\u00104J&\u0010;\u001a\b\u0012\u0004\u0012\u0002020\u00032\u0006\u0010(\u001a\u00020)2\u0006\u0010*\u001a\u00020)H\u00a6@\u00a2\u0006\u0004\b<\u00104J\u001c\u0010=\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00130\u0003H\u00a6@\u00a2\u0006\u0004\b>\u0010\u0015J\u001e\u0010?\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0004\b@\u0010\u0007J\u001e\u0010A\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u0004H\u00a6@\u00a2\u0006\u0004\bB\u0010\u0007J\u0014\u0010C\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00040\u00130DH&J\u001c\u0010E\u001a\u00020\u00102\f\u0010F\u001a\b\u0012\u0004\u0012\u00020\u00040\u0013H\u00a6@\u00a2\u0006\u0002\u0010GJ\u000e\u0010H\u001a\u00020\u0010H\u00a6@\u00a2\u0006\u0002\u0010\u0015J\u0016\u0010I\u001a\b\u0012\u0004\u0012\u00020\u00100\u0003H\u00a6@\u00a2\u0006\u0004\bJ\u0010\u0015J\u0010\u0010K\u001a\u0004\u0018\u00010)H\u00a6@\u00a2\u0006\u0002\u0010\u0015\u00a8\u0006L\u00c0\u0006\u0003"}, d2 = {"Lcom/example/ma/domain/repository/TransactionRepository;", "", "createTransaction", "Lkotlin/Result;", "Lcom/example/ma/domain/model/Transaction;", "transaction", "createTransaction-gIAlu-s", "(Lcom/example/ma/domain/model/Transaction;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTransactionById", "id", "", "getTransactionById-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTransaction", "updateTransaction-gIAlu-s", "deleteTransaction", "", "deleteTransaction-gIAlu-s", "getAllTransactions", "", "getAllTransactions-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTransactionsByType", "type", "Lcom/example/ma/domain/model/TransactionType;", "getTransactionsByType-gIAlu-s", "(Lcom/example/ma/domain/model/TransactionType;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTransactionsByStatus", "status", "Lcom/example/ma/domain/model/TransactionStatus;", "getTransactionsByStatus-gIAlu-s", "(Lcom/example/ma/domain/model/TransactionStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTransactionStatus", "updateTransactionStatus-0E7RQCE", "(Ljava/lang/String;Lcom/example/ma/domain/model/TransactionStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTransactions", "page", "", "limit", "category", "startDate", "Ljava/util/Date;", "endDate", "getTransactions-bMdYcbs", "(IILjava/lang/String;Lcom/example/ma/domain/model/TransactionType;Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchTransactions", "query", "searchTransactions-gIAlu-s", "getTransactionsByCategory", "", "", "getTransactionsByCategory-0E7RQCE", "(Ljava/util/Date;Ljava/util/Date;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTransactionsByDateRange", "getTransactionsByDateRange-0E7RQCE", "getTotalIncome", "getTotalIncome-0E7RQCE", "getTotalExpense", "getTotalExpense-0E7RQCE", "getNetBalance", "getNetBalance-0E7RQCE", "getRecurringTransactions", "getRecurringTransactions-IoAF18A", "createRecurringTransaction", "createRecurringTransaction-gIAlu-s", "updateRecurringTransaction", "updateRecurringTransaction-gIAlu-s", "getTransactionsFlow", "Lkotlinx/coroutines/flow/Flow;", "saveTransactionsLocally", "transactions", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "clearLocalTransactions", "syncTransactions", "syncTransactions-IoAF18A", "getLastSyncTimestamp", "app_debug"})
public abstract interface TransactionRepository {
    
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.ma.domain.model.Transaction>> getTransactionsFlow();
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object saveTransactionsLocally(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.ma.domain.model.Transaction> transactions, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object clearLocalTransactions(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLastSyncTimestamp(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.Date> $completion);
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}