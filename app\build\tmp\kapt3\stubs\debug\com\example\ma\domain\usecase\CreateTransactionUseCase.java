package com.example.ma.domain.usecase;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J@\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u000e2\b\u0010\u0011\u001a\u0004\u0018\u00010\u000eH\u0086B\u00a2\u0006\u0004\b\u0012\u0010\u0013R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/ma/domain/usecase/CreateTransactionUseCase;", "", "transactionRepository", "Lcom/example/ma/domain/repository/TransactionRepository;", "notificationRepository", "Lcom/example/ma/domain/repository/NotificationRepository;", "<init>", "(Lcom/example/ma/domain/repository/TransactionRepository;Lcom/example/ma/domain/repository/NotificationRepository;)V", "invoke", "Lkotlin/Result;", "", "transaction", "Lcom/example/ma/domain/model/Transaction;", "currentUserId", "", "partnerUserId", "currentUserDisplayName", "currentUserAvatarUrl", "invoke-hUnOzRk", "(Lcom/example/ma/domain/model/Transaction;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class CreateTransactionUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.repository.TransactionRepository transactionRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.repository.NotificationRepository notificationRepository = null;
    
    @javax.inject.Inject()
    public CreateTransactionUseCase(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.TransactionRepository transactionRepository, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.NotificationRepository notificationRepository) {
        super();
    }
}