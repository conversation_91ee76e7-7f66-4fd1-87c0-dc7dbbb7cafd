package com.example.ma.domain.usecase;

/**
 * Use Case برای دریافت آمار سریع
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u0016\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\b0\u0007H\u0086B\u00a2\u0006\u0004\b\t\u0010\nR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000b"}, d2 = {"Lcom/example/ma/domain/usecase/GetQuickStatsUseCase;", "", "transactionRepository", "Lcom/example/ma/domain/repository/TransactionRepository;", "<init>", "(Lcom/example/ma/domain/repository/TransactionRepository;)V", "invoke", "Lkotlin/Result;", "Lcom/example/ma/domain/model/QuickStats;", "invoke-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class GetQuickStatsUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.repository.TransactionRepository transactionRepository = null;
    
    @javax.inject.Inject()
    public GetQuickStatsUseCase(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.TransactionRepository transactionRepository) {
        super();
    }
}