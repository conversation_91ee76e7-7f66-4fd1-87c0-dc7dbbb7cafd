package com.example.ma.domain.usecase.auth;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0003\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J&\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\t2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\fH\u0086B\u00a2\u0006\u0004\b\u000e\u0010\u000fJ\u000e\u0010\u0010\u001a\u00020\u0011H\u0086@\u00a2\u0006\u0002\u0010\u0012J\u0010\u0010\u0013\u001a\u0004\u0018\u00010\nH\u0086@\u00a2\u0006\u0002\u0010\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0014"}, d2 = {"Lcom/example/ma/domain/usecase/auth/SignInUseCase;", "", "authRepository", "Lcom/example/ma/domain/repository/AuthRepository;", "validator", "Lcom/example/ma/core/validation/Validator;", "<init>", "(Lcom/example/ma/domain/repository/AuthRepository;Lcom/example/ma/core/validation/Validator;)V", "invoke", "Lkotlin/Result;", "Lcom/example/ma/domain/model/User;", "username", "", "password", "invoke-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isUserLoggedIn", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCurrentUser", "app_debug"})
public final class SignInUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.repository.AuthRepository authRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.core.validation.Validator validator = null;
    
    @javax.inject.Inject()
    public SignInUseCase(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.AuthRepository authRepository, @org.jetbrains.annotations.NotNull()
    com.example.ma.core.validation.Validator validator) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object isUserLoggedIn(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getCurrentUser(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.domain.model.User> $completion) {
        return null;
    }
}