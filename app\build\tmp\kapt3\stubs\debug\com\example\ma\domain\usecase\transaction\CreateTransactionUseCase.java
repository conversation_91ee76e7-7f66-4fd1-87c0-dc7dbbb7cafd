package com.example.ma.domain.usecase.transaction;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\u0018\u00002\u00020\u0001B!\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ~\u0010\n\u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u00122\b\b\u0002\u0010\u0014\u001a\u00020\u00152\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00122\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00122\u000e\b\u0002\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u00120\u00192\b\b\u0002\u0010\u001a\u001a\u00020\u001b2\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u001dH\u0086B\u00a2\u0006\u0004\b\u001e\u0010\u001fJ\u001e\u0010 \u001a\b\u0012\u0004\u0012\u00020\f0\u000b2\u0006\u0010!\u001a\u00020\fH\u0086@\u00a2\u0006\u0004\b\"\u0010#J&\u0010$\u001a\u00020%2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u00122\u0006\u0010\u0013\u001a\u00020\u0012H\u0086@\u00a2\u0006\u0002\u0010&R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\'"}, d2 = {"Lcom/example/ma/domain/usecase/transaction/CreateTransactionUseCase;", "", "transactionRepository", "Lcom/example/ma/domain/repository/TransactionRepository;", "userRepository", "Lcom/example/ma/domain/repository/UserRepository;", "validator", "Lcom/example/ma/core/validation/TransactionValidator;", "<init>", "(Lcom/example/ma/domain/repository/TransactionRepository;Lcom/example/ma/domain/repository/UserRepository;Lcom/example/ma/core/validation/TransactionValidator;)V", "invoke", "Lkotlin/Result;", "Lcom/example/ma/domain/model/Transaction;", "amount", "", "type", "Lcom/example/ma/domain/model/TransactionType;", "category", "", "description", "date", "Ljava/util/Date;", "subcategory", "location", "tags", "", "isRecurring", "", "recurringPattern", "Lcom/example/ma/domain/model/RecurringPattern;", "invoke-5p_uFSQ", "(DLcom/example/ma/domain/model/TransactionType;Ljava/lang/String;Ljava/lang/String;Ljava/util/Date;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;ZLcom/example/ma/domain/model/RecurringPattern;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createRecurringTransaction", "transaction", "createRecurringTransaction-gIAlu-s", "(Lcom/example/ma/domain/model/Transaction;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "validateTransactionData", "Lcom/example/ma/core/validation/ValidationResult;", "(DLjava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class CreateTransactionUseCase {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.repository.TransactionRepository transactionRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.repository.UserRepository userRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.core.validation.TransactionValidator validator = null;
    
    @javax.inject.Inject()
    public CreateTransactionUseCase(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.TransactionRepository transactionRepository, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.UserRepository userRepository, @org.jetbrains.annotations.NotNull()
    com.example.ma.core.validation.TransactionValidator validator) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object validateTransactionData(double amount, @org.jetbrains.annotations.NotNull()
    java.lang.String category, @org.jetbrains.annotations.NotNull()
    java.lang.String description, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.ma.core.validation.ValidationResult> $completion) {
        return null;
    }
}