package com.example.ma.presentation.adapters;

/**
 * Adapter برای نمایش اعلانات در RecyclerView
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\u0018\u00002\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00030\u0001:\u0002\u0014\u0015BW\u0012\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005\u00a2\u0006\u0004\b\n\u0010\u000bJ\u0018\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u0010H\u0016J\u0018\u0010\u0011\u001a\u00020\u00062\u0006\u0010\u0012\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\u0010H\u0016R\u001a\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00060\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/example/ma/presentation/adapters/NotificationAdapter;", "Landroidx/recyclerview/widget/ListAdapter;", "Lcom/example/ma/domain/model/Notification;", "Lcom/example/ma/presentation/adapters/NotificationAdapter$NotificationViewHolder;", "onNotificationClick", "Lkotlin/Function1;", "", "onMarkAsRead", "onApprove", "onReject", "<init>", "(Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V", "onCreateViewHolder", "parent", "Landroid/view/ViewGroup;", "viewType", "", "onBindViewHolder", "holder", "position", "NotificationViewHolder", "NotificationDiffCallback", "app_debug"})
public final class NotificationAdapter extends androidx.recyclerview.widget.ListAdapter<com.example.ma.domain.model.Notification, com.example.ma.presentation.adapters.NotificationAdapter.NotificationViewHolder> {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.example.ma.domain.model.Notification, kotlin.Unit> onNotificationClick = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.example.ma.domain.model.Notification, kotlin.Unit> onMarkAsRead = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.example.ma.domain.model.Notification, kotlin.Unit> onApprove = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlin.jvm.functions.Function1<com.example.ma.domain.model.Notification, kotlin.Unit> onReject = null;
    
    public NotificationAdapter(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.ma.domain.model.Notification, kotlin.Unit> onNotificationClick, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.ma.domain.model.Notification, kotlin.Unit> onMarkAsRead, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.ma.domain.model.Notification, kotlin.Unit> onApprove, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.ma.domain.model.Notification, kotlin.Unit> onReject) {
        super(null);
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public com.example.ma.presentation.adapters.NotificationAdapter.NotificationViewHolder onCreateViewHolder(@org.jetbrains.annotations.NotNull()
    android.view.ViewGroup parent, int viewType) {
        return null;
    }
    
    @java.lang.Override()
    public void onBindViewHolder(@org.jetbrains.annotations.NotNull()
    com.example.ma.presentation.adapters.NotificationAdapter.NotificationViewHolder holder, int position) {
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0003\u0010\u0004J\u0018\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00022\u0006\u0010\b\u001a\u00020\u0002H\u0016J\u0018\u0010\t\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u00022\u0006\u0010\b\u001a\u00020\u0002H\u0016\u00a8\u0006\n"}, d2 = {"Lcom/example/ma/presentation/adapters/NotificationAdapter$NotificationDiffCallback;", "Landroidx/recyclerview/widget/DiffUtil$ItemCallback;", "Lcom/example/ma/domain/model/Notification;", "<init>", "()V", "areItemsTheSame", "", "oldItem", "newItem", "areContentsTheSame", "app_debug"})
    static final class NotificationDiffCallback extends androidx.recyclerview.widget.DiffUtil.ItemCallback<com.example.ma.domain.model.Notification> {
        
        public NotificationDiffCallback() {
            super();
        }
        
        @java.lang.Override()
        public boolean areItemsTheSame(@org.jetbrains.annotations.NotNull()
        com.example.ma.domain.model.Notification oldItem, @org.jetbrains.annotations.NotNull()
        com.example.ma.domain.model.Notification newItem) {
            return false;
        }
        
        @java.lang.Override()
        public boolean areContentsTheSame(@org.jetbrains.annotations.NotNull()
        com.example.ma.domain.model.Notification oldItem, @org.jetbrains.annotations.NotNull()
        com.example.ma.domain.model.Notification newItem) {
            return false;
        }
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B_\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005\u0012\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005\u0012\u0012\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005\u0012\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005\u00a2\u0006\u0004\b\u000b\u0010\fJ\u000e\u0010\u0019\u001a\u00020\u00072\u0006\u0010\u001a\u001a\u00020\u0006J\u0010\u0010\u001b\u001a\u00020\u001c2\u0006\u0010\u001d\u001a\u00020\u001eH\u0002R\u001a\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\t\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0010X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0016\u001a\u0004\u0018\u00010\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0017X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001f"}, d2 = {"Lcom/example/ma/presentation/adapters/NotificationAdapter$NotificationViewHolder;", "Landroidx/recyclerview/widget/RecyclerView$ViewHolder;", "itemView", "Landroid/view/View;", "onNotificationClick", "Lkotlin/Function1;", "Lcom/example/ma/domain/model/Notification;", "", "onMarkAsRead", "onApprove", "onReject", "<init>", "(Landroid/view/View;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function1;)V", "cardNotification", "Lcom/google/android/material/card/MaterialCardView;", "tvTitle", "Lcom/google/android/material/textview/MaterialTextView;", "tvMessage", "tvDate", "tvType", "Lcom/google/android/material/chip/Chip;", "indicatorUnread", "btnApprove", "Lcom/google/android/material/button/MaterialButton;", "btnReject", "bind", "notification", "getNotificationTypeText", "", "type", "Lcom/example/ma/domain/model/Notification$NotificationType;", "app_debug"})
    public static final class NotificationViewHolder extends androidx.recyclerview.widget.RecyclerView.ViewHolder {
        @org.jetbrains.annotations.NotNull()
        private final kotlin.jvm.functions.Function1<com.example.ma.domain.model.Notification, kotlin.Unit> onNotificationClick = null;
        @org.jetbrains.annotations.NotNull()
        private final kotlin.jvm.functions.Function1<com.example.ma.domain.model.Notification, kotlin.Unit> onMarkAsRead = null;
        @org.jetbrains.annotations.NotNull()
        private final kotlin.jvm.functions.Function1<com.example.ma.domain.model.Notification, kotlin.Unit> onApprove = null;
        @org.jetbrains.annotations.NotNull()
        private final kotlin.jvm.functions.Function1<com.example.ma.domain.model.Notification, kotlin.Unit> onReject = null;
        @org.jetbrains.annotations.NotNull()
        private final com.google.android.material.card.MaterialCardView cardNotification = null;
        @org.jetbrains.annotations.NotNull()
        private final com.google.android.material.textview.MaterialTextView tvTitle = null;
        @org.jetbrains.annotations.NotNull()
        private final com.google.android.material.textview.MaterialTextView tvMessage = null;
        @org.jetbrains.annotations.NotNull()
        private final com.google.android.material.textview.MaterialTextView tvDate = null;
        @org.jetbrains.annotations.NotNull()
        private final com.google.android.material.chip.Chip tvType = null;
        @org.jetbrains.annotations.NotNull()
        private final android.view.View indicatorUnread = null;
        @org.jetbrains.annotations.Nullable()
        private final com.google.android.material.button.MaterialButton btnApprove = null;
        @org.jetbrains.annotations.Nullable()
        private final com.google.android.material.button.MaterialButton btnReject = null;
        
        public NotificationViewHolder(@org.jetbrains.annotations.NotNull()
        android.view.View itemView, @org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function1<? super com.example.ma.domain.model.Notification, kotlin.Unit> onNotificationClick, @org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function1<? super com.example.ma.domain.model.Notification, kotlin.Unit> onMarkAsRead, @org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function1<? super com.example.ma.domain.model.Notification, kotlin.Unit> onApprove, @org.jetbrains.annotations.NotNull()
        kotlin.jvm.functions.Function1<? super com.example.ma.domain.model.Notification, kotlin.Unit> onReject) {
            super(null);
        }
        
        public final void bind(@org.jetbrains.annotations.NotNull()
        com.example.ma.domain.model.Notification notification) {
        }
        
        private final java.lang.String getNotificationTypeText(com.example.ma.domain.model.Notification.NotificationType type) {
            return null;
        }
    }
}