package com.example.ma.presentation.auth;

/**
 * صفحه ورود به سیستم
 * این صفحه احراز هویت کاربران را انجام می‌دهد
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\f\b\u0007\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u0012\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0014H\u0014J\b\u0010\u0015\u001a\u00020\u0012H\u0002J\b\u0010\u0016\u001a\u00020\u0012H\u0002J\u0018\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001aH\u0002J\u0010\u0010\u001c\u001a\u00020\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J\u0010\u0010\u001d\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u001aH\u0002J\b\u0010\u001f\u001a\u00020\u0012H\u0002J\u0010\u0010 \u001a\u00020\u00122\u0006\u0010!\u001a\u00020\u0018H\u0002J\u0010\u0010\"\u001a\u00020\u00122\u0006\u0010\u001e\u001a\u00020\u001aH\u0002J\b\u0010#\u001a\u00020\u0012H\u0002J\b\u0010$\u001a\u00020\u0012H\u0002J\b\u0010%\u001a\u00020\u0012H\u0002R\u001b\u0010\u0004\u001a\u00020\u00058BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\b\u0010\t\u001a\u0004\b\u0006\u0010\u0007R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u000eX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0010X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006&"}, d2 = {"Lcom/example/ma/presentation/auth/LoginActivity;", "Landroidx/appcompat/app/AppCompatActivity;", "<init>", "()V", "viewModel", "Lcom/example/ma/presentation/auth/LoginViewModel;", "getViewModel", "()Lcom/example/ma/presentation/auth/LoginViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "usernameEditText", "Lcom/google/android/material/textfield/TextInputEditText;", "passwordEditText", "loginButton", "Lcom/google/android/material/button/MaterialButton;", "errorTextView", "Lcom/google/android/material/textview/MaterialTextView;", "onCreate", "", "savedInstanceState", "Landroid/os/Bundle;", "setupUI", "observeViewModel", "validateInput", "", "username", "", "password", "isValidUsername", "showError", "message", "hideError", "showLoading", "show", "showSuccess", "enableLoginButton", "disableLoginButton", "navigateToMain", "app_debug"})
public final class LoginActivity extends androidx.appcompat.app.AppCompatActivity {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy viewModel$delegate = null;
    private com.google.android.material.textfield.TextInputEditText usernameEditText;
    private com.google.android.material.textfield.TextInputEditText passwordEditText;
    private com.google.android.material.button.MaterialButton loginButton;
    private com.google.android.material.textview.MaterialTextView errorTextView;
    
    public LoginActivity() {
        super();
    }
    
    private final com.example.ma.presentation.auth.LoginViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override()
    protected void onCreate(@org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupUI() {
    }
    
    private final void observeViewModel() {
    }
    
    private final boolean validateInput(java.lang.String username, java.lang.String password) {
        return false;
    }
    
    private final boolean isValidUsername(java.lang.String username) {
        return false;
    }
    
    private final void showError(java.lang.String message) {
    }
    
    private final void hideError() {
    }
    
    private final void showLoading(boolean show) {
    }
    
    private final void showSuccess(java.lang.String message) {
    }
    
    private final void enableLoginButton() {
    }
    
    private final void disableLoginButton() {
    }
    
    private final void navigateToMain() {
    }
}