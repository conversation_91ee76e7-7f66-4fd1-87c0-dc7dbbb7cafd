package com.example.ma.presentation.notifications;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\b\u0010\u000e\u001a\u00020\u000fH\u0002J\u000e\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u0012J\u000e\u0010\u0013\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u0012R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0006\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\b0\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\r\u00a8\u0006\u0014"}, d2 = {"Lcom/example/ma/presentation/notifications/NotificationViewModel;", "Landroidx/lifecycle/ViewModel;", "notificationRepository", "Lcom/example/ma/domain/repository/NotificationRepository;", "<init>", "(Lcom/example/ma/domain/repository/NotificationRepository;)V", "_notifications", "Landroidx/lifecycle/MutableLiveData;", "", "Lcom/example/ma/domain/model/Notification;", "notifications", "Landroidx/lifecycle/LiveData;", "getNotifications", "()Landroidx/lifecycle/LiveData;", "loadNotifications", "", "markAsRead", "notificationId", "", "deleteNotification", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class NotificationViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.repository.NotificationRepository notificationRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.List<com.example.ma.domain.model.Notification>> _notifications = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.List<com.example.ma.domain.model.Notification>> notifications = null;
    
    @javax.inject.Inject()
    public NotificationViewModel(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.NotificationRepository notificationRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.example.ma.domain.model.Notification>> getNotifications() {
        return null;
    }
    
    private final void loadNotifications() {
    }
    
    public final void markAsRead(@org.jetbrains.annotations.NotNull()
    java.lang.String notificationId) {
    }
    
    public final void deleteNotification(@org.jetbrains.annotations.NotNull()
    java.lang.String notificationId) {
    }
}