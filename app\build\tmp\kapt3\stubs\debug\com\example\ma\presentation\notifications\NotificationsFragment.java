package com.example.ma.presentation.notifications;

/**
 * Fragment برای نمایش اعلانات
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000`\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J&\u0010\u0016\u001a\u0004\u0018\u00010\u00172\u0006\u0010\u0018\u001a\u00020\u00192\b\u0010\u001a\u001a\u0004\u0018\u00010\u001b2\b\u0010\u001c\u001a\u0004\u0018\u00010\u001dH\u0016J\u001a\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\u00172\b\u0010\u001c\u001a\u0004\u0018\u00010\u001dH\u0016J\u0010\u0010!\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\u0017H\u0002J\b\u0010\"\u001a\u00020\u001fH\u0002J\b\u0010#\u001a\u00020\u001fH\u0002J\b\u0010$\u001a\u00020\u001fH\u0002J\u0016\u0010%\u001a\u00020\u001f2\f\u0010&\u001a\b\u0012\u0004\u0012\u00020(0\'H\u0002J\u0010\u0010)\u001a\u00020\u001f2\u0006\u0010*\u001a\u00020(H\u0002J\u0010\u0010+\u001a\u00020\u001f2\u0006\u0010*\u001a\u00020(H\u0002J\u0018\u0010,\u001a\u00020\u001f2\u0006\u0010*\u001a\u00020(2\u0006\u0010-\u001a\u00020.H\u0002R\u001b\u0010\u0004\u001a\u00020\u00058BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\b\u0010\t\u001a\u0004\b\u0006\u0010\u0007R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u001e\u0010\f\u001a\u00020\r8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010\u000f\"\u0004\b\u0010\u0010\u0011R\u000e\u0010\u0012\u001a\u00020\u0013X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006/"}, d2 = {"Lcom/example/ma/presentation/notifications/NotificationsFragment;", "Landroidx/fragment/app/Fragment;", "<init>", "()V", "viewModel", "Lcom/example/ma/presentation/notifications/NotificationsViewModel;", "getViewModel", "()Lcom/example/ma/presentation/notifications/NotificationsViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "notificationAdapter", "Lcom/example/ma/presentation/adapters/NotificationAdapter;", "preferencesManager", "Lcom/example/ma/utils/PreferencesManager;", "getPreferencesManager", "()Lcom/example/ma/utils/PreferencesManager;", "setPreferencesManager", "(Lcom/example/ma/utils/PreferencesManager;)V", "recyclerView", "Landroidx/recyclerview/widget/RecyclerView;", "tvEmptyState", "Lcom/google/android/material/textview/MaterialTextView;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onViewCreated", "", "view", "setupViews", "setupRecyclerView", "observeViewModel", "loadNotifications", "updateNotificationsList", "notifications", "", "Lcom/example/ma/domain/model/Notification;", "handleNotificationClick", "notification", "markAsRead", "handleApproval", "approved", "", "app_debug"})
public final class NotificationsFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy viewModel$delegate = null;
    private com.example.ma.presentation.adapters.NotificationAdapter notificationAdapter;
    @javax.inject.Inject()
    public com.example.ma.utils.PreferencesManager preferencesManager;
    private androidx.recyclerview.widget.RecyclerView recyclerView;
    private com.google.android.material.textview.MaterialTextView tvEmptyState;
    
    public NotificationsFragment() {
        super();
    }
    
    private final com.example.ma.presentation.notifications.NotificationsViewModel getViewModel() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.utils.PreferencesManager getPreferencesManager() {
        return null;
    }
    
    public final void setPreferencesManager(@org.jetbrains.annotations.NotNull()
    com.example.ma.utils.PreferencesManager p0) {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupViews(android.view.View view) {
    }
    
    private final void setupRecyclerView() {
    }
    
    private final void observeViewModel() {
    }
    
    private final void loadNotifications() {
    }
    
    private final void updateNotificationsList(java.util.List<com.example.ma.domain.model.Notification> notifications) {
    }
    
    private final void handleNotificationClick(com.example.ma.domain.model.Notification notification) {
    }
    
    private final void markAsRead(com.example.ma.domain.model.Notification notification) {
    }
    
    private final void handleApproval(com.example.ma.domain.model.Notification notification, boolean approved) {
    }
}