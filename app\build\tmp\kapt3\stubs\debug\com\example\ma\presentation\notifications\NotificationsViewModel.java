package com.example.ma.presentation.notifications;

/**
 * ViewModel برای NotificationsFragment
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B!\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0004\b\b\u0010\tJ\u0006\u0010\u0019\u001a\u00020\u001aJ\u000e\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001c\u001a\u00020\u0016J\u0016\u0010\u001d\u001a\u00020\u001a2\u0006\u0010\u001e\u001a\u00020\r2\u0006\u0010\u001f\u001a\u00020\u0013J\u000e\u0010 \u001a\u00020\u001a2\u0006\u0010\u001c\u001a\u00020\u0016J\f\u0010!\u001a\b\u0012\u0004\u0012\u00020\"0\u000fJ\u0006\u0010#\u001a\u00020\u001aR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\n\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\f0\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00130\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u0016\u0010\u0015\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00160\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0017\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00160\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0011\u00a8\u0006$"}, d2 = {"Lcom/example/ma/presentation/notifications/NotificationsViewModel;", "Landroidx/lifecycle/ViewModel;", "notificationRepository", "Lcom/example/ma/domain/repository/NotificationRepository;", "transactionRepository", "Lcom/example/ma/domain/repository/TransactionRepository;", "preferencesManager", "Lcom/example/ma/utils/PreferencesManager;", "<init>", "(Lcom/example/ma/domain/repository/NotificationRepository;Lcom/example/ma/domain/repository/TransactionRepository;Lcom/example/ma/utils/PreferencesManager;)V", "_notifications", "Landroidx/lifecycle/MutableLiveData;", "", "Lcom/example/ma/domain/model/Notification;", "notifications", "Landroidx/lifecycle/LiveData;", "getNotifications", "()Landroidx/lifecycle/LiveData;", "_isLoading", "", "isLoading", "_error", "", "error", "getError", "loadNotifications", "", "markAsRead", "notificationId", "processTransactionApproval", "notification", "approved", "deleteNotification", "getUnreadCount", "", "clearError", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class NotificationsViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.repository.NotificationRepository notificationRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.repository.TransactionRepository transactionRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.utils.PreferencesManager preferencesManager = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.List<com.example.ma.domain.model.Notification>> _notifications = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.List<com.example.ma.domain.model.Notification>> notifications = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> error = null;
    
    @javax.inject.Inject()
    public NotificationsViewModel(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.NotificationRepository notificationRepository, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.TransactionRepository transactionRepository, @org.jetbrains.annotations.NotNull()
    com.example.ma.utils.PreferencesManager preferencesManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.example.ma.domain.model.Notification>> getNotifications() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getError() {
        return null;
    }
    
    /**
     * بارگذاری اعلانات
     */
    public final void loadNotifications() {
    }
    
    /**
     * علامت‌گذاری اعلان به عنوان خوانده شده
     */
    public final void markAsRead(@org.jetbrains.annotations.NotNull()
    java.lang.String notificationId) {
    }
    
    /**
     * پردازش تایید/رد تراکنش از روی اعلان
     */
    public final void processTransactionApproval(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.Notification notification, boolean approved) {
    }
    
    /**
     * حذف اعلان
     */
    public final void deleteNotification(@org.jetbrains.annotations.NotNull()
    java.lang.String notificationId) {
    }
    
    /**
     * دریافت تعداد اعلانات خوانده نشده
     */
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Integer> getUnreadCount() {
        return null;
    }
    
    /**
     * پاک کردن خطا
     */
    public final void clearError() {
    }
}