package com.example.ma.presentation.profile;

/**
 * Fragment برای نمایش پروفایل کاربر
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J&\u0010\u001b\u001a\u0004\u0018\u00010\u001c2\u0006\u0010\u001d\u001a\u00020\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010 2\b\u0010!\u001a\u0004\u0018\u00010\"H\u0016J\u001a\u0010#\u001a\u00020$2\u0006\u0010%\u001a\u00020\u001c2\b\u0010!\u001a\u0004\u0018\u00010\"H\u0016J\u0010\u0010&\u001a\u00020$2\u0006\u0010%\u001a\u00020\u001cH\u0002J\b\u0010\'\u001a\u00020$H\u0002J\b\u0010(\u001a\u00020$H\u0002J\u0010\u0010)\u001a\u00020$2\u0006\u0010*\u001a\u00020+H\u0002J\u0010\u0010,\u001a\u00020$2\u0006\u0010-\u001a\u00020.H\u0002J\u0010\u0010/\u001a\u0002002\u0006\u00101\u001a\u000202H\u0002J\b\u00103\u001a\u00020$H\u0002R\u001b\u0010\u0004\u001a\u00020\u00058BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\b\u0010\t\u001a\u0004\b\u0006\u0010\u0007R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u0015X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u0018X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0018X\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00064"}, d2 = {"Lcom/example/ma/presentation/profile/ProfileFragment;", "Landroidx/fragment/app/Fragment;", "<init>", "()V", "viewModel", "Lcom/example/ma/presentation/profile/ProfileViewModel;", "getViewModel", "()Lcom/example/ma/presentation/profile/ProfileViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "ivProfileImage", "Lde/hdodenhof/circleimageview/CircleImageView;", "tvUserName", "Lcom/google/android/material/textview/MaterialTextView;", "tvUserEmail", "tvUserPhone", "tvUserRole", "tvTotalSales", "tvTotalExpenses", "tvMyShare", "cardProfileInfo", "Lcom/google/android/material/card/MaterialCardView;", "cardFinancialSummary", "btnEditProfile", "Lcom/google/android/material/button/MaterialButton;", "btnChangePassword", "btnChangePhoto", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onViewCreated", "", "view", "setupViews", "observeViewModel", "loadUserProfile", "updateUserDisplay", "user", "Lcom/example/ma/domain/model/User;", "updateFinancialDisplay", "summary", "Lcom/example/ma/domain/model/FinancialSummary;", "formatCurrency", "", "amount", "", "navigateToProfileImage", "app_debug"})
public final class ProfileFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy viewModel$delegate = null;
    private de.hdodenhof.circleimageview.CircleImageView ivProfileImage;
    private com.google.android.material.textview.MaterialTextView tvUserName;
    private com.google.android.material.textview.MaterialTextView tvUserEmail;
    private com.google.android.material.textview.MaterialTextView tvUserPhone;
    private com.google.android.material.textview.MaterialTextView tvUserRole;
    private com.google.android.material.textview.MaterialTextView tvTotalSales;
    private com.google.android.material.textview.MaterialTextView tvTotalExpenses;
    private com.google.android.material.textview.MaterialTextView tvMyShare;
    private com.google.android.material.card.MaterialCardView cardProfileInfo;
    private com.google.android.material.card.MaterialCardView cardFinancialSummary;
    private com.google.android.material.button.MaterialButton btnEditProfile;
    private com.google.android.material.button.MaterialButton btnChangePassword;
    private com.google.android.material.button.MaterialButton btnChangePhoto;
    
    public ProfileFragment() {
        super();
    }
    
    private final com.example.ma.presentation.profile.ProfileViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupViews(android.view.View view) {
    }
    
    private final void observeViewModel() {
    }
    
    private final void loadUserProfile() {
    }
    
    private final void updateUserDisplay(com.example.ma.domain.model.User user) {
    }
    
    private final void updateFinancialDisplay(com.example.ma.domain.model.FinancialSummary summary) {
    }
    
    private final java.lang.String formatCurrency(double amount) {
        return null;
    }
    
    private final void navigateToProfileImage() {
    }
}