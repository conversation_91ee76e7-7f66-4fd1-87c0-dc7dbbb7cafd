package com.example.ma.presentation.reports;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0006\u0010\u001f\u001a\u00020 J\u0006\u0010!\u001a\u00020 J\u0006\u0010\"\u001a\u00020 J\u0006\u0010#\u001a\u00020 J\u0006\u0010$\u001a\u00020 J\u0006\u0010%\u001a\u00020 R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u000b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\n0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0016\u0010\u000f\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00100\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0011\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00100\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u000eR \u0010\u0013\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00160\u00140\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R#\u0010\u0017\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00160\u00140\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u000eR\u0014\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u001a0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001a0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u000eR\u0016\u0010\u001c\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00150\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001d\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00150\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u000e\u00a8\u0006&"}, d2 = {"Lcom/example/ma/presentation/reports/ReportsViewModel;", "Landroidx/lifecycle/ViewModel;", "getFinancialSummaryUseCase", "Lcom/example/ma/domain/usecase/GetFinancialSummaryUseCase;", "getQuickStatsUseCase", "Lcom/example/ma/domain/usecase/GetQuickStatsUseCase;", "<init>", "(Lcom/example/ma/domain/usecase/GetFinancialSummaryUseCase;Lcom/example/ma/domain/usecase/GetQuickStatsUseCase;)V", "_financialSummary", "Landroidx/lifecycle/MutableLiveData;", "Lcom/example/ma/domain/model/BusinessFinancialSummary;", "financialSummary", "Landroidx/lifecycle/LiveData;", "getFinancialSummary", "()Landroidx/lifecycle/LiveData;", "_quickStats", "Lcom/example/ma/domain/model/QuickStats;", "quickStats", "getQuickStats", "_cashCardReport", "", "", "", "cashCardReport", "getCashCardReport", "_isLoading", "", "isLoading", "_error", "error", "getError", "loadFinancialSummary", "", "loadQuickStats", "loadCashCardReport", "exportReport", "shareReport", "clearError", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class ReportsViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.usecase.GetFinancialSummaryUseCase getFinancialSummaryUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.usecase.GetQuickStatsUseCase getQuickStatsUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.example.ma.domain.model.BusinessFinancialSummary> _financialSummary = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.example.ma.domain.model.BusinessFinancialSummary> financialSummary = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.example.ma.domain.model.QuickStats> _quickStats = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.example.ma.domain.model.QuickStats> quickStats = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.Map<java.lang.String, java.lang.Object>> _cashCardReport = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.Map<java.lang.String, java.lang.Object>> cashCardReport = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> error = null;
    
    @javax.inject.Inject()
    public ReportsViewModel(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.usecase.GetFinancialSummaryUseCase getFinancialSummaryUseCase, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.usecase.GetQuickStatsUseCase getQuickStatsUseCase) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.example.ma.domain.model.BusinessFinancialSummary> getFinancialSummary() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.example.ma.domain.model.QuickStats> getQuickStats() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.Map<java.lang.String, java.lang.Object>> getCashCardReport() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getError() {
        return null;
    }
    
    /**
     * بارگذاری خلاصه مالی
     */
    public final void loadFinancialSummary() {
    }
    
    /**
     * بارگذاری آمار سریع
     */
    public final void loadQuickStats() {
    }
    
    /**
     * بارگذاری گزارش نقدی/کارتی
     */
    public final void loadCashCardReport() {
    }
    
    /**
     * صادر کردن گزارش
     */
    public final void exportReport() {
    }
    
    /**
     * اشتراک‌گذاری گزارش
     */
    public final void shareReport() {
    }
    
    /**
     * پاک کردن خطا
     */
    public final void clearError() {
    }
}