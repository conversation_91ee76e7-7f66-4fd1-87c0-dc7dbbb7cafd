package com.example.ma.presentation.settings;

/**
 * مدل داده‌های تنظیمات
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u001b\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001BM\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\u0005\u0012\b\b\u0002\u0010\t\u001a\u00020\u0005\u0012\b\b\u0002\u0010\n\u001a\u00020\u0005\u00a2\u0006\u0004\b\u000b\u0010\fJ\t\u0010\u0016\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0017\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0018\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0005H\u00c6\u0003JO\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u001e\u001a\u00020\u00052\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020!H\u00d6\u0001J\t\u0010\"\u001a\u00020#H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010R\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u0011\u0010\n\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0010\u00a8\u0006$"}, d2 = {"Lcom/example/ma/presentation/settings/SettingsData;", "", "themeMode", "Lcom/example/ma/utils/ThemeManager$ThemeMode;", "pushNotificationsEnabled", "", "soundVibrationEnabled", "transactionAlertsEnabled", "biometricAuthEnabled", "autoLockEnabled", "autoSyncEnabled", "<init>", "(Lcom/example/ma/utils/ThemeManager$ThemeMode;ZZZZZZ)V", "getThemeMode", "()Lcom/example/ma/utils/ThemeManager$ThemeMode;", "getPushNotificationsEnabled", "()Z", "getSoundVibrationEnabled", "getTransactionAlertsEnabled", "getBiometricAuthEnabled", "getAutoLockEnabled", "getAutoSyncEnabled", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "hashCode", "", "toString", "", "app_debug"})
public final class SettingsData {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.utils.ThemeManager.ThemeMode themeMode = null;
    private final boolean pushNotificationsEnabled = false;
    private final boolean soundVibrationEnabled = false;
    private final boolean transactionAlertsEnabled = false;
    private final boolean biometricAuthEnabled = false;
    private final boolean autoLockEnabled = false;
    private final boolean autoSyncEnabled = false;
    
    public SettingsData(@org.jetbrains.annotations.NotNull()
    com.example.ma.utils.ThemeManager.ThemeMode themeMode, boolean pushNotificationsEnabled, boolean soundVibrationEnabled, boolean transactionAlertsEnabled, boolean biometricAuthEnabled, boolean autoLockEnabled, boolean autoSyncEnabled) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.utils.ThemeManager.ThemeMode getThemeMode() {
        return null;
    }
    
    public final boolean getPushNotificationsEnabled() {
        return false;
    }
    
    public final boolean getSoundVibrationEnabled() {
        return false;
    }
    
    public final boolean getTransactionAlertsEnabled() {
        return false;
    }
    
    public final boolean getBiometricAuthEnabled() {
        return false;
    }
    
    public final boolean getAutoLockEnabled() {
        return false;
    }
    
    public final boolean getAutoSyncEnabled() {
        return false;
    }
    
    public SettingsData() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.utils.ThemeManager.ThemeMode component1() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    public final boolean component7() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.presentation.settings.SettingsData copy(@org.jetbrains.annotations.NotNull()
    com.example.ma.utils.ThemeManager.ThemeMode themeMode, boolean pushNotificationsEnabled, boolean soundVibrationEnabled, boolean transactionAlertsEnabled, boolean biometricAuthEnabled, boolean autoLockEnabled, boolean autoSyncEnabled) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}