package com.example.ma.presentation.settings;

/**
 * Fragment برای تنظیمات برنامه
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\u0007\u00a2\u0006\u0004\b\u0002\u0010\u0003J&\u0010!\u001a\u0004\u0018\u00010\"2\u0006\u0010#\u001a\u00020$2\b\u0010%\u001a\u0004\u0018\u00010&2\b\u0010\'\u001a\u0004\u0018\u00010(H\u0016J\u001a\u0010)\u001a\u00020*2\u0006\u0010+\u001a\u00020\"2\b\u0010\'\u001a\u0004\u0018\u00010(H\u0016J\u0010\u0010,\u001a\u00020*2\u0006\u0010+\u001a\u00020\"H\u0002J\b\u0010-\u001a\u00020*H\u0002J\b\u0010.\u001a\u00020*H\u0002J\b\u0010/\u001a\u00020*H\u0002J\b\u00100\u001a\u00020*H\u0002J\b\u00101\u001a\u00020*H\u0002J\b\u00102\u001a\u00020*H\u0002R\u001b\u0010\u0004\u001a\u00020\u00058BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\b\u0010\t\u001a\u0004\b\u0006\u0010\u0007R\u000e\u0010\n\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001a\u001a\u00020\u0012X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001c\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001d\u001a\u00020\u000bX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001e\u001a\u00020\rX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001f\u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010 \u001a\u00020\u000fX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u00063"}, d2 = {"Lcom/example/ma/presentation/settings/SettingsFragment;", "Landroidx/fragment/app/Fragment;", "<init>", "()V", "viewModel", "Lcom/example/ma/presentation/settings/SettingsViewModel;", "getViewModel", "()Lcom/example/ma/presentation/settings/SettingsViewModel;", "viewModel$delegate", "Lkotlin/Lazy;", "cardAppearance", "Lcom/google/android/material/card/MaterialCardView;", "tvCurrentTheme", "Lcom/google/android/material/textview/MaterialTextView;", "btnChangeTheme", "Lcom/google/android/material/button/MaterialButton;", "cardNotifications", "switchPushNotifications", "Lcom/google/android/material/switchmaterial/SwitchMaterial;", "switchSoundVibration", "switchTransactionAlerts", "cardSecurity", "btnChangePassword", "switchBiometric", "switchAutoLock", "cardDataSync", "switchAutoSync", "btnBackupData", "btnClearCache", "cardAbout", "tvAppVersion", "btnContactSupport", "btnLogout", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "savedInstanceState", "Landroid/os/Bundle;", "onViewCreated", "", "view", "setupViews", "setupClickListeners", "observeViewModel", "loadSettings", "showThemeSelectionDialog", "showClearCacheConfirmation", "showLogoutConfirmation", "app_debug"})
public final class SettingsFragment extends androidx.fragment.app.Fragment {
    @org.jetbrains.annotations.NotNull()
    private final kotlin.Lazy viewModel$delegate = null;
    private com.google.android.material.card.MaterialCardView cardAppearance;
    private com.google.android.material.textview.MaterialTextView tvCurrentTheme;
    private com.google.android.material.button.MaterialButton btnChangeTheme;
    private com.google.android.material.card.MaterialCardView cardNotifications;
    private com.google.android.material.switchmaterial.SwitchMaterial switchPushNotifications;
    private com.google.android.material.switchmaterial.SwitchMaterial switchSoundVibration;
    private com.google.android.material.switchmaterial.SwitchMaterial switchTransactionAlerts;
    private com.google.android.material.card.MaterialCardView cardSecurity;
    private com.google.android.material.button.MaterialButton btnChangePassword;
    private com.google.android.material.switchmaterial.SwitchMaterial switchBiometric;
    private com.google.android.material.switchmaterial.SwitchMaterial switchAutoLock;
    private com.google.android.material.card.MaterialCardView cardDataSync;
    private com.google.android.material.switchmaterial.SwitchMaterial switchAutoSync;
    private com.google.android.material.button.MaterialButton btnBackupData;
    private com.google.android.material.button.MaterialButton btnClearCache;
    private com.google.android.material.card.MaterialCardView cardAbout;
    private com.google.android.material.textview.MaterialTextView tvAppVersion;
    private com.google.android.material.button.MaterialButton btnContactSupport;
    private com.google.android.material.button.MaterialButton btnLogout;
    
    public SettingsFragment() {
        super();
    }
    
    private final com.example.ma.presentation.settings.SettingsViewModel getViewModel() {
        return null;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull()
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable()
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override()
    public void onViewCreated(@org.jetbrains.annotations.NotNull()
    android.view.View view, @org.jetbrains.annotations.Nullable()
    android.os.Bundle savedInstanceState) {
    }
    
    private final void setupViews(android.view.View view) {
    }
    
    private final void setupClickListeners() {
    }
    
    private final void observeViewModel() {
    }
    
    private final void loadSettings() {
    }
    
    private final void showThemeSelectionDialog() {
    }
    
    private final void showClearCacheConfirmation() {
    }
    
    private final void showLogoutConfirmation() {
    }
}