package com.example.ma.presentation.settings;

/**
 * ViewModel برای SettingsFragment
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\f\b\u0007\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0006\u0010\u0019\u001a\u00020\u001aJ\u000e\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u001c\u001a\u00020\u001dJ\u000e\u0010\u001e\u001a\u00020\u001a2\u0006\u0010\u001f\u001a\u00020\u0010J\u000e\u0010 \u001a\u00020\u001a2\u0006\u0010\u001f\u001a\u00020\u0010J\u000e\u0010!\u001a\u00020\u001a2\u0006\u0010\u001f\u001a\u00020\u0010J\u000e\u0010\"\u001a\u00020\u001a2\u0006\u0010\u001f\u001a\u00020\u0010J\u000e\u0010#\u001a\u00020\u001a2\u0006\u0010\u001f\u001a\u00020\u0010J\u000e\u0010$\u001a\u00020\u001a2\u0006\u0010\u001f\u001a\u00020\u0010J\u0006\u0010%\u001a\u00020\u001aJ\u0006\u0010&\u001a\u00020\u001aJ\u0006\u0010\'\u001a\u00020\u001aJ\u0006\u0010(\u001a\u00020\u001aR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0014\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00100\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u000eR\u0016\u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00130\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0014\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00130\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000eR\u0014\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00100\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00100\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u000e\u00a8\u0006)"}, d2 = {"Lcom/example/ma/presentation/settings/SettingsViewModel;", "Landroidx/lifecycle/ViewModel;", "authRepository", "Lcom/example/ma/domain/repository/AuthRepository;", "preferencesManager", "Lcom/example/ma/utils/PreferencesManager;", "<init>", "(Lcom/example/ma/domain/repository/AuthRepository;Lcom/example/ma/utils/PreferencesManager;)V", "_settings", "Landroidx/lifecycle/MutableLiveData;", "Lcom/example/ma/presentation/settings/SettingsData;", "settings", "Landroidx/lifecycle/LiveData;", "getSettings", "()Landroidx/lifecycle/LiveData;", "_isLoading", "", "isLoading", "_error", "", "error", "getError", "_logoutResult", "logoutResult", "getLogoutResult", "loadSettings", "", "updateTheme", "themeMode", "Lcom/example/ma/utils/ThemeManager$ThemeMode;", "updatePushNotifications", "enabled", "updateSoundVibration", "updateTransactionAlerts", "updateBiometricAuth", "updateAutoLock", "updateAutoSync", "backupData", "clearCache", "logout", "clearError", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class SettingsViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.repository.AuthRepository authRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.utils.PreferencesManager preferencesManager = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<com.example.ma.presentation.settings.SettingsData> _settings = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<com.example.ma.presentation.settings.SettingsData> settings = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> error = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _logoutResult = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> logoutResult = null;
    
    @javax.inject.Inject()
    public SettingsViewModel(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.AuthRepository authRepository, @org.jetbrains.annotations.NotNull()
    com.example.ma.utils.PreferencesManager preferencesManager) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<com.example.ma.presentation.settings.SettingsData> getSettings() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> getLogoutResult() {
        return null;
    }
    
    /**
     * بارگذاری تنظیمات
     */
    public final void loadSettings() {
    }
    
    /**
     * تغییر تم
     */
    public final void updateTheme(@org.jetbrains.annotations.NotNull()
    com.example.ma.utils.ThemeManager.ThemeMode themeMode) {
    }
    
    /**
     * تغییر وضعیت اعلانات push
     */
    public final void updatePushNotifications(boolean enabled) {
    }
    
    /**
     * تغییر وضعیت صدا و لرزش
     */
    public final void updateSoundVibration(boolean enabled) {
    }
    
    /**
     * تغییر وضعیت هشدارهای تراکنش
     */
    public final void updateTransactionAlerts(boolean enabled) {
    }
    
    /**
     * تغییر وضعیت احراز هویت بیومتریک
     */
    public final void updateBiometricAuth(boolean enabled) {
    }
    
    /**
     * تغییر وضعیت قفل خودکار
     */
    public final void updateAutoLock(boolean enabled) {
    }
    
    /**
     * تغییر وضعیت همگام‌سازی خودکار
     */
    public final void updateAutoSync(boolean enabled) {
    }
    
    /**
     * پشتیبان‌گیری از داده‌ها
     */
    public final void backupData() {
    }
    
    /**
     * پاک کردن حافظه موقت
     */
    public final void clearCache() {
    }
    
    /**
     * خروج از حساب کاربری
     */
    public final void logout() {
    }
    
    /**
     * پاک کردن خطا
     */
    public final void clearError() {
    }
}