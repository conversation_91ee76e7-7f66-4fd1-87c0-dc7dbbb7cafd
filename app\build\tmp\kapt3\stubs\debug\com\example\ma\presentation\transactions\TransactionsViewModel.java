package com.example.ma.presentation.transactions;

/**
 * ViewModel for TransactionsFragment
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\t\b\u0007\u0018\u00002\u00020\u0001B\u0019\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0004\b\u0006\u0010\u0007J\u0006\u0010\u001c\u001a\u00020\u000bJ\u000e\u0010\u001d\u001a\u00020\u000b2\u0006\u0010\u001e\u001a\u00020\u001fJ\u000e\u0010 \u001a\u00020\u000b2\u0006\u0010!\u001a\u00020\"J\u0016\u0010#\u001a\u00020\u000b2\u0006\u0010$\u001a\u00020\u00192\u0006\u0010!\u001a\u00020\"J0\u0010%\u001a\u00020\u000b2\u0006\u0010&\u001a\u00020\u00122\u0006\u0010\'\u001a\u00020\u00192\u0006\u0010(\u001a\u00020\u00192\u0006\u0010)\u001a\u00020\u00192\b\u0010*\u001a\u0004\u0018\u00010\u0019R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u001a\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00110\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00120\u00110\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u000fR\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00160\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u000fR\u0016\u0010\u0018\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00190\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001a\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00190\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u000f\u00a8\u0006+"}, d2 = {"Lcom/example/ma/presentation/transactions/TransactionsViewModel;", "Landroidx/lifecycle/ViewModel;", "transactionRepository", "Lcom/example/ma/domain/repository/TransactionRepository;", "createTransactionUseCase", "Lcom/example/ma/domain/usecase/CreateTransactionUseCase;", "<init>", "(Lcom/example/ma/domain/repository/TransactionRepository;Lcom/example/ma/domain/usecase/CreateTransactionUseCase;)V", "_createTransactionResult", "Landroidx/lifecycle/MutableLiveData;", "Lkotlin/Result;", "", "createTransactionResult", "Landroidx/lifecycle/LiveData;", "getCreateTransactionResult", "()Landroidx/lifecycle/LiveData;", "_transactions", "", "Lcom/example/ma/domain/model/Transaction;", "transactions", "getTransactions", "_isLoading", "", "isLoading", "_error", "", "error", "getError", "loadTransactions", "filterTransactionsByType", "type", "Lcom/example/ma/domain/model/TransactionType;", "filterTransactionsByStatus", "status", "Lcom/example/ma/domain/model/TransactionStatus;", "updateTransactionStatus", "transactionId", "createTransaction", "transaction", "currentUserId", "partnerUserId", "currentUserDisplayName", "currentUserAvatarUrl", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class TransactionsViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.repository.TransactionRepository transactionRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.domain.usecase.CreateTransactionUseCase createTransactionUseCase = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<kotlin.Result<kotlin.Unit>> _createTransactionResult = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<kotlin.Result<kotlin.Unit>> createTransactionResult = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.util.List<com.example.ma.domain.model.Transaction>> _transactions = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.util.List<com.example.ma.domain.model.Transaction>> transactions = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull()
    private final androidx.lifecycle.LiveData<java.lang.String> error = null;
    
    @javax.inject.Inject()
    public TransactionsViewModel(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.repository.TransactionRepository transactionRepository, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.usecase.CreateTransactionUseCase createTransactionUseCase) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<kotlin.Result<kotlin.Unit>> getCreateTransactionResult() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.util.List<com.example.ma.domain.model.Transaction>> getTransactions() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.lifecycle.LiveData<java.lang.String> getError() {
        return null;
    }
    
    /**
     * بارگذاری تراکنش‌ها
     */
    public final void loadTransactions() {
    }
    
    /**
     * فیلتر تراکنش‌ها بر اساس نوع
     */
    public final void filterTransactionsByType(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TransactionType type) {
    }
    
    /**
     * فیلتر تراکنش‌ها بر اساس وضعیت
     */
    public final void filterTransactionsByStatus(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TransactionStatus status) {
    }
    
    /**
     * آپدیت وضعیت تراکنش
     */
    public final void updateTransactionStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String transactionId, @org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.TransactionStatus status) {
    }
    
    /**
     * ایجاد یک تراکنش جدید
     */
    public final void createTransaction(@org.jetbrains.annotations.NotNull()
    com.example.ma.domain.model.Transaction transaction, @org.jetbrains.annotations.NotNull()
    java.lang.String currentUserId, @org.jetbrains.annotations.NotNull()
    java.lang.String partnerUserId, @org.jetbrains.annotations.NotNull()
    java.lang.String currentUserDisplayName, @org.jetbrains.annotations.Nullable()
    java.lang.String currentUserAvatarUrl) {
    }
}