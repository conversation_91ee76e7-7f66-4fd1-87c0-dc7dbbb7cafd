package com.example.ma.utils;

/**
 * نتیجه تست سرعت
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0015\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B3\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\u0004\b\f\u0010\rJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\tH\u00c6\u0003J\u000b\u0010\u001c\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J=\u0010\u001d\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u000bH\u00c6\u0001J\u0013\u0010\u001e\u001a\u00020\u00032\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020!H\u00d6\u0001J\t\u0010\"\u001a\u00020\u000bH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0013\u0010\n\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017\u00a8\u0006#"}, d2 = {"Lcom/example/ma/utils/ConnectionSpeedResult;", "", "success", "", "latency", "", "speedCategory", "Lcom/example/ma/utils/SpeedCategory;", "networkType", "Lcom/example/ma/utils/NetworkType;", "error", "", "<init>", "(ZJLcom/example/ma/utils/SpeedCategory;Lcom/example/ma/utils/NetworkType;Ljava/lang/String;)V", "getSuccess", "()Z", "getLatency", "()J", "getSpeedCategory", "()Lcom/example/ma/utils/SpeedCategory;", "getNetworkType", "()Lcom/example/ma/utils/NetworkType;", "getError", "()Ljava/lang/String;", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class ConnectionSpeedResult {
    private final boolean success = false;
    private final long latency = 0L;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.utils.SpeedCategory speedCategory = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.ma.utils.NetworkType networkType = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String error = null;
    
    public ConnectionSpeedResult(boolean success, long latency, @org.jetbrains.annotations.NotNull()
    com.example.ma.utils.SpeedCategory speedCategory, @org.jetbrains.annotations.NotNull()
    com.example.ma.utils.NetworkType networkType, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        super();
    }
    
    public final boolean getSuccess() {
        return false;
    }
    
    public final long getLatency() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.utils.SpeedCategory getSpeedCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.utils.NetworkType getNetworkType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getError() {
        return null;
    }
    
    public final boolean component1() {
        return false;
    }
    
    public final long component2() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.utils.SpeedCategory component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.utils.NetworkType component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.utils.ConnectionSpeedResult copy(boolean success, long latency, @org.jetbrains.annotations.NotNull()
    com.example.ma.utils.SpeedCategory speedCategory, @org.jetbrains.annotations.NotNull()
    com.example.ma.utils.NetworkType networkType, @org.jetbrains.annotations.Nullable()
    java.lang.String error) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}