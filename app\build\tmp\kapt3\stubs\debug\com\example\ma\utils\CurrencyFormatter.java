package com.example.ma.utils;

/**
 * فرمت کردن مبالغ مالی
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u000e\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\u000b\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nJ\u000e\u0010\f\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nR\u0016\u0010\u0004\u001a\n \u0006*\u0004\u0018\u00010\u00050\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/example/ma/utils/CurrencyFormatter;", "", "<init>", "()V", "numberFormat", "Ljava/text/NumberFormat;", "kotlin.jvm.PlatformType", "format", "", "amount", "", "formatCompact", "formatWithoutCurrency", "app_debug"})
public final class CurrencyFormatter {
    private static final java.text.NumberFormat numberFormat = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.ma.utils.CurrencyFormatter INSTANCE = null;
    
    private CurrencyFormatter() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String format(double amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatCompact(double amount) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String formatWithoutCurrency(double amount) {
        return null;
    }
}