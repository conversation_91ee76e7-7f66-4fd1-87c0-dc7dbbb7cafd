package com.example.ma.utils;

/**
 * مدیریت بارگذاری و کش تصاویر با استفاده از Glide
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\b\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u0011\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J<\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\b\u0010\n\u001a\u0004\u0018\u00010\u000b2\u0010\b\u0002\u0010\f\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\r2\u0010\b\u0002\u0010\u000e\u001a\n\u0012\u0004\u0012\u00020\u0007\u0018\u00010\rJ\"\u0010\u000f\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\b\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\u0010\u001a\u00020\u0011J\u0010\u0010\u0012\u001a\u00020\u00072\b\u0010\n\u001a\u0004\u0018\u00010\u000bJ\u0006\u0010\u0013\u001a\u00020\u0007J\u0006\u0010\u0014\u001a\u00020\u0015J\u0006\u0010\u0016\u001a\u00020\u0007J2\u0010\u0017\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\b\u0010\n\u001a\u0004\u0018\u00010\u000b2\u0006\u0010\u0018\u001a\u00020\u00112\u0006\u0010\u0019\u001a\u00020\u00112\b\b\u0002\u0010\u0010\u001a\u00020\u0011J\"\u0010\u001a\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\b\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\u0010\u001a\u00020\u0011J\"\u0010\u001b\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\b\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\u0010\u001a\u00020\u0011J\"\u0010\u001c\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\t2\b\u0010\n\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\u0010\u001a\u00020\u0011J\u0010\u0010\u001d\u001a\u00020\u001e2\b\u0010\n\u001a\u0004\u0018\u00010\u000bJ\u0012\u0010\u001f\u001a\u0004\u0018\u00010 2\b\u0010\n\u001a\u0004\u0018\u00010\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006!"}, d2 = {"Lcom/example/ma/utils/ImageManager;", "", "context", "Landroid/content/Context;", "<init>", "(Landroid/content/Context;)V", "loadProfileImage", "", "imageView", "Landroid/widget/ImageView;", "imageUrl", "", "onSuccess", "Lkotlin/Function0;", "onError", "loadThumbnail", "placeholderRes", "", "preloadImage", "clearImageCache", "getCacheSize", "", "optimizeMemoryUsage", "loadImageWithSize", "width", "height", "loadImageLowQuality", "loadImageWithAnimation", "loadImageHighPriority", "isImageCached", "", "getCachedImage", "Landroid/graphics/drawable/Drawable;", "app_debug"})
public final class ImageManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    
    @javax.inject.Inject()
    public ImageManager(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    /**
     * بارگذاری تصویر پروفایل
     */
    public final void loadProfileImage(@org.jetbrains.annotations.NotNull()
    android.widget.ImageView imageView, @org.jetbrains.annotations.Nullable()
    java.lang.String imageUrl, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onSuccess, @org.jetbrains.annotations.Nullable()
    kotlin.jvm.functions.Function0<kotlin.Unit> onError) {
    }
    
    /**
     * بارگذاری تصویر کوچک (thumbnail)
     */
    public final void loadThumbnail(@org.jetbrains.annotations.NotNull()
    android.widget.ImageView imageView, @org.jetbrains.annotations.Nullable()
    java.lang.String imageUrl, int placeholderRes) {
    }
    
    /**
     * پیش‌بارگذاری تصویر برای بهبود performance
     */
    public final void preloadImage(@org.jetbrains.annotations.Nullable()
    java.lang.String imageUrl) {
    }
    
    /**
     * پاک کردن کش تصاویر
     */
    public final void clearImageCache() {
    }
    
    /**
     * دریافت اندازه کش تصاویر
     */
    public final long getCacheSize() {
        return 0L;
    }
    
    /**
     * بهینه‌سازی memory usage
     */
    public final void optimizeMemoryUsage() {
    }
    
    /**
     * بارگذاری تصویر با اندازه سفارشی
     */
    public final void loadImageWithSize(@org.jetbrains.annotations.NotNull()
    android.widget.ImageView imageView, @org.jetbrains.annotations.Nullable()
    java.lang.String imageUrl, int width, int height, int placeholderRes) {
    }
    
    /**
     * بارگذاری تصویر با کیفیت پایین برای شبکه کند
     */
    public final void loadImageLowQuality(@org.jetbrains.annotations.NotNull()
    android.widget.ImageView imageView, @org.jetbrains.annotations.Nullable()
    java.lang.String imageUrl, int placeholderRes) {
    }
    
    /**
     * بارگذاری تصویر با انیمیشن
     */
    public final void loadImageWithAnimation(@org.jetbrains.annotations.NotNull()
    android.widget.ImageView imageView, @org.jetbrains.annotations.Nullable()
    java.lang.String imageUrl, int placeholderRes) {
    }
    
    /**
     * بارگذاری تصویر با priority بالا
     */
    public final void loadImageHighPriority(@org.jetbrains.annotations.NotNull()
    android.widget.ImageView imageView, @org.jetbrains.annotations.Nullable()
    java.lang.String imageUrl, int placeholderRes) {
    }
    
    /**
     * بررسی وجود تصویر در کش
     */
    public final boolean isImageCached(@org.jetbrains.annotations.Nullable()
    java.lang.String imageUrl) {
        return false;
    }
    
    /**
     * دریافت تصویر از کش
     */
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.drawable.Drawable getCachedImage(@org.jetbrains.annotations.Nullable()
    java.lang.String imageUrl) {
        return null;
    }
}