package com.example.ma.utils;

/**
 * کیفیت شبکه
 */
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/example/ma/utils/NetworkQuality;", "", "<init>", "(Lja<PERSON>/lang/String;I)V", "EXCELLENT", "GOOD", "FAIR", "POOR", "NO_CONNECTION", "app_debug"})
public enum NetworkQuality {
    /*public static final*/ EXCELLENT /* = new EXCELLENT() */,
    /*public static final*/ GOOD /* = new GOOD() */,
    /*public static final*/ FAIR /* = new FAIR() */,
    /*public static final*/ POOR /* = new POOR() */,
    /*public static final*/ NO_CONNECTION /* = new NO_CONNECTION() */;
    
    NetworkQuality() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.ma.utils.NetworkQuality> getEntries() {
        return null;
    }
}