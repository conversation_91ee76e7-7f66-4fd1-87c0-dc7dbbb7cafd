package com.example.ma.utils;

/**
 * مدیریت SharedPreferences برای تنظیمات اپلیکیشن
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0002\n\u0002\b\f\n\u0002\u0010\u000e\n\u0002\b\u000b\n\u0002\u0010\t\n\u0002\b\f\n\u0002\u0010$\n\u0002\b\u0002\b\u0007\u0018\u0000 22\u00020\u0001:\u00012B\u0013\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0004\b\u0004\u0010\u0005J\u0006\u0010\b\u001a\u00020\tJ\u000e\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\tJ\u0006\u0010\r\u001a\u00020\tJ\u000e\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\tJ\u0006\u0010\u000f\u001a\u00020\tJ\u000e\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\tJ\u0006\u0010\u0011\u001a\u00020\tJ\u000e\u0010\u0012\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\tJ\u0006\u0010\u0013\u001a\u00020\tJ\u000e\u0010\u0014\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\tJ\u0006\u0010\u0015\u001a\u00020\tJ\u000e\u0010\u0016\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\tJ\b\u0010\u0017\u001a\u0004\u0018\u00010\u0018J\u000e\u0010\u0019\u001a\u00020\u000b2\u0006\u0010\u001a\u001a\u00020\u0018J\b\u0010\u001b\u001a\u0004\u0018\u00010\u0018J\u000e\u0010\u001c\u001a\u00020\u000b2\u0006\u0010\u001d\u001a\u00020\u0018J\b\u0010\u001e\u001a\u0004\u0018\u00010\u0018J\u000e\u0010\u001f\u001a\u00020\u000b2\u0006\u0010 \u001a\u00020\u0018J\b\u0010!\u001a\u0004\u0018\u00010\u0018J\u000e\u0010\"\u001a\u00020\u000b2\u0006\u0010 \u001a\u00020\u0018J\u0006\u0010#\u001a\u00020$J\u000e\u0010%\u001a\u00020\u000b2\u0006\u0010&\u001a\u00020$J\u0006\u0010\'\u001a\u00020\tJ\u000e\u0010(\u001a\u00020\u000b2\u0006\u0010)\u001a\u00020\tJ\u0006\u0010*\u001a\u00020\tJ\u000e\u0010+\u001a\u00020\u000b2\u0006\u0010,\u001a\u00020\tJ\u0006\u0010-\u001a\u00020\u000bJ\u0006\u0010.\u001a\u00020\u000bJ\u0006\u0010/\u001a\u00020\tJ\u0014\u00100\u001a\u0010\u0012\u0004\u0012\u00020\u0018\u0012\u0006\u0012\u0004\u0018\u00010\u000101R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00063"}, d2 = {"Lcom/example/ma/utils/PreferencesManager;", "", "context", "Landroid/content/Context;", "<init>", "(Landroid/content/Context;)V", "prefs", "Landroid/content/SharedPreferences;", "getPushNotificationsEnabled", "", "setPushNotificationsEnabled", "", "enabled", "getSoundVibrationEnabled", "setSoundVibrationEnabled", "getTransactionAlertsEnabled", "setTransactionAlertsEnabled", "getBiometricAuthEnabled", "setBiometricAuthEnabled", "getAutoLockEnabled", "setAutoLockEnabled", "getAutoSyncEnabled", "setAutoSyncEnabled", "getUserId", "", "setUserId", "userId", "getUsername", "setUsername", "username", "getAccessToken", "setAccessToken", "token", "getRefreshToken", "setRefreshToken", "getLastSyncTime", "", "setLastSyncTime", "time", "isFirstLaunch", "setFirstLaunch", "isFirst", "isOnboardingCompleted", "setOnboardingCompleted", "completed", "clearUserData", "clearAllData", "isUserLoggedIn", "getAllSettings", "", "Companion", "app_debug"})
public final class PreferencesManager {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREF_NAME = "ma_preferences";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_PUSH_NOTIFICATIONS = "push_notifications_enabled";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_SOUND_VIBRATION = "sound_vibration_enabled";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_TRANSACTION_ALERTS = "transaction_alerts_enabled";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_BIOMETRIC_AUTH = "biometric_auth_enabled";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_AUTO_LOCK = "auto_lock_enabled";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_AUTO_SYNC = "auto_sync_enabled";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_USER_ID = "user_id";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_USERNAME = "username";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_ACCESS_TOKEN = "access_token";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_REFRESH_TOKEN = "refresh_token";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_LAST_SYNC_TIME = "last_sync_time";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_FIRST_LAUNCH = "first_launch";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_ONBOARDING_COMPLETED = "onboarding_completed";
    @org.jetbrains.annotations.NotNull()
    private final android.content.SharedPreferences prefs = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.ma.utils.PreferencesManager.Companion Companion = null;
    
    @javax.inject.Inject()
    public PreferencesManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        super();
    }
    
    public final boolean getPushNotificationsEnabled() {
        return false;
    }
    
    public final void setPushNotificationsEnabled(boolean enabled) {
    }
    
    public final boolean getSoundVibrationEnabled() {
        return false;
    }
    
    public final void setSoundVibrationEnabled(boolean enabled) {
    }
    
    public final boolean getTransactionAlertsEnabled() {
        return false;
    }
    
    public final void setTransactionAlertsEnabled(boolean enabled) {
    }
    
    public final boolean getBiometricAuthEnabled() {
        return false;
    }
    
    public final void setBiometricAuthEnabled(boolean enabled) {
    }
    
    public final boolean getAutoLockEnabled() {
        return false;
    }
    
    public final void setAutoLockEnabled(boolean enabled) {
    }
    
    public final boolean getAutoSyncEnabled() {
        return false;
    }
    
    public final void setAutoSyncEnabled(boolean enabled) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getUserId() {
        return null;
    }
    
    public final void setUserId(@org.jetbrains.annotations.NotNull()
    java.lang.String userId) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getUsername() {
        return null;
    }
    
    public final void setUsername(@org.jetbrains.annotations.NotNull()
    java.lang.String username) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getAccessToken() {
        return null;
    }
    
    public final void setAccessToken(@org.jetbrains.annotations.NotNull()
    java.lang.String token) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRefreshToken() {
        return null;
    }
    
    public final void setRefreshToken(@org.jetbrains.annotations.NotNull()
    java.lang.String token) {
    }
    
    public final long getLastSyncTime() {
        return 0L;
    }
    
    public final void setLastSyncTime(long time) {
    }
    
    public final boolean isFirstLaunch() {
        return false;
    }
    
    public final void setFirstLaunch(boolean isFirst) {
    }
    
    public final boolean isOnboardingCompleted() {
        return false;
    }
    
    public final void setOnboardingCompleted(boolean completed) {
    }
    
    public final void clearUserData() {
    }
    
    public final void clearAllData() {
    }
    
    public final boolean isUserLoggedIn() {
        return false;
    }
    
    /**
     * دریافت تمام تنظیمات به صورت Map برای debugging
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> getAllSettings() {
        return null;
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u000e\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000f\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/example/ma/utils/PreferencesManager$Companion;", "", "<init>", "()V", "PREF_NAME", "", "KEY_PUSH_NOTIFICATIONS", "KEY_SOUND_VIBRATION", "KEY_TRANSACTION_ALERTS", "KEY_BIOMETRIC_AUTH", "KEY_AUTO_LOCK", "KEY_AUTO_SYNC", "KEY_USER_ID", "KEY_USERNAME", "KEY_ACCESS_TOKEN", "KEY_REFRESH_TOKEN", "KEY_LAST_SYNC_TIME", "KEY_FIRST_LAUNCH", "KEY_ONBOARDING_COMPLETED", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}