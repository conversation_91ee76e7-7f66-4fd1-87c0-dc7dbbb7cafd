package com.example.ma.utils;

@kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u00c6\u0002\u0018\u00002\u00020\u0001:\u0001\u0012B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003J\u000e\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fJ\u000e\u0010\r\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000fJ\u0006\u0010\u0010\u001a\u00020\u000fJ\u0010\u0010\u0011\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0005X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\bX\u0082.\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0013"}, d2 = {"Lcom/example/ma/utils/ThemeManager;", "", "<init>", "()V", "PREF_NAME", "", "KEY_THEME_MODE", "prefs", "Landroid/content/SharedPreferences;", "init", "", "context", "Landroid/content/Context;", "setTheme", "themeMode", "Lcom/example/ma/utils/ThemeManager$ThemeMode;", "getCurrentTheme", "applyTheme", "ThemeMode", "app_debug"})
public final class ThemeManager {
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String PREF_NAME = "theme_preferences";
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String KEY_THEME_MODE = "theme_mode";
    private static android.content.SharedPreferences prefs;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.ma.utils.ThemeManager INSTANCE = null;
    
    private ThemeManager() {
        super();
    }
    
    public final void init(@org.jetbrains.annotations.NotNull()
    android.content.Context context) {
    }
    
    public final void setTheme(@org.jetbrains.annotations.NotNull()
    com.example.ma.utils.ThemeManager.ThemeMode themeMode) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.ma.utils.ThemeManager.ThemeMode getCurrentTheme() {
        return null;
    }
    
    private final void applyTheme(com.example.ma.utils.ThemeManager.ThemeMode themeMode) {
    }
    
    @kotlin.Metadata(mv = {2, 2, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0006\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\t\b\u0002\u00a2\u0006\u0004\b\u0002\u0010\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006\u00a8\u0006\u0007"}, d2 = {"Lcom/example/ma/utils/ThemeManager$ThemeMode;", "", "<init>", "(Ljava/lang/String;I)V", "LIGHT", "DARK", "SYSTEM_DEFAULT", "app_debug"})
    public static enum ThemeMode {
        /*public static final*/ LIGHT /* = new LIGHT() */,
        /*public static final*/ DARK /* = new DARK() */,
        /*public static final*/ SYSTEM_DEFAULT /* = new SYSTEM_DEFAULT() */;
        
        ThemeMode() {
        }
        
        @org.jetbrains.annotations.NotNull()
        public static kotlin.enums.EnumEntries<com.example.ma.utils.ThemeManager.ThemeMode> getEntries() {
            return null;
        }
    }
}