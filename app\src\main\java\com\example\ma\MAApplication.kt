package com.example.ma

import android.app.Application
import com.example.ma.utils.AnalyticsManager
import com.example.ma.utils.LogManager
import com.example.ma.utils.ThemeManager
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class MAApplication : Application() {

    @Inject
    lateinit var analyticsManager: AnalyticsManager

    override fun onCreate() {
        super.onCreate()

        // Initialize utilities
        LogManager.init(this)
        ThemeManager.init(this)

        // Initialize Analytics
        analyticsManager.logAppStart()

        LogManager.info("MAApplication", "Application started successfully")
    }
}