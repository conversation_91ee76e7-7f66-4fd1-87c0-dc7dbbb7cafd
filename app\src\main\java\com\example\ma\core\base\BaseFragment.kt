package com.example.ma.core.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.viewbinding.ViewBinding
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

abstract class BaseFragment<VB : ViewBinding, VM : BaseViewModel<*, *>> : Fragment() {
    
    private var _binding: VB? = null
    protected val binding get() = _binding!!
    
    protected abstract val viewModel: VM
    
    protected abstract fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): VB
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = getViewBinding(inflater, container)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupViews()
        observeViewModel()
        setupListeners()
    }
    
    protected open fun setupViews() {
        // Override in subclasses
    }
    
    protected open fun observeViewModel() {
        // Observe loading state
        observeLoading()
        
        // Observe error state
        observeError()
    }
    
    protected open fun setupListeners() {
        // Override in subclasses
    }
    
    private fun observeLoading() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.isLoading.collect { isLoading ->
                    onLoadingChanged(isLoading)
                }
            }
        }
    }
    
    private fun observeError() {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                viewModel.error.collect { error ->
                    error?.let { onError(it) }
                }
            }
        }
    }
    
    protected open fun onLoadingChanged(isLoading: Boolean) {
        // Override in subclasses to show/hide loading indicators
    }
    
    protected open fun onError(error: String) {
        // Override in subclasses to show error messages
        showErrorSnackbar(error)
    }
    
    protected fun showErrorSnackbar(message: String) {
        // Implement error snackbar display
    }
    
    protected fun <T> StateFlow<T>.collectWithLifecycle(
        action: suspend (T) -> Unit
    ) {
        viewLifecycleOwner.lifecycleScope.launch {
            viewLifecycleOwner.repeatOnLifecycle(Lifecycle.State.STARTED) {
                collect(action)
            }
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 