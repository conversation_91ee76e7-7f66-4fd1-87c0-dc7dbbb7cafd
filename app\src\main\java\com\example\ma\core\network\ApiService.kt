package com.example.ma.core.network

import com.example.ma.domain.model.User
import com.example.ma.domain.model.Transaction
import com.example.ma.domain.model.FinancialReport
import retrofit2.http.*

interface ApiService {
    
    // Authentication
    @POST("auth/signup")
    suspend fun signUp(@Body user: User): NetworkResult<User>
    
    @POST("auth/signin")
    suspend fun signIn(@Body credentials: SignInRequest): NetworkResult<SignInResponse>
    
    @POST("auth/signout")
    suspend fun signOut(): NetworkResult<Unit>
    
    // User Management
    @GET("users/profile")
    suspend fun getUserProfile(): NetworkResult<User>
    
    @PUT("users/profile")
    suspend fun updateUserProfile(@Body user: User): NetworkResult<User>
    
    // Transactions
    @GET("transactions")
    suspend fun getTransactions(
        @Query("page") page: Int = 1,
        @Query("limit") limit: Int = 20,
        @Query("category") category: String? = null,
        @Query("start_date") startDate: String? = null,
        @Query("end_date") endDate: String? = null
    ): NetworkResult<List<Transaction>>
    
    @POST("transactions")
    suspend fun createTransaction(@Body transaction: Transaction): NetworkResult<Transaction>
    
    @PUT("transactions/{id}")
    suspend fun updateTransaction(
        @Path("id") id: String,
        @Body transaction: Transaction
    ): NetworkResult<Transaction>
    
    @DELETE("transactions/{id}")
    suspend fun deleteTransaction(@Path("id") id: String): NetworkResult<Unit>
    
    // Financial Reports
    @GET("reports/financial")
    suspend fun getFinancialReport(
        @Query("start_date") startDate: String,
        @Query("end_date") endDate: String
    ): NetworkResult<FinancialReport>
    
    @GET("reports/categories")
    suspend fun getCategoryReport(
        @Query("start_date") startDate: String,
        @Query("end_date") endDate: String
    ): NetworkResult<Map<String, Double>>
}

data class SignInRequest(
    val email: String,
    val password: String
)

data class SignInResponse(
    val user: User,
    val token: String,
    val refreshToken: String
) 