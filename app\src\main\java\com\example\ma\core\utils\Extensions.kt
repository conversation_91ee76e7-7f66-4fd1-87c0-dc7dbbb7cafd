package com.example.ma.core.utils

import android.content.Context
import android.view.View
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.google.android.material.snackbar.Snackbar
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

// Context Extensions
fun Context.showToast(message: String, duration: Int = Toast.LENGTH_SHORT) {
    Toast.makeText(this, message, duration).show()
}

fun Context.showLongToast(message: String) {
    showToast(message, Toast.LENGTH_LONG)
}

// View Extensions
fun View.show() {
    visibility = View.VISIBLE
}

fun View.hide() {
    visibility = View.GONE
}

fun View.invisible() {
    visibility = View.INVISIBLE
}

fun View.isVisible(): Boolean = visibility == View.VISIBLE

fun View.isHidden(): Boolean = visibility == View.GONE

fun View.isInvisible(): Boolean = visibility == View.INVISIBLE

fun View.showIf(condition: Boolean) {
    visibility = if (condition) View.VISIBLE else View.GONE
}

fun View.hideIf(condition: Boolean) {
    visibility = if (condition) View.GONE else View.VISIBLE
}

// Fragment Extensions
fun Fragment.showSnackbar(
    message: String,
    duration: Int = Snackbar.LENGTH_LONG,
    actionText: String? = null,
    action: (() -> Unit)? = null
) {
    val snackbar = Snackbar.make(requireView(), message, duration)
    
    if (actionText != null && action != null) {
        snackbar.setAction(actionText) { action() }
    }
    
    snackbar.show()
}

fun Fragment.showErrorSnackbar(
    message: String,
    duration: Int = Snackbar.LENGTH_LONG,
    actionText: String = "Retry",
    action: (() -> Unit)? = null
) {
    val snackbar = Snackbar.make(requireView(), message, duration)
    
    if (action != null) {
        snackbar.setAction(actionText) { action() }
    }
    
    snackbar.show()
}

// String Extensions
fun String.isValidEmail(): Boolean {
    return android.util.Patterns.EMAIL_ADDRESS.matcher(this).matches()
}

fun String.isValidPhone(): Boolean {
    val cleanPhone = this.replace(Regex("[^\\d+]"), "")
    return cleanPhone.length >= 10 && cleanPhone.length <= 15
}

fun String.capitalizeWords(): String {
    return this.split(" ").joinToString(" ") { word ->
        word.lowercase().replaceFirstChar { it.uppercase() }
    }
}

fun String.truncate(maxLength: Int, suffix: String = "..."): String {
    return if (this.length <= maxLength) this else this.take(maxLength) + suffix
}

// Number Extensions
fun Double.formatCurrency(currencyCode: String = "USD"): String {
    val format = NumberFormat.getCurrencyInstance()
    format.currency = Currency.getInstance(currencyCode)
    return format.format(this)
}

fun Double.formatPercentage(): String {
    return String.format("%.1f%%", this)
}

fun Long.formatFileSize(): String {
    val units = arrayOf("B", "KB", "MB", "GB", "TB")
    var size = this.toDouble()
    var unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.size - 1) {
        size /= 1024
        unitIndex++
    }
    
    return String.format("%.1f %s", size, units[unitIndex])
}

// Date Extensions
fun Date.formatDate(pattern: String = "MMM dd, yyyy"): String {
    val formatter = SimpleDateFormat(pattern, Locale.getDefault())
    return formatter.format(this)
}

fun Date.formatTime(pattern: String = "HH:mm"): String {
    val formatter = SimpleDateFormat(pattern, Locale.getDefault())
    return formatter.format(this)
}

fun Date.formatDateTime(pattern: String = "MMM dd, yyyy HH:mm"): String {
    val formatter = SimpleDateFormat(pattern, Locale.getDefault())
    return formatter.format(this)
}

fun Date.isToday(): Boolean {
    val today = Calendar.getInstance()
    val thisDate = Calendar.getInstance()
    thisDate.time = this
    
    return today.get(Calendar.YEAR) == thisDate.get(Calendar.YEAR) &&
           today.get(Calendar.DAY_OF_YEAR) == thisDate.get(Calendar.DAY_OF_YEAR)
}

fun Date.isYesterday(): Boolean {
    val yesterday = Calendar.getInstance()
    yesterday.add(Calendar.DAY_OF_YEAR, -1)
    val thisDate = Calendar.getInstance()
    thisDate.time = this
    
    return yesterday.get(Calendar.YEAR) == thisDate.get(Calendar.YEAR) &&
           yesterday.get(Calendar.DAY_OF_YEAR) == thisDate.get(Calendar.DAY_OF_YEAR)
}

fun Date.isThisWeek(): Boolean {
    val thisWeek = Calendar.getInstance()
    val thisDate = Calendar.getInstance()
    thisDate.time = this
    
    return thisWeek.get(Calendar.YEAR) == thisDate.get(Calendar.YEAR) &&
           thisWeek.get(Calendar.WEEK_OF_YEAR) == thisDate.get(Calendar.WEEK_OF_YEAR)
}

fun Date.isThisMonth(): Boolean {
    val thisMonth = Calendar.getInstance()
    val thisDate = Calendar.getInstance()
    thisDate.time = this
    
    return thisMonth.get(Calendar.YEAR) == thisDate.get(Calendar.YEAR) &&
           thisMonth.get(Calendar.MONTH) == thisDate.get(Calendar.MONTH)
}

fun Date.isThisYear(): Boolean {
    val thisYear = Calendar.getInstance()
    val thisDate = Calendar.getInstance()
    thisDate.time = this
    
    return thisYear.get(Calendar.YEAR) == thisDate.get(Calendar.YEAR)
}

// List Extensions
fun <T> List<T>.safeGet(index: Int): T? {
    return if (index in indices) this[index] else null
}

fun <T> List<T>.getOrNull(index: Int): T? {
    return safeGet(index)
}

fun <T> List<T>.isNotEmptyOrNull(): Boolean {
    return this.isNotEmpty()
}

fun <T> List<T>.isNullOrEmpty(): Boolean {
    return this.isEmpty()
}

// Collection Extensions
fun <T> Collection<T>?.isNullOrEmpty(): Boolean {
    return this == null || this.isEmpty()
}

fun <T> Collection<T>?.isNotNullOrEmpty(): Boolean {
    return !this.isNullOrEmpty()
}

// Boolean Extensions
fun Boolean.toVisibility(): Int {
    return if (this) View.VISIBLE else View.GONE
}

fun Boolean.toInverseVisibility(): Int {
    return if (this) View.GONE else View.VISIBLE
}

// Resource Extensions
fun Context.getColorCompat(colorRes: Int): Int {
    return androidx.core.content.ContextCompat.getColor(this, colorRes)
}

fun Context.getDrawableCompat(drawableRes: Int): android.graphics.drawable.Drawable? {
    return androidx.core.content.ContextCompat.getDrawable(this, drawableRes)
}

// Time formatting extensions
fun Long.formatRelativeTime(): String {
    val now = System.currentTimeMillis()
    val diff = now - this
    
    val seconds = diff / 1000
    val minutes = seconds / 60
    val hours = minutes / 60
    val days = hours / 24
    val weeks = days / 7
    val months = days / 30
    val years = days / 365
    
    return when {
        years > 0 -> "$years سال پیش"
        months > 0 -> "$months ماه پیش"
        weeks > 0 -> "$weeks هفته پیش"
        days > 0 -> "$days روز پیش"
        hours > 0 -> "$hours ساعت پیش"
        minutes > 0 -> "$minutes دقیقه پیش"
        else -> "همین الان"
    }
}

fun Date.formatRelativeTime(): String {
    return this.time.formatRelativeTime()
} 