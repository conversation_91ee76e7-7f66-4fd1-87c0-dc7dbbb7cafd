package com.example.ma.core.validation

import com.example.ma.domain.model.Transaction
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TransactionValidator @Inject constructor(
    private val validator: Validator
) {
    
    fun validateTransaction(transaction: Transaction): ValidationResult {
        // Validate amount
        val amountValidation = validator.validateAmount(transaction.amount)
        if (amountValidation !is ValidationResult.Success) {
            return amountValidation
        }
        
        // Validate category
        val categoryValidation = validator.validateRequiredField(transaction.category, "Category")
        if (categoryValidation !is ValidationResult.Success) {
            return categoryValidation
        }
        
        // Validate description
        val descriptionValidation = validator.validateFieldLength(transaction.description, "Description", 1, 500)
        if (descriptionValidation !is ValidationResult.Success) {
            return descriptionValidation
        }
        
        // Validate date
        val dateValidation = validator.validateDate(transaction.date, "Transaction date")
        if (dateValidation !is ValidationResult.Success) {
            return dateValidation
        }
        
        // Validate subcategory if provided
        transaction.subcategory?.let { subcategory ->
            val subcategoryValidation = validator.validateFieldLength(subcategory, "Subcategory", 1, 100)
            if (subcategoryValidation !is ValidationResult.Success) {
                return subcategoryValidation
            }
        }
        
        // Validate location if provided
        transaction.location?.let { location ->
            val locationValidation = validator.validateFieldLength(location, "Location", 1, 200)
            if (locationValidation !is ValidationResult.Success) {
                return locationValidation
            }
        }
        
        // Validate tags
        transaction.tags.forEach { tag ->
            val tagValidation = validator.validateFieldLength(tag, "Tag", 1, 50)
            if (tagValidation !is ValidationResult.Success) {
                return tagValidation
            }
        }
        
        return ValidationResult.Success
    }
    
    fun validateTransactionData(
        amount: Double,
        category: String,
        description: String
    ): ValidationResult {
        // Validate amount
        val amountValidation = validator.validateAmount(amount)
        if (amountValidation !is ValidationResult.Success) {
            return amountValidation
        }
        
        // Validate category
        val categoryValidation = validator.validateRequiredField(category, "Category")
        if (categoryValidation !is ValidationResult.Success) {
            return categoryValidation
        }
        
        // Validate description
        val descriptionValidation = validator.validateFieldLength(description, "Description", 1, 500)
        if (descriptionValidation !is ValidationResult.Success) {
            return descriptionValidation
        }
        
        return ValidationResult.Success
    }
    
    fun validateRecurringTransaction(transaction: Transaction): ValidationResult {
        // First validate the basic transaction
        val basicValidation = validateTransaction(transaction)
        if (basicValidation !is ValidationResult.Success) {
            return basicValidation
        }
        
        // Validate recurring pattern
        if (transaction.isRecurring && transaction.recurringPattern == null) {
            return ValidationResult.Error("Recurring pattern is required for recurring transactions")
        }
        
        return ValidationResult.Success
    }
} 