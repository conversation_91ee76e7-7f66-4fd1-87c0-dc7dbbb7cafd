package com.example.ma.core.validation

sealed class ValidationResult {
    object Success : ValidationResult()
    data class Error(val message: String) : ValidationResult()
    
    val isValid: Boolean
        get() = this is Success
    
    val errorMessage: String?
        get() = if (this is <PERSON>rror) message else null
}

sealed class FieldValidationResult {
    object Valid : FieldValidationResult()
    data class Invalid(val message: String) : FieldValidationResult()
    
    val isValid: Boolean
        get() = this is Valid
}

data class FormValidationResult(
    val isValid: Boolean,
    val fieldErrors: Map<String, String> = emptyMap(),
    val generalErrors: List<String> = emptyList()
) {
    companion object {
        fun success() = FormValidationResult(isValid = true)
        fun failure(fieldErrors: Map<String, String> = emptyMap(), generalErrors: List<String> = emptyList()) = 
            FormValidationResult(isValid = false, fieldErrors = fieldErrors, generalErrors = generalErrors)
    }
} 