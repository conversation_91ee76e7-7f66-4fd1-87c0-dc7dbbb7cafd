package com.example.ma.core.validation

import android.util.Patterns
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class Validator @Inject constructor() {
    
    fun validateEmail(email: String): ValidationResult {
        return when {
            email.isBlank() -> ValidationResult.Error("Email cannot be empty")
            !Patterns.EMAIL_ADDRESS.matcher(email).matches() -> ValidationResult.Error("Invalid email format")
            email.length > 100 -> ValidationResult.Error("Email is too long")
            else -> ValidationResult.Success
        }
    }

    fun validateUsername(username: String): ValidationResult {
        return when {
            username.isBlank() -> ValidationResult.Error("Username cannot be empty")
            username.length < 3 -> ValidationResult.Error("Username must be at least 3 characters")
            username.length > 30 -> ValidationResult.Error("Username is too long")
            !username.matches(Regex("^[a-zA-Z0-9_.-]+$")) -> ValidationResult.Error("Username can only contain letters, numbers, dot, dash and underscore")
            else -> ValidationResult.Success
        }
    }
    
    fun validatePassword(password: String): ValidationResult {
        return when {
            password.isBlank() -> ValidationResult.Error("Password cannot be empty")
            password.length < 8 -> ValidationResult.Error("Password must be at least 8 characters")
            password.length > 128 -> ValidationResult.Error("Password is too long")
            !password.any { it.isUpperCase() } -> ValidationResult.Error("Password must contain at least one uppercase letter")
            !password.any { it.isLowerCase() } -> ValidationResult.Error("Password must contain at least one lowercase letter")
            !password.any { it.isDigit() } -> ValidationResult.Error("Password must contain at least one number")
            !password.any { !it.isLetterOrDigit() } -> ValidationResult.Error("Password must contain at least one special character")
            else -> ValidationResult.Success
        }
    }
    
    fun validateName(name: String, fieldName: String = "Name"): ValidationResult {
        return when {
            name.isBlank() -> ValidationResult.Error("$fieldName cannot be empty")
            name.length < 2 -> ValidationResult.Error("$fieldName must be at least 2 characters")
            name.length > 50 -> ValidationResult.Error("$fieldName is too long")
            !name.all { it.isLetter() || it.isWhitespace() } -> ValidationResult.Error("$fieldName can only contain letters and spaces")
            else -> ValidationResult.Success
        }
    }
    
    fun validatePhoneNumber(phone: String): ValidationResult {
        val cleanPhone = phone.replace(Regex("[^\\d+]"), "")
        return when {
            phone.isBlank() -> ValidationResult.Error("Phone number cannot be empty")
            cleanPhone.length < 10 -> ValidationResult.Error("Phone number is too short")
            cleanPhone.length > 15 -> ValidationResult.Error("Phone number is too long")
            !cleanPhone.startsWith("+") && !cleanPhone.all { it.isDigit() } -> ValidationResult.Error("Invalid phone number format")
            else -> ValidationResult.Success
        }
    }
    
    fun validateAmount(amount: Double): ValidationResult {
        return when {
            amount <= 0 -> ValidationResult.Error("Amount must be greater than 0")
            amount > 999999999.99 -> ValidationResult.Error("Amount is too large")
            else -> ValidationResult.Success
        }
    }
    
    fun validateRequiredField(value: String, fieldName: String): ValidationResult {
        return when {
            value.isBlank() -> ValidationResult.Error("$fieldName is required")
            else -> ValidationResult.Success
        }
    }
    
    fun validateFieldLength(value: String, fieldName: String, minLength: Int, maxLength: Int): ValidationResult {
        return when {
            value.length < minLength -> ValidationResult.Error("$fieldName must be at least $minLength characters")
            value.length > maxLength -> ValidationResult.Error("$fieldName cannot exceed $maxLength characters")
            else -> ValidationResult.Success
        }
    }
    
    fun validateUrl(url: String): ValidationResult {
        return when {
            url.isBlank() -> ValidationResult.Error("URL cannot be empty")
            !Patterns.WEB_URL.matcher(url).matches() -> ValidationResult.Error("Invalid URL format")
            else -> ValidationResult.Success
        }
    }
    
    fun validateDate(date: java.util.Date, fieldName: String = "Date"): ValidationResult {
        val now = java.util.Date()
        return when {
            date.after(now) -> ValidationResult.Error("$fieldName cannot be in the future")
            else -> ValidationResult.Success
        }
    }
} 