package com.example.ma.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.example.ma.domain.model.Notification

@Entity(tableName = "notifications")
data class NotificationEntity(
    @PrimaryKey
    val id: String,
    val fromUserId: String,        // فرستنده اعلان
    val toUserId: String,          // گیرنده اعلان
    val senderName: String,        // نام فرستنده
    val senderProfileUrl: String?, // عکس پروفایل فرستنده
    val title: String,
    val message: String,
    val type: String,
    val isRead: Boolean,
    val data: String?,
    val createdAt: Long
) {
    fun toDomain(): Notification {
        return Notification(
            id = id,
            fromUserId = fromUserId,
            toUserId = toUserId,
            senderName = senderName,
            senderProfileUrl = senderProfileUrl,
            title = title,
            message = message,
            type = Notification.NotificationType.valueOf(type),
            isRead = isRead,
            data = data,
            createdAt = createdAt
        )
    }
}

fun Notification.toEntity(): NotificationEntity {
    return NotificationEntity(
        id = id,
        fromUserId = fromUserId,
        toUserId = toUserId,
        senderName = senderName,
        senderProfileUrl = senderProfileUrl,
        title = title,
        message = message,
        type = type.name,
        isRead = isRead,
        data = data,
        createdAt = createdAt
    )
}