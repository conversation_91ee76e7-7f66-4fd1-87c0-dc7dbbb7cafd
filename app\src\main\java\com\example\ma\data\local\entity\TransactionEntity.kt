package com.example.ma.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.example.ma.domain.model.Transaction
import com.example.ma.domain.model.TransactionStatus
import com.example.ma.domain.model.TransactionType
import java.util.Date

@Entity(tableName = "transactions")
data class TransactionEntity(
    @PrimaryKey
    val id: String,
    val userId: String,
    val amount: Double,
    val currency: String,
    val type: String,
    val category: String,
    val subcategory: String?,
    val description: String,
    val date: Long,
    val status: String,
    val createdAt: Long,
    val updatedAt: Long
)

fun TransactionEntity.toDomain(): Transaction {
    return Transaction(
        id = id,
        userId = userId,
        amount = amount,
        currency = currency,
        type = TransactionType.valueOf(type),
        category = category,
        subcategory = subcategory,
        description = description,
        date = Date(date),
        status = TransactionStatus.valueOf(status),
        createdAt = Date(createdAt),
        updatedAt = Date(updatedAt)
    )
}

fun Transaction.toEntity(): TransactionEntity {
    return TransactionEntity(
        id = id,
        userId = userId,
        amount = amount,
        currency = currency,
        type = type.name,
        category = category,
        subcategory = subcategory,
        description = description,
        date = date.time,
        status = status.name,
        createdAt = createdAt.time,
        updatedAt = updatedAt.time
    )
}
