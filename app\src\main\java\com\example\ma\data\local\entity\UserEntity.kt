package com.example.ma.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.example.ma.domain.model.User
import java.util.Date

@Entity(tableName = "users")
data class UserEntity(
    @PrimaryKey
    val id: String,
    val username: String,
    val displayName: String,
    val email: String,
    val phone: String?,
    val profileImageUrl: String?,
    val isActive: <PERSON>olean,
    val createdAt: Long,
    val updatedAt: Long
) {
    fun toDomain(): User {
        return User(
            id = id,
            username = username,
            email = email,
            displayName = displayName,
            phone = phone,
            profileImageUrl = profileImageUrl,
            isActive = isActive,
            createdAt = Date(createdAt),
            updatedAt = Date(updatedAt)
        )
    }
}

fun User.toEntity(): UserEntity {
    return UserEntity(
        id = id,
        username = username,
        displayName = displayName,
        email = email,
        phone = phone,
        profileImageUrl = profileImageUrl,
        isActive = isActive,
        createdAt = createdAt.time,
        updatedAt = updatedAt.time
    )
} 