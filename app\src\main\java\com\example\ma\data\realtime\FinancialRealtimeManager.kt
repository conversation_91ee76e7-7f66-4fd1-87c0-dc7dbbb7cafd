package com.example.ma.data.realtime

import com.example.ma.domain.calculator.FinancialCalculator
import com.example.ma.domain.model.BusinessFinancialSummary
import com.example.ma.domain.model.QuickStats
import com.example.ma.domain.model.Transaction
import com.example.ma.domain.usecase.GetQuickStatsUseCase
import com.example.ma.utils.LogManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدیریت Real-time محاسبات مالی
 * این کلاس تغییرات تراکنش‌ها را رصد کرده و محاسبات مالی را به‌روزرسانی می‌کند
 */
@Singleton
class FinancialRealtimeManager @Inject constructor(
    private val financialCalculator: FinancialCalculator,
    private val getQuickStatsUseCase: GetQuickStatsUseCase
) {
    
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // StateFlows برای real-time data
    private val _financialSummary = MutableStateFlow<BusinessFinancialSummary?>(null)
    val financialSummary: StateFlow<BusinessFinancialSummary?> = _financialSummary.asStateFlow()
    
    private val _quickStats = MutableStateFlow<QuickStats?>(null)
    val quickStats: StateFlow<QuickStats?> = _quickStats.asStateFlow()
    
    private val _isConnected = MutableStateFlow(false)
    val isConnected: StateFlow<Boolean> = _isConnected.asStateFlow()
    
    private val _lastUpdateTime = MutableStateFlow(0L)
    val lastUpdateTime: StateFlow<Long> = _lastUpdateTime.asStateFlow()
    
    /**
     * شروع real-time monitoring
     */
    fun startRealtimeUpdates() {
        scope.launch {
            try {
                LogManager.info("FinancialRealtimeManager", "شروع real-time updates")
                _isConnected.value = true
                
                // بارگذاری اولیه داده‌ها
                refreshFinancialData()
                
                // TODO: اتصال به Supabase Realtime
                // setupSupabaseRealtimeListener()
                
            } catch (e: Exception) {
                LogManager.error("FinancialRealtimeManager", "خطا در شروع real-time updates", e)
                _isConnected.value = false
            }
        }
    }
    
    /**
     * توقف real-time monitoring
     */
    fun stopRealtimeUpdates() {
        scope.launch {
            try {
                LogManager.info("FinancialRealtimeManager", "توقف real-time updates")
                _isConnected.value = false
                
                // TODO: قطع اتصال از Supabase Realtime
                // disconnectSupabaseRealtime()
                
            } catch (e: Exception) {
                LogManager.error("FinancialRealtimeManager", "خطا در توقف real-time updates", e)
            }
        }
    }
    
    /**
     * به‌روزرسانی دستی داده‌های مالی
     */
    fun refreshFinancialData() {
        scope.launch {
            try {
                LogManager.debug("FinancialRealtimeManager", "شروع به‌روزرسانی داده‌های مالی")
                
                // محاسبه خلاصه مالی
                val summaryResult = financialCalculator.calculateCompleteFinancialSummary()
                summaryResult.onSuccess { summary ->
                    _financialSummary.value = summary
                    LogManager.debug("FinancialRealtimeManager", "خلاصه مالی به‌روزرسانی شد")
                }.onFailure { error ->
                    LogManager.error("FinancialRealtimeManager", "خطا در محاسبه خلاصه مالی", error)
                }
                
                // محاسبه آمار سریع
                val statsResult = getQuickStatsUseCase()
                statsResult.onSuccess { stats ->
                    _quickStats.value = stats
                    LogManager.debug("FinancialRealtimeManager", "آمار سریع به‌روزرسانی شد")
                }.onFailure { error ->
                    LogManager.error("FinancialRealtimeManager", "خطا در محاسبه آمار سریع", error)
                }
                
                _lastUpdateTime.value = System.currentTimeMillis()
                
            } catch (e: Exception) {
                LogManager.error("FinancialRealtimeManager", "خطا در به‌روزرسانی داده‌های مالی", e)
            }
        }
    }
    
    /**
     * رسیدگی به تغییرات تراکنش
     * این متد زمانی فراخوانی می‌شود که تراکنش جدیدی اضافه، ویرایش یا حذف شود
     */
    fun onTransactionChanged(transaction: Transaction, changeType: ChangeType) {
        scope.launch {
            try {
                LogManager.info("FinancialRealtimeManager", 
                    "تغییر تراکنش: ${changeType.name} - ${transaction.id}")
                
                // به‌روزرسانی فوری محاسبات
                refreshFinancialData()
                
            } catch (e: Exception) {
                LogManager.error("FinancialRealtimeManager", "خطا در رسیدگی به تغییر تراکنش", e)
            }
        }
    }
    
    /**
     * رسیدگی به تغییرات وضعیت تراکنش (تایید/رد)
     */
    fun onTransactionStatusChanged(transactionId: String, newStatus: TransactionStatus) {
        scope.launch {
            try {
                LogManager.info("FinancialRealtimeManager", 
                    "تغییر وضعیت تراکنش: $transactionId -> ${newStatus.name}")
                
                // به‌روزرسانی فوری محاسبات
                refreshFinancialData()
                
            } catch (e: Exception) {
                LogManager.error("FinancialRealtimeManager", "خطا در رسیدگی به تغییر وضعیت", e)
            }
        }
    }
    
    /**
     * دریافت آخرین خلاصه مالی
     */
    fun getCurrentFinancialSummary(): BusinessFinancialSummary? {
        return _financialSummary.value
    }
    
    /**
     * دریافت آخرین آمار سریع
     */
    fun getCurrentQuickStats(): QuickStats? {
        return _quickStats.value
    }
    
    /**
     * بررسی وضعیت اتصال
     */
    fun isRealtimeConnected(): Boolean {
        return _isConnected.value
    }
    
    /**
     * دریافت زمان آخرین به‌روزرسانی
     */
    fun getLastUpdateTime(): Long {
        return _lastUpdateTime.value
    }
    
    // TODO: پیاده‌سازی اتصال به Supabase Realtime
    private fun setupSupabaseRealtimeListener() {
        // اتصال به Supabase Realtime برای جدول transactions
        // listener برای INSERT, UPDATE, DELETE events
    }
    
    private fun disconnectSupabaseRealtime() {
        // قطع اتصال از Supabase Realtime
    }
}

/**
 * نوع تغییرات تراکنش
 */
enum class ChangeType {
    INSERT,    // اضافه شدن تراکنش جدید
    UPDATE,    // ویرایش تراکنش موجود
    DELETE     // حذف تراکنش
}

/**
 * وضعیت تراکنش
 */
enum class TransactionStatus {
    PENDING, APPROVED, REJECTED, COMPLETED, CANCELLED, FAILED
}

/**
 * Extension functions برای راحتی استفاده
 */
fun FinancialRealtimeManager.observeFinancialSummary(
    scope: CoroutineScope,
    onUpdate: (BusinessFinancialSummary?) -> Unit
) {
    scope.launch {
        financialSummary.collect { summary ->
            onUpdate(summary)
        }
    }
}

fun FinancialRealtimeManager.observeQuickStats(
    scope: CoroutineScope,
    onUpdate: (QuickStats?) -> Unit
) {
    scope.launch {
        quickStats.collect { stats ->
            onUpdate(stats)
        }
    }
}
