package com.example.ma.data.remote

import com.example.ma.domain.model.User
import io.github.jan.supabase.auth.providers.builtin.Email
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import java.util.Date

class AuthRemoteDataSource @Inject constructor(
    private val supabaseClient: SupabaseClient
) {

    private val authorizedUsers = mapOf(
        "ali_kakai" to "علی کاکایی",
        "milad_nasiri" to "میلاد نصیری"
    )

    suspend fun login(username: String, password: String): Result<User> = withContext(Dispatchers.IO) {
        try {
            if (!authorizedUsers.containsKey(username)) {
                return@withContext Result.failure(Exception("نام کاربری یا رمز عبور اشتباه است"))
            }

            val emailAddr = "$<EMAIL>"
            supabaseClient.auth.signInWith(Email) {
                this.email = emailAddr
                this.password = password
            }

            val user = supabaseClient.auth.currentSessionOrNull()?.user
            if (user != null) {
                val displayName = authorizedUsers[username] ?: username
                val domainUser = User(
                    id = user.id,
                    username = username,
                    email = user.email ?: emailAddr,
                    displayName = displayName,
                    phone = user.phone,
                    profileImageUrl = user.userMetadata?.get("avatar_url")?.toString(),
                    isActive = true,
                    createdAt = Date(),
                    updatedAt = Date()
                )
                Result.success(domainUser)
            } else {
                Result.failure(Exception("خطا در ورود - نام کاربری یا رمز عبور اشتباه است"))
            }
        } catch (e: Exception) {
            Result.failure(Exception("خطا در اتصال به سرور: ${e.message}"))
        }
    }

    suspend fun logout(): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            supabaseClient.auth.signOut()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(Exception("خطا در خروج: ${e.message}"))
        }
    }

    suspend fun getCurrentUser(): Result<User?> = withContext(Dispatchers.IO) {
        try {
            val session = supabaseClient.auth.currentSessionOrNull()
            if (session != null) {
                val user = session.user
                val email = user.email ?: ""
                val username = email.replace("@ma-partnership.com", "")
                val displayName = authorizedUsers[username] ?: username

                val domainUser = User(
                    id = user.id,
                    username = username,
                    email = email,
                    displayName = displayName,
                    phone = user.phone,
                    profileImageUrl = user.userMetadata?.get("avatar_url")?.toString(),
                    isActive = true,
                    createdAt = Date(),
                    updatedAt = Date()
                )
                Result.success(domainUser)
            } else {
                Result.success(null)
            }
        } catch (e: Exception) {
            Result.failure(Exception("خطا در دریافت اطلاعات کاربر: ${e.message}"))
        }
    }

    suspend fun updateProfile(user: User): Result<User> = withContext(Dispatchers.IO) {
        try {
            supabaseClient.auth.updateUser(
                data = mapOf(
                    "display_name" to user.displayName,
                    "avatar_url" to (user.profileImageUrl ?: "")
                )
            )
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(Exception("خطا در آپدیت پروفایل: ${e.message}"))
        }
    }

    suspend fun changePassword(oldPassword: String, newPassword: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            supabaseClient.auth.updateUser(password = newPassword)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(Exception("خطا در تغییر رمز عبور: ${e.message}"))
        }
    }
} 