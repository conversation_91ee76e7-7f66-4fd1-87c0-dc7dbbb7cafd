package com.example.ma.data.remote

import com.example.ma.config.AppConfig
import io.github.jan.supabase.createSupabaseClient
import io.github.jan.supabase.auth.Auth
import io.github.jan.supabase.auth.auth
import io.github.jan.supabase.postgrest.Postgrest
import io.github.jan.supabase.postgrest.postgrest
import io.github.jan.supabase.realtime.Realtime
import io.github.jan.supabase.realtime.realtime
import io.github.jan.supabase.storage.Storage
import io.github.jan.supabase.storage.storage
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SupabaseClient @Inject constructor() {

	private val client = createSupabaseClient(
		supabaseUrl = AppConfig.SUPABASE_URL,
		supabaseKey = AppConfig.SUPABASE_ANON_KEY
	) {
		install(Auth)
		install(Postgrest)
		install(Realtime)
		install(Storage)
	}

	val auth: Auth
		get() = client.auth

	val database: Postgrest
		get() = client.postgrest

	val realtime: Realtime
		get() = client.realtime

	val storage: Storage
		get() = client.storage
} 