package com.example.ma.data.remote

import com.example.ma.domain.model.Transaction
import com.example.ma.domain.model.TransactionStatus
import com.example.ma.domain.model.TransactionType
import com.example.ma.domain.repository.TransactionRepository
import java.util.Date
import io.github.jan.supabase.postgrest.postgrest
import io.github.jan.supabase.postgrest.query.Columns
import io.github.jan.supabase.postgrest.query.Order
import io.github.jan.supabase.postgrest.rpc
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import java.util.UUID
import javax.inject.Inject

class TransactionRemoteDataSource @Inject constructor(
    private val supabaseClient: SupabaseClient
) {

    suspend fun createTransaction(transaction: Transaction): Result<Transaction> = withContext(Dispatchers.IO) {
        try {
            val response = supabaseClient.database
                .from("transactions")
                .insert(transaction.toRemoteMap())
                .decodeSingle<Map<String, Any>>()

            val createdTransaction = mapToTransaction(response)
            Result.success(createdTransaction)
        } catch (e: Exception) {
            Result.failure(Exception("خطا در ایجاد تراکنش: ${e.message}"))
        }
    }

    suspend fun getTransactionById(id: String): Result<Transaction?> = withContext(Dispatchers.IO) {
        try {
            val response = supabaseClient.database
                .from("transactions")
                .select {
                    filter { eq("id", id) }
                }
                .decodeList<Map<String, Any>>()

            if (response.isNotEmpty()) {
                val transaction = mapToTransaction(response.first())
                Result.success(transaction)
            } else {
                Result.success(null)
            }
        } catch (e: Exception) {
            Result.failure(Exception("خطا در دریافت تراکنش: ${e.message}"))
        }
    }

    fun getAllTransactions(): Flow<List<Transaction>> = flow {
        try {
            val response = supabaseClient.database
                .from("transactions")
                .select {
                    order(column = "created_at", order = Order.DESCENDING)
                }
                .decodeList<Map<String, Any>>()

            val transactions = response.map { mapToTransaction(it) }
            emit(transactions)
        } catch (e: Exception) {
            emit(emptyList())
        }
    }

    fun getTransactionsByType(type: TransactionType): Flow<List<Transaction>> = flow {
        try {
            val response = supabaseClient.database
                .from("transactions")
                .select {
                    filter { eq("type", type.name) }
                    order(column = "created_at", order = Order.DESCENDING)
                }
                .decodeList<Map<String, Any>>()

            val transactions = response.map { mapToTransaction(it) }
            emit(transactions)
        } catch (e: Exception) {
            emit(emptyList())
        }
    }

    fun getTransactionsByStatus(status: TransactionStatus): Flow<List<Transaction>> = flow {
        try {
            val response = supabaseClient.database
                .from("transactions")
                .select {
                    filter { eq("status", status.name) }
                    order(column = "created_at", order = Order.DESCENDING)
                }
                .decodeList<Map<String, Any>>()

            val transactions = response.map { mapToTransaction(it) }
            emit(transactions)
        } catch (e: Exception) {
            emit(emptyList())
        }
    }

    fun getTransactionsByUserId(userId: String): Flow<List<Transaction>> = flow {
        try {
            val response = supabaseClient.database
                .from("transactions")
                .select {
                    filter { eq("user_id", userId) }
                    order(column = "created_at", order = Order.DESCENDING)
                }
                .decodeList<Map<String, Any>>()

            val transactions = response.map { mapToTransaction(it) }
            emit(transactions)
        } catch (e: Exception) {
            emit(emptyList())
        }
    }

    suspend fun updateTransactionStatus(id: String, status: TransactionStatus): Result<Transaction> = withContext(Dispatchers.IO) {
        try {
            val response = supabaseClient.database
                .from("transactions")
                .update(
                    mapOf(
                        "status" to status.name,
                        "updated_at" to System.currentTimeMillis()
                    )
                ) {
                    filter { eq("id", id) }
                }
                .select(columns = Columns.list("id", "user_id", "type", "amount", "description", "category", "status", "created_at", "updated_at"))
                .decodeSingle<Map<String, Any>>()

            val updatedTransaction = mapToTransaction(response)
            Result.success(updatedTransaction)
        } catch (e: Exception) {
            Result.failure(Exception("خطا در به‌روزرسانی وضعیت تراکنش: ${e.message}"))
        }
    }

    @Deprecated("This is inefficient. Use getFinancialSummaryRpc instead.", ReplaceWith("getFinancialSummaryRpc()"))
    suspend fun getFinancialSummary(): Result<com.example.ma.domain.model.FinancialSummary> = withContext(Dispatchers.IO) {
        try {
            val salesResponse = supabaseClient.database
                .from("transactions")
                .select {
                    filter { eq("type", TransactionType.INCOME.name) }
                }
                .decodeList<Map<String, Any>>()

            val expensesResponse = supabaseClient.database
                .from("transactions")
                .select {
                    filter { eq("type", TransactionType.EXPENSE.name) }
                }
                .decodeList<Map<String, Any>>()

            val withdrawalsResponse = supabaseClient.database
                .from("transactions")
                .select {
                    filter { eq("type", TransactionType.TRANSFER.name) }
                }
                .decodeList<Map<String, Any>>()

            val totalSales = salesResponse.sumOf { (it["amount"] as Number).toDouble() }
            val totalExpenses = expensesResponse.sumOf { (it["amount"] as Number).toDouble() }
            val totalWithdrawals = withdrawalsResponse.sumOf { (it["amount"] as Number).toDouble() }
            val netProfit = totalSales - totalExpenses

            val summary = com.example.ma.domain.model.FinancialSummary(
                totalSales = totalSales,
                totalExpenses = totalExpenses,
                netProfit = netProfit,
                profitMargin = if (totalSales > 0) (netProfit / totalSales) * 100 else 0.0,
                totalTransactions = 0,
                averageTransactionAmount = 0.0,
                cashBalance = 0.0,
                cardBalance = 0.0,
                totalWithdrawals = totalWithdrawals,
                partner1Share = 0.0,
                partner2Share = 0.0
            )
            Result.success(summary)
        } catch (e: Exception) {
            Result.failure(Exception("خطا در محاسبه خلاصه مالی: ${e.message}"))
        }
    }

    suspend fun getFinancialSummaryRpc(): Result<com.example.ma.domain.model.FinancialSummary> = withContext(Dispatchers.IO) {
        try {
            val summary = supabaseClient.database
                .rpc("get_financial_summary")
                .decodeSingle<com.example.ma.domain.model.FinancialSummary>()
            Result.success(summary)
        } catch (e: Exception) {
            Result.failure(Exception("خطا در دریافت خلاصه مالی از سرور: ${e.message}"))
        }
    }

    private fun mapToTransaction(data: Map<String, Any?>): Transaction {
        fun anyToDate(value: Any?): Date {
            return when (value) {
                is Number -> Date(value.toLong())
                is String -> {
                    // TODO: parse ISO8601 if backend returns strings
                    Date()
                }
                else -> Date()
            }
        }

        return Transaction(
            id = data["id"] as String,
            userId = data["user_id"] as String,
            amount = (data["amount"] as? Number)?.toDouble() ?: 0.0,
            currency = data["currency"] as? String ?: "USD",
            type = TransactionType.valueOf(data["type"] as? String ?: "EXPENSE"),
            category = data["category"] as? String ?: "",
            subcategory = data["subcategory"] as? String,
            description = data["description"] as? String ?: "",
            date = anyToDate(data["date"]),
            status = TransactionStatus.valueOf(data["status"] as? String ?: "COMPLETED"),
            createdAt = anyToDate(data["created_at"]),
            updatedAt = anyToDate(data["updated_at"])
        )
    }

    private fun Transaction.toRemoteMap(): Map<String, Any?> {
        return mapOf(
            "id" to id,
            "user_id" to userId,
            "amount" to amount,
            "currency" to currency,
            "type" to type.name,
            "category" to category,
            "subcategory" to subcategory,
            "description" to description,
            "date" to date.time,
            "status" to status.name,
            "created_at" to createdAt.time,
            "updated_at" to updatedAt.time
        )
    }
}
