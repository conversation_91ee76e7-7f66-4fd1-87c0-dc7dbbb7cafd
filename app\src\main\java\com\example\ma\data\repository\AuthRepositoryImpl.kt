package com.example.ma.data.repository

import com.example.ma.data.local.dao.UserDao
import com.example.ma.data.local.entity.toEntity
import com.example.ma.data.remote.AuthRemoteDataSource
import com.example.ma.domain.model.User
import com.example.ma.domain.repository.AuthRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

class AuthRepositoryImpl @Inject constructor(
    private val userDao: UserDao,
    private val remoteDataSource: AuthRemoteDataSource
) : AuthRepository {
    private var currentUser: User? = null

    override suspend fun login(username: String, password: String): Result<User> = withContext(Dispatchers.IO) {
        try {
            val remoteResult = remoteDataSource.login(username, password)
            if (remoteResult.isSuccess) {
                val user = remoteResult.getOrNull()
                if (user != null) {
                    userDao.insertUser(user.toEntity())
                    currentUser = user
                    Result.success(user)
                } else {
                    Result.failure(Exception("خطا در دریافت اطلاعات کاربر"))
                }
            } else {
                Result.failure(remoteResult.exceptionOrNull() ?: Exception("خطا در ورود"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun logout(): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val remoteResult = remoteDataSource.logout()
            if (remoteResult.isSuccess) {
                userDao.clearAllUsers()
                currentUser = null
                Result.success(true)
            } else {
                Result.failure(remoteResult.exceptionOrNull() ?: Exception("خطا در خروج"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun isLoggedIn(): Boolean = currentUser != null

    override suspend fun getCurrentUser(): Result<User> = withContext(Dispatchers.IO) {
        try {
            val remoteResult = remoteDataSource.getCurrentUser()
            if (remoteResult.isSuccess) {
                val user = remoteResult.getOrNull()
                if (user != null) {
                    currentUser = user
                    userDao.insertUser(user.toEntity())
                    Result.success(user)
                } else {
                    Result.failure(Exception("کاربر وارد نشده است"))
                }
            } else {
                currentUser?.let { Result.success(it) } ?: Result.failure(Exception("عدم دسترسی به کاربر جاری"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateProfile(user: User): Result<User> = withContext(Dispatchers.IO) {
        try {
            val remoteResult = remoteDataSource.updateProfile(user)
            if (remoteResult.isSuccess) {
                val updatedUser = remoteResult.getOrNull()
                if (updatedUser != null) {
                    userDao.updateUser(updatedUser.toEntity())
                    currentUser = updatedUser
                    Result.success(updatedUser)
                } else {
                    Result.failure(Exception("خطا در آپدیت پروفایل"))
                }
            } else {
                Result.failure(remoteResult.exceptionOrNull() ?: Exception("خطا در آپدیت پروفایل"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun changePassword(oldPassword: String, newPassword: String): Result<Boolean> = withContext(Dispatchers.IO) {
        try {
            val remoteResult = remoteDataSource.changePassword(oldPassword, newPassword)
            if (remoteResult.isSuccess) {
                Result.success(true)
            } else {
                Result.failure(remoteResult.exceptionOrNull() ?: Exception("خطا در تغییر رمز عبور"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
} 