package com.example.ma.data.repository

import com.example.ma.data.local.dao.NotificationDao
import com.example.ma.data.local.entity.NotificationEntity
import com.example.ma.domain.model.Notification
import com.example.ma.domain.repository.NotificationRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import javax.inject.Inject

class NotificationRepositoryImpl @Inject constructor(
    private val notificationDao: NotificationDao
) : NotificationRepository {

    override fun getAllNotifications(): Flow<List<Notification>> {
        return notificationDao.getAllNotifications().map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override fun getNotificationsByUserId(userId: String): Flow<List<Notification>> {
        return notificationDao.getNotificationsByUserId(userId).map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override suspend fun getNotificationById(id: String): Result<Notification?> = withContext(Dispatchers.IO) {
        try {
            val entity = notificationDao.getNotificationById(id)
            Result.success(entity?.toDomain())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun createNotification(notification: Notification): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val entity = notification.toEntity()
            notificationDao.insertNotification(entity)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun markAsRead(id: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            notificationDao.markAsRead(id)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun deleteNotification(id: String): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val entity = notificationDao.getNotificationById(id)
            if (entity != null) {
                notificationDao.deleteNotification(entity)
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getUnreadCount(): Result<Int> = withContext(Dispatchers.IO) {
        try {
            // This repository does not have current user context; caller should pass userId or use PreferencesManager.
            // For now, return 0 to avoid misuse.
            Result.success(0)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

private fun NotificationEntity.toDomain(): Notification {
    return Notification(
        id = id,
        fromUserId = fromUserId,
        toUserId = toUserId,
        senderName = senderName,
        senderProfileUrl = senderProfileUrl,
        title = title,
        message = message,
        type = Notification.NotificationType.valueOf(type),
        isRead = isRead,
        data = data,
        createdAt = createdAt
    )
}

private fun Notification.toEntity(): NotificationEntity {
    return NotificationEntity(
        id = id,
        fromUserId = fromUserId,
        toUserId = toUserId,
        senderName = senderName,
        senderProfileUrl = senderProfileUrl,
        title = title,
        message = message,
        type = type.name,
        isRead = isRead,
        data = data,
        createdAt = createdAt
    )
} 