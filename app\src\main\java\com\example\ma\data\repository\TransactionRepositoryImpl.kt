package com.example.ma.data.repository

import com.example.ma.data.local.dao.TransactionDao
import com.example.ma.data.local.entity.toDomain
import com.example.ma.data.local.entity.toEntity
import com.example.ma.data.remote.TransactionRemoteDataSource
import com.example.ma.domain.model.Transaction
import com.example.ma.domain.model.TransactionStatus
import com.example.ma.domain.model.TransactionType
import com.example.ma.domain.repository.TransactionRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.first
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TransactionRepositoryImpl @Inject constructor(
    private val localDataSource: TransactionDao,
    private val remoteDataSource: TransactionRemoteDataSource
) : TransactionRepository {

    override fun getTransactionsFlow(): Flow<List<Transaction>> {
        return localDataSource.getAllTransactions().map { entities ->
            entities.map { it.toDomain() }
        }
    }

    override suspend fun createTransaction(transaction: Transaction): Result<Transaction> {
        // 1. Save to local database first for offline-first support
        localDataSource.insertTransaction(transaction.toEntity())

        // 2. Try to save to remote
        val remoteResult = remoteDataSource.createTransaction(transaction)
        return remoteResult.fold(
            onSuccess = { created ->
                localDataSource.insertTransaction(created.toEntity())
                Result.success(created)
            },
            onFailure = {
                // Keep local insert for offline-first; mark as pending sync via a flag/column if needed
                Result.success(transaction)
            }
        )
    }

    override suspend fun getTransactionById(id: String): Result<Transaction> {
        return try {
            val transaction = localDataSource.getTransactionById(id)?.toDomain()
            if (transaction != null) {
                Result.success(transaction)
            } else {
                Result.failure(Exception("Transaction not found"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateTransaction(transaction: Transaction): Result<Transaction> {
        localDataSource.updateTransaction(transaction.toEntity())
        // Also update remote
        return remoteDataSource.updateTransactionStatus(transaction.id, transaction.status)
    }

    override suspend fun deleteTransaction(id: String): Result<Unit> {
        localDataSource.deleteTransaction(id)
        // Also delete from remote
        // remoteDataSource.deleteTransaction(id) // This method doesn't exist yet
        return Result.success(Unit)
    }

    // Newly added methods to align with presentation layer
    override suspend fun getAllTransactions(): Result<List<Transaction>> {
        return try {
            val entities = localDataSource.getAllTransactionsSync()
            Result.success(entities.map { it.toDomain() })
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getTransactionsByType(type: TransactionType): Result<List<Transaction>> {
        return try {
            val entities = localDataSource.getTransactionsByType(type.name).first()
            Result.success(entities.map { it.toDomain() })
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getTransactionsByStatus(status: TransactionStatus): Result<List<Transaction>> {
        return try {
            val entities = localDataSource.getTransactionsByStatus(status.name).first()
            Result.success(entities.map { it.toDomain() })
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun updateTransactionStatus(id: String, status: TransactionStatus): Result<Transaction> {
        val remote = remoteDataSource.updateTransactionStatus(id, status)
        return remote.fold(
            onSuccess = { updated ->
                localDataSource.insertTransaction(updated.toEntity())
                Result.success(updated)
            },
            onFailure = { error ->
                Result.failure(error)
            }
        )
    }

    override suspend fun syncTransactions(): Result<Unit> {
        // This is a simplified sync logic
        // A real implementation would handle conflicts, pending changes, etc.
        try {
            // Fetch latest from remote
            remoteDataSource.getAllTransactions().collect { remoteTransactions ->
                // Save all remote transactions to local, replacing existing ones
                saveTransactionsLocally(remoteTransactions)
            }
            return Result.success(Unit)
        } catch (e: Exception) {
            return Result.failure(e)
        }
    }
    
    override suspend fun saveTransactionsLocally(transactions: List<Transaction>) {
        localDataSource.insertAll(transactions.map { it.toEntity() })
    }
    
    override suspend fun clearLocalTransactions() {
        localDataSource.clearAllTransactions()
    }

    // --- Methods from the old interface that need proper implementation ---

    override suspend fun getTransactions(page: Int, limit: Int, category: String?, type: TransactionType?, startDate: Date?, endDate: Date?): Result<List<Transaction>> {
        // This should be implemented with proper DAO queries
        return Result.failure(UnsupportedOperationException("Not yet implemented"))
    }

    override suspend fun searchTransactions(query: String): Result<List<Transaction>> {
        // This should be implemented with a proper FTS (Full-Text Search) table in Room
        return Result.failure(UnsupportedOperationException("Not yet implemented"))
    }

    override suspend fun getTransactionsByCategory(startDate: Date, endDate: Date): Result<Map<String, Double>> {
        return Result.failure(UnsupportedOperationException("Not yet implemented"))
    }

    override suspend fun getTransactionsByDateRange(startDate: Date, endDate: Date): Result<List<Transaction>> {
        return try {
            val entities = localDataSource.getTransactionsByPeriod(startDate.time, endDate.time)
            Result.success(entities.map { it.toDomain() })
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getTotalIncome(startDate: Date, endDate: Date): Result<Double> {
        return Result.failure(UnsupportedOperationException("Not yet implemented"))
    }

    override suspend fun getTotalExpense(startDate: Date, endDate: Date): Result<Double> {
        return Result.failure(UnsupportedOperationException("Not yet implemented"))
    }

    override suspend fun getNetBalance(startDate: Date, endDate: Date): Result<Double> {
        return Result.failure(UnsupportedOperationException("Not yet implemented"))
    }

    override suspend fun getRecurringTransactions(): Result<List<Transaction>> {
        return Result.failure(UnsupportedOperationException("Not yet implemented"))
    }

    override suspend fun createRecurringTransaction(transaction: Transaction): Result<Transaction> {
        return Result.failure(UnsupportedOperationException("Not yet implemented"))
    }

    override suspend fun updateRecurringTransaction(transaction: Transaction): Result<Transaction> {
        return Result.failure(UnsupportedOperationException("Not yet implemented"))
    }

    override suspend fun getLastSyncTimestamp(): Date? {
        // This would typically be stored in SharedPreferences or a dedicated table
        return null
    }
}
