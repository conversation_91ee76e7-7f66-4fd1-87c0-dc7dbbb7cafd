package com.example.ma.data.repository

import com.example.ma.data.local.dao.UserDao
import com.example.ma.data.local.entity.UserEntity
import com.example.ma.domain.model.User
import com.example.ma.domain.repository.UserRepository
import com.example.ma.utils.PreferencesManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserRepositoryImpl @Inject constructor(
    private val userDao: UserDao,
    private val preferencesManager: PreferencesManager
) : UserRepository {

    override suspend fun getUserById(id: String): User = withContext(Dispatchers.IO) {
        try {
            val entity = userDao.getUserById(id)
            entity?.toDomain() ?: createDummyUser(id)
        } catch (e: Exception) {
            createDummyUser(id)
        }
    }

    override suspend fun getAllUsers(): List<User> = withContext(Dispatchers.IO) {
        try {
            val entities = userDao.getAllUsersSync()
            if (entities.isEmpty()) {
                createDefaultUsers()
            } else {
                entities.map { it.toDomain() }
            }
        } catch (e: Exception) {
            createDefaultUsers()
        }
    }

    override suspend fun updateUser(user: User): Result<User> = withContext(Dispatchers.IO) {
        try {
            val entity = user.toEntity()
            userDao.updateUser(entity)
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    override suspend fun getCurrentUser(): User? = withContext(Dispatchers.IO) {
        try {
            val userId = preferencesManager.getUserId()
            if (userId != null) {
                getUserById(userId)
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    private suspend fun createDefaultUsers(): List<User> {
        val user1 = User(
            id = "user1",
            username = "ali_kakai",
            displayName = "علی کاکایی",
            email = "<EMAIL>",
            phone = "09123456789"
        )
        
        val user2 = User(
            id = "user2", 
            username = "milad_nasiri",
            displayName = "میلاد نصیری",
            email = "<EMAIL>",
            phone = "09123456788"
        )

        try {
            userDao.insertUser(user1.toEntity())
            userDao.insertUser(user2.toEntity())
        } catch (e: Exception) {
        }

        return listOf(user1, user2)
    }

    private fun createDummyUser(id: String): User {
        return User(
            id = id,
            username = "user_$id",
            displayName = "کاربر $id",
            email = "$<EMAIL>"
        )
    }
}

private fun User.toEntity(): UserEntity {
    return UserEntity(
        id = id,
        username = username,
        displayName = displayName,
        email = email,
        phone = phone,
        profileImageUrl = profileImageUrl,
        isActive = isActive,
        createdAt = createdAt.time,
        updatedAt = updatedAt.time
    )
}

private fun UserEntity.toDomain(): User {
    return User(
        id = id,
        username = username,
        displayName = displayName,
        email = email,
        phone = phone,
        profileImageUrl = profileImageUrl,
        isActive = isActive,
        createdAt = java.util.Date(createdAt),
        updatedAt = java.util.Date(updatedAt)
    )
}


