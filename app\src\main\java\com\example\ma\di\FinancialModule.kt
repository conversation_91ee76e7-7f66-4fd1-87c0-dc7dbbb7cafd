package com.example.ma.di

import com.example.ma.domain.calculator.FinancialCalculator
import com.example.ma.domain.repository.TransactionRepository
import com.example.ma.domain.repository.UserRepository
import com.example.ma.domain.usecase.GetFinancialSummaryUseCase
import com.example.ma.domain.usecase.GetPartnerBalanceUseCase
import com.example.ma.domain.usecase.GetQuickStatsUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * Hilt Module برای محاسبات مالی
 */
@Module
@InstallIn(SingletonComponent::class)
object FinancialModule {

    @Provides
    @Singleton
    fun provideFinancialCalculator(
        transactionRepository: TransactionRepository,
        userRepository: UserRepository
    ): FinancialCalculator {
        return FinancialCalculator(transactionRepository, userRepository)
    }

    @Provides
    fun provideGetFinancialSummaryUseCase(
        financialCalculator: FinancialCalculator
    ): GetFinancialSummaryUseCase {
        return GetFinancialSummaryUseCase(financialCalculator)
    }

    @Provides
    fun provideGetPartnerBalanceUseCase(
        transactionRepository: TransactionRepository
    ): GetPartnerBalanceUseCase {
        return GetPartnerBalanceUseCase(transactionRepository)
    }

    @Provides
    fun provideGetQuickStatsUseCase(
        transactionRepository: TransactionRepository
    ): GetQuickStatsUseCase {
        return GetQuickStatsUseCase(transactionRepository)
    }
}
