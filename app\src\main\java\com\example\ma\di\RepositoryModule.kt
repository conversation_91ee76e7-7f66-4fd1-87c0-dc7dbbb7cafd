package com.example.ma.di

import com.example.ma.data.repository.AuthRepositoryImpl
import com.example.ma.data.repository.NotificationRepositoryImpl
import com.example.ma.data.repository.TransactionRepositoryImpl
import com.example.ma.data.repository.UserRepositoryImpl
import com.example.ma.domain.repository.AuthRepository
import com.example.ma.domain.repository.NotificationRepository
import com.example.ma.domain.repository.TransactionRepository
import com.example.ma.domain.repository.UserRepository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class RepositoryModule {

    @Binds
    abstract fun bindAuthRepository(
        authRepositoryImpl: AuthRepositoryImpl
    ): AuthRepository

    @Binds
    abstract fun bindTransactionRepository(
        transactionRepositoryImpl: TransactionRepositoryImpl
    ): TransactionRepository

    @Binds
    abstract fun bindNotificationRepository(
        notificationRepositoryImpl: NotificationRepositoryImpl
    ): NotificationRepository

    @Binds
    abstract fun bindUserRepository(
        userRepositoryImpl: UserRepositoryImpl
    ): UserRepository
} 