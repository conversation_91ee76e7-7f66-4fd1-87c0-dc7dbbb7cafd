package com.example.ma.domain.calculator

import com.example.ma.domain.model.BusinessFinancialSummary
import com.example.ma.domain.model.PartnerBalance
import com.example.ma.domain.model.Transaction
import com.example.ma.domain.model.TransactionType
import com.example.ma.domain.repository.TransactionRepository
import com.example.ma.domain.repository.UserRepository
import com.example.ma.utils.LogManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * کلاس جدید محاسبات مالی برای مدیریت پیشرفته شراکت
 * این کلاس منطق حسابداری را بر اساس آورده شرکا و تقسیم سود ۵۰-۵۰ پیاده‌سازی می‌کند.
 */
@Singleton
class FinancialCalculator @Inject constructor(
    private val transactionRepository: TransactionRepository,
    private val userRepository: UserRepository
) {

    /**
     * متد اصلی برای محاسبه خلاصه مالی کامل کسب‌وکار با منطق جدید حسابداری
     */
    suspend fun calculateCompleteFinancialSummary(): Result<BusinessFinancialSummary> = withContext(Dispatchers.IO) {
        try {
            LogManager.info("FinancialCalculatorV2", "شروع محاسبه خلاصه مالی با منطق جدید")

            val allTransactionsResult = transactionRepository.getAllTransactions()
            if (allTransactionsResult.isFailure) {
                return@withContext Result.failure(allTransactionsResult.exceptionOrNull()!!)
            }
            val allTransactions = allTransactionsResult.getOrNull() ?: emptyList()

            val partners = userRepository.getAllUsers()
            if (partners.size < 2) {
                return@withContext Result.failure(Exception("برای محاسبه شراکت، حداقل دو کاربر لازم است."))
            }

            val partner1 = partners[0]
            val partner2 = partners[1]

            // 1. محاسبه کل درآمد و هزینه کسب‌وکار
            val totalSales = allTransactions
                .filter { it.type == TransactionType.SALE }
                .sumOf { it.amount.toDouble() }

            val totalExpenses = allTransactions
                .filter { it.type == TransactionType.EXPENSE }
                .sumOf { it.amount.toDouble() }

            // 2. محاسبه سود خالص کسب‌وکار
            val netProfit = totalSales - totalExpenses

            // 3. محاسبه سهم هر شریک از سود (50%)
            val profitSharePerPartner = netProfit / 2

            // 4. محاسبه ترازنامه هر شریک
            val partner1Balance = calculatePartnerBalance(allTransactions, partner1.id, partner1.displayName, profitSharePerPartner)
            val partner2Balance = calculatePartnerBalance(allTransactions, partner2.id, partner2.displayName, profitSharePerPartner)

            // 5. تجمیع نتایج برای خلاصه کلی
            val totalBusinessBalance = partner1Balance.totalBalance + partner2Balance.totalBalance
            val totalWithdrawals = partner1Balance.totalWithdrawals + partner2Balance.totalWithdrawals
            
            val summary = BusinessFinancialSummary(
                partner1Balance = partner1Balance,
                partner2Balance = partner2Balance,
                totalBusinessBalance = totalBusinessBalance,
                totalSales = totalSales,
                totalExpenses = totalExpenses,
                totalWithdrawals = totalWithdrawals,
                netProfit = netProfit,
                profitMargin = if (totalSales > 0) (netProfit / totalSales) * 100 else 0.0,
                // سایر فیلدها را می‌توان در صورت نیاز اضافه کرد
                totalCashInBusiness = 0.0, // این موارد نیاز به بازنگری در مدل جدید دارند
                totalCardInBusiness = 0.0,
                totalCashSales = 0.0,
                totalCardSales = 0.0,
                cashToCardRatio = 0.0
            )

            LogManager.info("FinancialCalculatorV2", "محاسبه خلاصه مالی با موفقیت انجام شد. سود خالص: $netProfit")
            Result.success(summary)

        } catch (e: Exception) {
            LogManager.error("FinancialCalculatorV2", "خطا در محاسبه خلاصه مالی", e)
            Result.failure(e)
        }
    }

    /**
     * محاسبه ترازنامه نهایی یک شریک بر اساس منطق جدید
     * ترازنامه = (کل آورده) + (سهم از سود) - (کل برداشت)
     */
    private fun calculatePartnerBalance(
        allTransactions: List<Transaction>,
        partnerId: String,
        partnerName: String,
        profitShare: Double
    ): PartnerBalance {

        // محاسبه کل آورده شریک (تراکنش‌های نوع CAPITAL)
        val totalCapitalContribution = allTransactions
            .filter { it.userId == partnerId && it.type == TransactionType.CAPITAL }
            .sumOf { it.amount.toDouble() }

        // محاسبه کل برداشت شریک (تراکنش‌های نوع WITHDRAWAL)
        val totalWithdrawals = allTransactions
            .filter { it.userId == partnerId && it.type == TransactionType.WITHDRAWAL }
            .sumOf { it.amount.toDouble() }

        // محاسبه موجودی نهایی شریک
        val finalBalance = totalCapitalContribution + profitShare - totalWithdrawals

        return PartnerBalance(
            partnerId = partnerId,
            partnerName = partnerName,
            totalBalance = finalBalance,
            totalCapitalContribution = totalCapitalContribution,
            totalWithdrawals = totalWithdrawals,
            profitShare = profitShare
        )
    }
}
