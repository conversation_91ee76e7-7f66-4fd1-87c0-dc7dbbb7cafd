package com.example.ma.domain.model

import kotlinx.parcelize.Parcelize
import android.os.Parcelable

/**
 * مدل ترازنامه یک شریک بر اساس منطق حسابداری جدید
 */
@Parcelize
data class PartnerBalance(
    val partnerId: String,
    val partnerName: String,
    val totalBalance: Double,               // موجودی نهایی شریک
    val totalCapitalContribution: Double,   // کل آورده نقدی
    val totalWithdrawals: Double,           // کل برداشت‌ها
    val profitShare: Double                 // سهم از سود خالص
) : Parcelable

/**
 * خلاصه مالی کامل کسب‌وکار
 */
@Parcelize
data class BusinessFinancialSummary(
    val partner1Balance: PartnerBalance,
    val partner2Balance: PartnerBalance,
    val totalCashInBusiness: Double,     // کل نقدی کسب‌وکار
    val totalCardInBusiness: Double,     // کل کارتی کسب‌وکار
    val totalBusinessBalance: Double,    // کل موجودی کسب‌وکار
    val totalSales: Double,              // کل فروش‌ها
    val totalCashSales: Double,          // کل فروش نقدی
    val totalCardSales: Double,          // کل فروش کارتی
    val totalExpenses: Double,           // کل هزینه‌ها
    val totalWithdrawals: Double,        // کل برداشت‌ها
    val netProfit: Double,               // سود خالص
    val profitMargin: Double,            // درصد سود
    val cashToCardRatio: Double,         // نسبت نقدی به کارتی
    val lastUpdated: Long = System.currentTimeMillis()
) : Parcelable

/**
 * گزارش نقدی/کارتی
 */
@Parcelize
data class CashCardReport(
    val totalCashSales: Double,
    val totalCardSales: Double,
    val totalCashWithdrawals: Double,
    val totalCardWithdrawals: Double,
    val cashToCardRatio: Double,
    val partner1CashShare: Double,
    val partner1CardShare: Double,
    val partner2CashShare: Double,
    val partner2CardShare: Double,
    val cashFlowTrend: TrendDirection,
    val cardFlowTrend: TrendDirection
) : Parcelable

/**
 * گزارش دوره‌ای
 */
@Parcelize
data class PeriodReport(
    val period: String,
    val startDate: Long,
    val endDate: Long,
    val sales: Double,
    val expenses: Double,
    val profit: Double,
    val profitMargin: Double,  // درصد سود
    val growth: Double,        // رشد نسبت به دوره قبل
    val transactionCount: Int,
    val averageTransactionAmount: Double,
    val trend: TrendDirection
) : Parcelable

/**
 * تحلیل موجودی انبار
 */
@Parcelize
data class InventoryAnalysis(
    val totalBottlesSold: Int,
    val averageBottlePrice: Double,
    val totalRevenue: Double,
    val estimatedCostPerBottle: Double,
    val totalEstimatedCost: Double,
    val grossProfit: Double,
    val profitPerBottle: Double,
    val profitMargin: Double,
    val salesVelocity: Double  // تعداد بطری در روز
) : Parcelable

/**
 * تحلیل عملکرد مالی
 */
@Parcelize
data class FinancialAnalysis(
    val roi: Double,              // بازده سرمایه
    val breakEvenPoint: Double,   // نقطه سربه‌سر
    val cashFlow: Double,         // جریان نقدی
    val efficiency: Double,       // کارایی (فروش/هزینه)
    val liquidityRatio: Double,   // نسبت نقدینگی
    val trend: TrendDirection,    // روند کلی
    val riskLevel: RiskLevel,     // سطح ریسک
    val recommendations: List<String> // توصیه‌ها
) : Parcelable

/**
 * جهت روند
 */
enum class TrendDirection {
    UP,      // صعودی
    DOWN,    // نزولی
    STABLE   // ثابت
}

/**
 * سطح ریسک
 */
enum class RiskLevel {
    LOW,     // کم
    MEDIUM,  // متوسط
    HIGH     // بالا
}

/**
 * نوع گزارش
 */
enum class ReportType {
    DAILY,
    WEEKLY,
    MONTHLY,
    QUARTERLY,
    YEARLY,
    CUSTOM
}

/**
 * فیلتر تراکنش‌ها
 */
@Parcelize
data class TransactionFilter(
    val startDate: Long? = null,
    val endDate: Long? = null,
    val transactionType: TransactionType? = null,
    val paymentMethod: PaymentMethod? = null,
    val partnerId: String? = null,
    val status: TransactionStatus? = null,
    val minAmount: Long? = null,
    val maxAmount: Long? = null,
    val category: String? = null
) : Parcelable

/**
 * آمار سریع
 */
@Parcelize
data class QuickStats(
    val todaySales: Double,
    val todayExpenses: Double,
    val todayProfit: Double,
    val weekSales: Double,
    val weekExpenses: Double,
    val weekProfit: Double,
    val monthSales: Double,
    val monthExpenses: Double,
    val monthProfit: Double,
    val pendingTransactionsCount: Int,
    val lastTransactionTime: Long
) : Parcelable
