package com.example.ma.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

@Parcelize
data class FinancialSummary(
    val totalSales: Double = 0.0,
    val totalExpenses: Double = 0.0,
    val netProfit: Double = 0.0,
    val profitMargin: Double = 0.0,
    val totalTransactions: Int = 0,
    val averageTransactionAmount: Double = 0.0,
    val cashBalance: Double = 0.0,
    val cardBalance: Double = 0.0,
    val totalWithdrawals: Double = 0.0,
    val partner1Share: Double = 0.0,
    val partner2Share: Double = 0.0,
    val lastUpdated: Long = System.currentTimeMillis()
) : Parcelable {
    
    val totalBalance: Double
        get() = cashBalance + cardBalance
    
    val isProfitable: Boolean
        get() = netProfit > 0
    
    val profitPercentage: Double
        get() = if (totalSales > 0) (netProfit / totalSales) * 100 else 0.0
}

@Parcelize
data class FinancialReport(
    val summary: FinancialSummary,
    val period: String,
    val startDate: Long,
    val endDate: Long,
    val transactions: @RawValue List<Transaction> = emptyList(),
    val trends: @RawValue Map<String, Double> = emptyMap(),
    val recommendations: List<String> = emptyList()
) : Parcelable 