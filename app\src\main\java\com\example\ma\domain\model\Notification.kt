package com.example.ma.domain.model

import kotlinx.parcelize.Parcelize
import android.os.Parcelable

@Parcelize
data class Notification(
    val id: String,
    val fromUserId: String,        // فرستنده اعلان
    val toUserId: String,          // گیرنده اعلان
    val senderName: String,        // نام فرستنده
    val senderProfileUrl: String?, // عکس پروفایل فرستنده
    val title: String,
    val message: String,
    val type: NotificationType,
    val isRead: Boolean = false,
    val data: String? = null,
    val createdAt: Long = System.currentTimeMillis()
) : Parcelable {

    enum class NotificationType {
        TRANSACTION_REQUEST,    // درخواست تایید تراکنش
        TRANSACTION_APPROVED,   // تایید تراکنش
        TRANSACTION_REJECTED,   // رد تراکنش
        NEW_TRANSACTION,        // تراکنش جدید
        PROFILE_UPDATE,         // بروزرسانی پروفایل
        SYSTEM_MESSAGE,         // پیام سیستمی
        REMINDER               // یادآوری
    }
} 