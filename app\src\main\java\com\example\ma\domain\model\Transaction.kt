package com.example.ma.domain.model

import java.util.*

data class Transaction(
    val id: String = "",
    val userId: String = "",
    val amount: Double = 0.0,
    val currency: String = "USD",
    val type: TransactionType = TransactionType.EXPENSE,
    val category: String = "",
    val subcategory: String? = null,
    val description: String = "",
    val date: Date = Date(),
    val location: String? = null,
    val tags: List<String> = emptyList(),
    val attachments: List<Attachment> = emptyList(),
    val isRecurring: Boolean = false,
    val recurringPattern: RecurringPattern? = null,
    val status: TransactionStatus = TransactionStatus.COMPLETED,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date()
) {
    
    val isExpense: Boolean
        get() = type == TransactionType.EXPENSE
    
    val isIncome: Boolean
        get() = type == TransactionType.INCOME || type == TransactionType.SALE
    
    val isTransfer: <PERSON>olean
        get() = type == TransactionType.TRANSFER
    
    val formattedAmount: String
        get() = "${if (isExpense) "-" else "+"}${currency}${String.format("%.2f", amount)}"
    
    val absoluteAmount: Double
        get() = kotlin.math.abs(amount)
    
    fun isValid(): Boolean {
        return userId.isNotBlank() && 
               amount > 0 && 
               category.isNotBlank() &&
               description.isNotBlank()
    }
    
    fun matchesSearch(query: String): Boolean {
        val searchQuery = query.lowercase()
        return description.lowercase().contains(searchQuery) ||
               category.lowercase().contains(searchQuery) ||
               subcategory?.lowercase()?.contains(searchQuery) == true ||
               tags.any { it.lowercase().contains(searchQuery) }
    }
    
    companion object {
        fun createEmpty(): Transaction = Transaction()
    }
}

enum class TransactionType {
    INCOME, EXPENSE, TRANSFER, SALE, WITHDRAWAL, CAPITAL
}

enum class TransactionStatus {
    PENDING, APPROVED, REJECTED, COMPLETED, CANCELLED, FAILED
}

data class Attachment(
    val id: String,
    val fileName: String,
    val fileUrl: String,
    val fileType: String,
    val fileSize: Long,
    val uploadedAt: Date = Date()
)

data class RecurringPattern(
    val frequency: RecurringFrequency,
    val interval: Int = 1,
    val endDate: Date? = null,
    val maxOccurrences: Int? = null
)

enum class RecurringFrequency {
    DAILY, WEEKLY, MONTHLY, YEARLY
}

enum class PaymentMethod {
    CASH, CARD, CHECK, TRANSFER, OTHER
}
