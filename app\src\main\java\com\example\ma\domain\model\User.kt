package com.example.ma.domain.model

import java.util.*

data class User(
    val id: String = "",
    val username: String = "",
    val email: String = "",
    val firstName: String = "",
    val lastName: String = "",
    val displayName: String = "",
    val phone: String? = null,
    val profileImageUrl: String? = null,
    val dateOfBirth: Date? = null,
    val createdAt: Date = Date(),
    val updatedAt: Date = Date(),
    val isActive: Boolean = true,
    val isEmailVerified: Boolean = false,
    val isPhoneVerified: Boolean = false,
    val preferences: UserPreferences = UserPreferences()
) {
    
    val fullName: String
        get() = when {
            displayName.isNotBlank() -> displayName
            else -> "$firstName $lastName".trim()
        }
    
    val displayLabel: String
        get() = when {
            displayName.isNotBlank() -> displayName
            fullName.isNotBlank() -> fullName
            else -> if (email.isNotBlank()) email else username
        }
    
    fun isValid(): Boolean {
        return (email.isNotBlank() || username.isNotBlank())
    }
    
    fun getInitials(): String {
        val nameSource = if (displayName.isNotBlank()) displayName else fullName
        val parts = nameSource.trim().split(" ").filter { it.isNotBlank() }
        return when {
            parts.size >= 2 -> "${parts[0].first().uppercase()}${parts[1].first().uppercase()}"
            parts.size == 1 -> parts[0].first().uppercase()
            else -> ""
        }
    }
    
    companion object {
        fun createEmpty(): User = User()
    }
}

data class UserPreferences(
    val currency: String = "USD",
    val language: String = "en",
    val timezone: String = "UTC",
    val notifications: NotificationSettings = NotificationSettings(),
    val privacy: PrivacySettings = PrivacySettings()
)

data class NotificationSettings(
    val pushEnabled: Boolean = true,
    val emailEnabled: Boolean = true,
    val smsEnabled: Boolean = false,
    val transactionAlerts: Boolean = true,
    val budgetAlerts: Boolean = true,
    val reportAlerts: Boolean = false
)

data class PrivacySettings(
    val profileVisibility: ProfileVisibility = ProfileVisibility.PRIVATE,
    val transactionHistory: TransactionVisibility = TransactionVisibility.PRIVATE,
    val analyticsSharing: Boolean = false
)

enum class ProfileVisibility {
    PUBLIC, PRIVATE, FRIENDS_ONLY
}

enum class TransactionVisibility {
    PUBLIC, PRIVATE, SHARED
} 