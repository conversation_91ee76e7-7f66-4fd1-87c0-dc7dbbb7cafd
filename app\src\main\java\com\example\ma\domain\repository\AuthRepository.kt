package com.example.ma.domain.repository

import com.example.ma.domain.model.User

interface AuthRepository {
    suspend fun login(username: String, password: String): Result<User>
    suspend fun logout(): Result<Boolean>
    suspend fun isLoggedIn(): <PERSON>olean
    suspend fun getCurrentUser(): Result<User>
    suspend fun updateProfile(user: User): Result<User>
    suspend fun changePassword(oldPassword: String, newPassword: String): Result<Boolean>
} 