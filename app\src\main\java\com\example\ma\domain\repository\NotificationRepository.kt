package com.example.ma.domain.repository

import com.example.ma.domain.model.Notification
import kotlinx.coroutines.flow.Flow

/**
 * Repository interface برای اعلانات
 */
interface NotificationRepository {
    
    /**
     * دریافت تمام اعلانات
     */
    fun getAllNotifications(): Flow<List<Notification>>
    
    /**
     * دریافت اعلانات کاربر
     */
    fun getNotificationsByUserId(userId: String): Flow<List<Notification>>
    
    /**
     * دریافت اعلان بر اساس ID
     */
    suspend fun getNotificationById(id: String): Result<Notification?>
    
    /**
     * ایجاد اعلان جدید
     */
    suspend fun createNotification(notification: Notification): Result<Unit>
    
    /**
     * علامت‌گذاری اعلان به عنوان خوانده شده
     */
    suspend fun markAsRead(id: String): Result<Unit>
    
    /**
     * حذف اعلان
     */
    suspend fun deleteNotification(id: String): Result<Unit>
    
    /**
     * دریافت تعداد اعلانات خوانده نشده
     */
    suspend fun getUnreadCount(): Result<Int>
} 