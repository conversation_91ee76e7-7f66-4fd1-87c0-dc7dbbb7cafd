package com.example.ma.domain.repository

import com.example.ma.domain.model.Transaction
import com.example.ma.domain.model.TransactionStatus
import com.example.ma.domain.model.TransactionType
import kotlinx.coroutines.flow.Flow
import java.util.*

interface TransactionRepository {
    
    // CRUD Operations
    suspend fun createTransaction(transaction: Transaction): Result<Transaction>
    suspend fun getTransactionById(id: String): Result<Transaction>
    suspend fun updateTransaction(transaction: Transaction): Result<Transaction>
    suspend fun deleteTransaction(id: String): Result<Unit>

    // New: Common operations used by presentation
    suspend fun getAllTransactions(): Result<List<Transaction>>
    suspend fun getTransactionsByType(type: TransactionType): Result<List<Transaction>>
    suspend fun getTransactionsByStatus(status: TransactionStatus): Result<List<Transaction>>
    suspend fun updateTransactionStatus(id: String, status: TransactionStatus): Result<Transaction>
    
    // Query Operations
    suspend fun getTransactions(
        page: Int = 1,
        limit: Int = 20,
        category: String? = null,
        type: TransactionType? = null,
        startDate: Date? = null,
        endDate: Date? = null
    ): Result<List<Transaction>>
    
    suspend fun searchTransactions(query: String): Result<List<Transaction>>
    
    // Analytics & Reports
    suspend fun getTransactionsByCategory(
        startDate: Date,
        endDate: Date
    ): Result<Map<String, Double>>
    
    suspend fun getTransactionsByDateRange(
        startDate: Date,
        endDate: Date
    ): Result<List<Transaction>>
    
    suspend fun getTotalIncome(startDate: Date, endDate: Date): Result<Double>
    suspend fun getTotalExpense(startDate: Date, endDate: Date): Result<Double>
    suspend fun getNetBalance(startDate: Date, endDate: Date): Result<Double>
    
    // Recurring Transactions
    suspend fun getRecurringTransactions(): Result<List<Transaction>>
    suspend fun createRecurringTransaction(transaction: Transaction): Result<Transaction>
    suspend fun updateRecurringTransaction(transaction: Transaction): Result<Transaction>
    
    // Local Storage
    fun getTransactionsFlow(): Flow<List<Transaction>>
    suspend fun saveTransactionsLocally(transactions: List<Transaction>)
    suspend fun clearLocalTransactions()
    
    // Sync
    suspend fun syncTransactions(): Result<Unit>
    suspend fun getLastSyncTimestamp(): Date?
}
