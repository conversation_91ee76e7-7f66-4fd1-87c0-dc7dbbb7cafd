package com.example.ma.domain.usecase

import com.example.ma.domain.model.Notification
import com.example.ma.domain.model.Transaction
import com.example.ma.domain.model.TransactionStatus
import com.example.ma.domain.repository.NotificationRepository
import com.example.ma.domain.repository.TransactionRepository
import java.util.UUID
import javax.inject.Inject

class CreateTransactionUseCase @Inject constructor(
    private val transactionRepository: TransactionRepository,
    private val notificationRepository: NotificationRepository
) {
    suspend operator fun invoke(transaction: Transaction, currentUserId: String, partnerUserId: String, currentUserDisplayName: String, currentUserAvatarUrl: String?): Result<Unit> {
        // Basic validation
        if (transaction.amount <= 0) {
            return Result.failure(IllegalArgumentException("مبلغ تراکنش باید بیشتر از صفر باشد."))
        }
        if (transaction.description.isBlank()) {
            return Result.failure(IllegalArgumentException("توضیحات تراکنش الزامی است."))
        }

        // Force PENDING status until partner approves
        val pendingTx = transaction.copy(
            status = TransactionStatus.PENDING
        )

        val createResult = transactionRepository.createTransaction(pendingTx)
        if (createResult.isFailure) return Result.failure(createResult.exceptionOrNull()!!)

        // Create notification to partner
        val notification = Notification(
            id = UUID.randomUUID().toString(),
            fromUserId = currentUserId,
            toUserId = partnerUserId,
            senderName = currentUserDisplayName,
            senderProfileUrl = currentUserAvatarUrl,
            title = "درخواست تایید تراکنش",
            message = pendingTx.description,
            type = Notification.NotificationType.TRANSACTION_REQUEST,
            isRead = false,
            data = pendingTx.id,
            createdAt = System.currentTimeMillis()
        )
        val notifResult = notificationRepository.createNotification(notification)
        return notifResult
    }
}
