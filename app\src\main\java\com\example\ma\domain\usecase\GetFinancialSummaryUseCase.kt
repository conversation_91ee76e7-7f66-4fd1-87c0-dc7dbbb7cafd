package com.example.ma.domain.usecase

import com.example.ma.domain.calculator.FinancialCalculator
import com.example.ma.domain.model.BusinessFinancialSummary
import com.example.ma.utils.LogManager
import javax.inject.Inject

/**
 * Use Case برای دریافت خلاصه مالی کامل
 */
class GetFinancialSummaryUseCase @Inject constructor(
    private val financialCalculator: FinancialCalculator
) {
    suspend operator fun invoke(): Result<BusinessFinancialSummary> {
        return try {
            LogManager.info("GetFinancialSummaryUseCase", "درخواست خلاصه مالی")
            
            val result = financialCalculator.calculateCompleteFinancialSummary()
            
            result.onSuccess { summary ->
                LogManager.info("GetFinancialSummaryUseCase", 
                    "خلاصه مالی با موفقیت محاسبه شد - کل موجودی: ${summary.totalBusinessBalance}")
            }.onFailure { error ->
                LogManager.error("GetFinancialSummaryUseCase", "خطا در محاسبه خلاصه مالی", error)
            }
            
            result
            
        } catch (e: Exception) {
            LogManager.error("GetFinancialSummaryUseCase", "خطای غیرمنتظره", e)
            Result.failure(e)
        }
    }
}
