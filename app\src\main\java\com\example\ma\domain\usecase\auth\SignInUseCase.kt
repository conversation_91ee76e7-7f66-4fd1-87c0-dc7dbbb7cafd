package com.example.ma.domain.usecase.auth

import com.example.ma.domain.model.User
import com.example.ma.domain.repository.AuthRepository
import com.example.ma.core.validation.ValidationResult
import com.example.ma.core.validation.Validator
import javax.inject.Inject

class SignInUseCase @Inject constructor(
    private val authRepository: AuthRepository,
    private val validator: Valida<PERSON>
) {
    
    suspend operator fun invoke(username: String, password: String): Result<User> {
        val usernameValidation = validator.validateUsername(username)
        val passwordValidation = validator.validatePassword(password)
        
        if (usernameValidation !is ValidationResult.Success) {
            return Result.failure(IllegalArgumentException(usernameValidation.errorMessage ?: "Username validation failed"))
        }
        
        if (passwordValidation !is ValidationResult.Success) {
            return Result.failure(IllegalArgumentException(passwordValidation.errorMessage ?: "Password validation failed"))
        }
        
        return authRepository.login(username, password)
    }
    
    suspend fun isUserLoggedIn(): Boolean {
        return authRepository.isLoggedIn()
    }
    
    suspend fun getCurrentUser(): User? {
        return authRepository.getCurrentUser().getOrNull()
    }
} 