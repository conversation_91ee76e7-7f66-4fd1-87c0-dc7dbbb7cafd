package com.example.ma.domain.usecase.transaction

import com.example.ma.domain.model.Transaction
import com.example.ma.domain.model.TransactionType
import com.example.ma.domain.model.RecurringPattern
import com.example.ma.domain.repository.TransactionRepository
import com.example.ma.domain.repository.UserRepository
import com.example.ma.core.validation.ValidationResult
import com.example.ma.core.validation.TransactionValidator
import javax.inject.Inject
import java.util.*

class CreateTransactionUseCase @Inject constructor(
    private val transactionRepository: TransactionRepository,
    private val userRepository: UserRepository,
    private val validator: TransactionValidator
) {
    
    suspend operator fun invoke(
        amount: Double,
        type: TransactionType,
        category: String,
        description: String,
        date: Date = Date(),
        subcategory: String? = null,
        location: String? = null,
        tags: List<String> = emptyList(),
        isRecurring: Boolean = false,
        recurringPattern: RecurringPattern? = null
    ): Result<Transaction> {
        
        // Get current user
        val currentUser = userRepository.getCurrentUser()
            ?: return Result.failure(IllegalStateException("User not logged in"))
        
        // Create transaction object
        val transaction = Transaction(
            userId = currentUser.id,
            amount = amount,
            type = type,
            category = category,
            subcategory = subcategory,
            description = description,
            date = date,
            location = location,
            tags = tags,
            isRecurring = isRecurring,
            recurringPattern = recurringPattern
        )
        
        // Validate transaction
        val validationResult = validator.validateTransaction(transaction)
        if (validationResult !is ValidationResult.Success) {
            return Result.failure(IllegalArgumentException(validationResult.errorMessage ?: "Validation failed"))
        }
        
        return try {
            val createdTransaction = transactionRepository.createTransaction(transaction).getOrThrow()
            
            // If recurring, create the recurring pattern
            if (isRecurring && recurringPattern != null) {
                transactionRepository.createRecurringTransaction(createdTransaction).getOrThrow()
            }
            
            Result.success(createdTransaction)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun createRecurringTransaction(transaction: Transaction): Result<Transaction> {
        return transactionRepository.createRecurringTransaction(transaction)
    }
    
    suspend fun validateTransactionData(
        amount: Double,
        category: String,
        description: String
    ): ValidationResult {
        return validator.validateTransactionData(amount, category, description)
    }
} 