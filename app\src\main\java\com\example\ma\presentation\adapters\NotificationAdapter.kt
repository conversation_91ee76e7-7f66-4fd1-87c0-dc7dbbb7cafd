package com.example.ma.presentation.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.ma.R
import com.example.ma.domain.model.Notification
import com.example.ma.domain.model.Notification.NotificationType
import com.example.ma.core.utils.formatRelativeTime
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import com.google.android.material.chip.Chip
import com.google.android.material.textview.MaterialTextView

/**
 * Adapter برای نمایش اعلانات در RecyclerView
 */
class NotificationAdapter(
	private val onNotificationClick: (Notification) -> Unit,
	private val onMarkAsRead: (Notification) -> Unit,
	private val onApprove: (Notification) -> Unit,
	private val onReject: (Notification) -> Unit
) : ListAdapter<Notification, NotificationAdapter.NotificationViewHolder>(NotificationDiffCallback()) {

	override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NotificationViewHolder {
		val view = LayoutInflater.from(parent.context)
			.inflate(R.layout.item_notification, parent, false)
		return NotificationViewHolder(view, onNotificationClick, onMarkAsRead, onApprove, onReject)
	}

	override fun onBindViewHolder(holder: NotificationViewHolder, position: Int) {
		holder.bind(getItem(position))
	}

	class NotificationViewHolder(
		itemView: View,
		private val onNotificationClick: (Notification) -> Unit,
		private val onMarkAsRead: (Notification) -> Unit,
		private val onApprove: (Notification) -> Unit,
		private val onReject: (Notification) -> Unit
	) : RecyclerView.ViewHolder(itemView) {

		private val cardNotification: MaterialCardView = itemView.findViewById(R.id.cardNotification)
		private val tvTitle: MaterialTextView = itemView.findViewById(R.id.tvNotificationTitle)
		private val tvMessage: MaterialTextView = itemView.findViewById(R.id.tvNotificationMessage)
		private val tvDate: MaterialTextView = itemView.findViewById(R.id.tvNotificationTime)
		private val tvType: Chip = itemView.findViewById(R.id.chipNotificationType)
		private val indicatorUnread: View = itemView.findViewById(R.id.indicatorUnread)
		private val btnApprove: MaterialButton? = itemView.findViewById(R.id.btnApprove)
		private val btnReject: MaterialButton? = itemView.findViewById(R.id.btnReject)

		fun bind(notification: Notification) {
			tvTitle.text = notification.title
			tvMessage.text = notification.message
			tvDate.text = notification.createdAt.formatRelativeTime()
			tvType.text = getNotificationTypeText(notification.type)

			indicatorUnread.visibility = if (notification.isRead) View.GONE else View.VISIBLE

			// Action buttons only for transaction requests
			if (notification.type == NotificationType.TRANSACTION_REQUEST) {
				btnApprove?.visibility = View.VISIBLE
				btnReject?.visibility = View.VISIBLE
				btnApprove?.setOnClickListener { onApprove(notification) }
				btnReject?.setOnClickListener { onReject(notification) }
			} else {
				btnApprove?.visibility = View.GONE
				btnReject?.visibility = View.GONE
			}

			cardNotification.setOnClickListener {
				onNotificationClick(notification)
				if (!notification.isRead) {
					onMarkAsRead(notification)
				}
			}
		}

		private fun getNotificationTypeText(type: NotificationType): String {
			return when (type) {
				NotificationType.TRANSACTION_REQUEST -> "درخواست تراکنش"
				NotificationType.TRANSACTION_APPROVED -> "تراکنش تایید شد"
				NotificationType.TRANSACTION_REJECTED -> "تراکنش رد شد"
				NotificationType.NEW_TRANSACTION -> "تراکنش جدید"
				NotificationType.PROFILE_UPDATE -> "بروزرسانی پروفایل"
				NotificationType.SYSTEM_MESSAGE -> "پیام سیستمی"
				NotificationType.REMINDER -> "یادآوری"
				else -> "سایر"
			}
		}
	}

	private class NotificationDiffCallback : DiffUtil.ItemCallback<Notification>() {
		override fun areItemsTheSame(oldItem: Notification, newItem: Notification): Boolean {
			return oldItem.id == newItem.id
		}

		override fun areContentsTheSame(oldItem: Notification, newItem: Notification): Boolean {
			return oldItem == newItem
		}
	}
} 