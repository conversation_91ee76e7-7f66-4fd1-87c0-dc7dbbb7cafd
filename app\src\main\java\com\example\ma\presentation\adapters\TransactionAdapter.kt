package com.example.ma.presentation.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.ma.databinding.ItemTransactionBinding
import com.example.ma.domain.model.Transaction
import com.example.ma.domain.model.TransactionType
import com.example.ma.domain.model.TransactionStatus
import java.text.SimpleDateFormat
import java.util.*

class TransactionAdapter(
    private val onTransactionClick: (Transaction) -> Unit,
    private val onTransactionLongClick: (Transaction) -> Boolean
) : ListAdapter<Transaction, TransactionAdapter.TransactionViewHolder>(TransactionDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TransactionViewHolder {
        val binding = ItemTransactionBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return TransactionViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TransactionViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class TransactionViewHolder(
        private val binding: ItemTransactionBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onTransactionClick(getItem(position))
                }
            }

            binding.root.setOnLongClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onTransactionLongClick(getItem(position))
                } else false
            }
        }

        fun bind(transaction: Transaction) {
            binding.apply {
                // Transaction icon based on type
                val iconRes = when (transaction.type) {
                    TransactionType.INCOME -> com.example.ma.R.drawable.ic_income
                    TransactionType.EXPENSE -> com.example.ma.R.drawable.ic_expense
                    TransactionType.TRANSFER -> com.example.ma.R.drawable.ic_transfer
                    TransactionType.SALE -> com.example.ma.R.drawable.ic_sales
                    TransactionType.WITHDRAWAL -> com.example.ma.R.drawable.ic_expense
                    TransactionType.CAPITAL -> com.example.ma.R.drawable.ic_income
                }
                ivTransactionType.setImageResource(iconRes)

                // Transaction amount with color coding
                tvAmount.text = transaction.formattedAmount
                tvAmount.setTextColor(
                    binding.root.context.getColor(
                        when (transaction.type) {
                            TransactionType.INCOME -> com.example.ma.R.color.income_green
                            TransactionType.EXPENSE -> com.example.ma.R.color.expense_red
                            TransactionType.TRANSFER -> com.example.ma.R.color.transfer_blue
                            TransactionType.SALE -> com.example.ma.R.color.income_green
                            TransactionType.WITHDRAWAL -> com.example.ma.R.color.expense_red
                            TransactionType.CAPITAL -> com.example.ma.R.color.income_green
                        }
                    )
                )

                // Transaction details
                tvDescription.text = transaction.description
                tvCategory.text = transaction.category
                tvSubcategory.text = transaction.subcategory ?: ""
                tvSubcategory.visibility = if (transaction.subcategory.isNullOrBlank()) ViewGroup.GONE else ViewGroup.VISIBLE

                // Transaction date
                val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
                tvDate.text = dateFormat.format(transaction.date)

                // Transaction time
                val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
                tvTime.text = timeFormat.format(transaction.date)

                // Location (if available)
                if (transaction.location.isNullOrBlank()) {
                    tvLocation.visibility = ViewGroup.GONE
                } else {
                    tvLocation.visibility = ViewGroup.VISIBLE
                    tvLocation.text = transaction.location
                }

                // Tags
                if (transaction.tags.isEmpty()) {
                    chipGroupTags.visibility = ViewGroup.GONE
                } else {
                    chipGroupTags.visibility = ViewGroup.VISIBLE
                    chipGroupTags.removeAllViews()
                    transaction.tags.take(3).forEach { tag ->
                        val chip = com.google.android.material.chip.Chip(binding.root.context)
                        chip.text = tag
                        chip.isCheckable = false
                        chipGroupTags.addView(chip)
                    }
                }

                // Recurring indicator
                ivRecurring.visibility = if (transaction.isRecurring) ViewGroup.VISIBLE else ViewGroup.GONE

                // Status indicator
                val statusColor = when (transaction.status) {
                    TransactionStatus.COMPLETED -> com.example.ma.R.color.success_green
                    TransactionStatus.PENDING -> com.example.ma.R.color.warning_orange
                    TransactionStatus.APPROVED -> com.example.ma.R.color.success_green
                    TransactionStatus.REJECTED -> com.example.ma.R.color.danger_red
                    TransactionStatus.CANCELLED -> com.example.ma.R.color.danger_red
                    TransactionStatus.FAILED -> com.example.ma.R.color.danger_red
                }
                statusIndicator.setBackgroundColor(binding.root.context.getColor(statusColor))
            }
        }
    }

    private class TransactionDiffCallback : DiffUtil.ItemCallback<Transaction>() {
        override fun areItemsTheSame(oldItem: Transaction, newItem: Transaction): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Transaction, newItem: Transaction): Boolean {
            return oldItem == newItem
        }
    }
} 