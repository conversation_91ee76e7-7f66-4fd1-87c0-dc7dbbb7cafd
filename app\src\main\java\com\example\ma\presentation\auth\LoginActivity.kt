package com.example.ma.presentation.auth

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.ma.R
import com.example.ma.presentation.main.MainActivity
import com.example.ma.domain.model.LoginResult
import com.google.android.material.button.MaterialButton
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textview.MaterialTextView
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * صفحه ورود به سیستم
 * این صفحه احراز هویت کاربران را انجام می‌دهد
 */
@AndroidEntryPoint
class LoginActivity : AppCompatActivity() {

    private val viewModel: LoginViewModel by viewModels()

    // UI Elements
    private lateinit var usernameEditText: TextInputEditText
    private lateinit var passwordEditText: TextInputEditText
    private lateinit var loginButton: MaterialButton
    private lateinit var errorTextView: MaterialTextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login)

        setupUI()
        observeViewModel()

        // بررسی اینکه آیا کاربر قبلاً وارد شده یا نه
        lifecycleScope.launch {
            val isLoggedIn = viewModel.isUserLoggedIn()
            if (isLoggedIn) {
                navigateToMain()
            }
        }
    }
    
    private fun setupUI() {
        // پیدا کردن view ها
        usernameEditText = findViewById(R.id.etUsername)
        passwordEditText = findViewById(R.id.etPassword)
        loginButton = findViewById(R.id.btnLogin)
        errorTextView = findViewById(R.id.tvError)

        // تنظیم دکمه ورود
        loginButton.setOnClickListener {
            val username = usernameEditText.text.toString().trim()
            val password = passwordEditText.text.toString().trim()

            if (validateInput(username, password)) {
                hideError()
                showLoading(true)
                viewModel.login(username, password)
            }
        }

        // پاک کردن خطاها هنگام تایپ
        usernameEditText.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) hideError()
        }

        passwordEditText.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) hideError()
        }
    }
    
    private fun observeViewModel() {
        viewModel.loginResult.observe(this) { result ->
            when (result) {
                is LoginResult.Success -> {
                    showLoading(false)
                    hideError()
                    showSuccess("خوش آمدید ${result.user.displayName}")
                    navigateToMain()
                }
                is LoginResult.Error -> {
                    showLoading(false)
                    showError(result.message)
                    enableLoginButton()
                }
                is LoginResult.Loading -> {
                    showLoading(true)
                    disableLoginButton()
                }
            }
        }
    }
    
    private fun validateInput(username: String, password: String): Boolean {
        return when {
            username.isEmpty() -> {
                showError("نام کاربری را وارد کنید")
                usernameEditText.requestFocus()
                false
            }
            password.isEmpty() -> {
                showError("رمز عبور را وارد کنید")
                passwordEditText.requestFocus()
                false
            }
            username.length < 3 -> {
                showError("نام کاربری باید حداقل 3 کاراکتر باشد")
                usernameEditText.requestFocus()
                false
            }
            password.length < 3 -> {
                showError("رمز عبور باید حداقل 3 کاراکتر باشد")
                passwordEditText.requestFocus()
                false
            }
            !isValidUsername(username) -> {
                showError("نام کاربری معتبر نیست")
                usernameEditText.requestFocus()
                false
            }
            else -> true
        }
    }

    private fun isValidUsername(username: String): Boolean {
        // فقط دو کاربر مجاز: ali_kakai و milad_nasiri
        return username == "ali_kakai" || username == "milad_nasiri"
    }

    private fun showError(message: String) {
        errorTextView.text = message
        errorTextView.visibility = View.VISIBLE
    }

    private fun hideError() {
        errorTextView.visibility = View.GONE
    }

    private fun showLoading(show: Boolean) {
        loginButton.isEnabled = !show
        if (show) {
            loginButton.text = "در حال ورود..."
        } else {
            loginButton.text = "ورود"
        }
    }

    private fun showSuccess(message: String) {
        // نمایش پیام موفقیت
        // TODO: پیاده‌سازی Toast یا Snackbar
    }

    private fun enableLoginButton() {
        loginButton.isEnabled = true
    }

    private fun disableLoginButton() {
        loginButton.isEnabled = false
    }

    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
    }
} 