package com.example.ma.presentation.auth

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.ma.domain.model.LoginResult
import com.example.ma.domain.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _loginResult = MutableLiveData<LoginResult>()
    val loginResult: LiveData<LoginResult> = _loginResult

    fun login(username: String, password: String) {
        viewModelScope.launch {
            _loginResult.value = LoginResult.Loading
            try {
                val result = authRepository.login(username, password)
                result.fold(
                    onSuccess = { user ->
                        _loginResult.value = LoginResult.Success(user)
                    },
                    onFailure = { exception ->
                        _loginResult.value = LoginResult.Error(exception.message ?: "خطا در ورود")
                    }
                )
            } catch (e: Exception) {
                _loginResult.value = LoginResult.Error(e.message ?: "خطا در ورود")
            }
        }
    }

    suspend fun isUserLoggedIn(): Boolean = withContext(Dispatchers.IO) {
        authRepository.isLoggedIn()
    }
} 