package com.example.ma.presentation.financial

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.ma.domain.model.Transaction
import com.example.ma.domain.repository.TransactionRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class TransactionsViewModel @Inject constructor(
    private val transactionRepository: TransactionRepository
) : ViewModel() {

    private val _transactions = MutableLiveData<List<Transaction>>()
    val transactions: LiveData<List<Transaction>> = _transactions

    init {
        loadTransactions()
    }

    private fun loadTransactions() {
        viewModelScope.launch {
            try {
                val result = transactionRepository.getAllTransactions()
                result.fold(
                    onSuccess = { transactionList ->
                        _transactions.value = transactionList
                    },
                    onFailure = { exception ->
                        // Handle error
                        _transactions.value = emptyList()
                    }
                )
            } catch (e: Exception) {
                _transactions.value = emptyList()
            }
        }
    }
} 