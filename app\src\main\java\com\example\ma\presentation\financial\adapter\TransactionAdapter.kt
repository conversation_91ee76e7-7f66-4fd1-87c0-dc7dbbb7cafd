package com.example.ma.presentation.financial.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.ma.databinding.ItemTransactionBinding
import com.example.ma.domain.model.Transaction
import com.example.ma.domain.model.TransactionStatus
import com.example.ma.domain.model.TransactionType
import java.text.SimpleDateFormat
import java.util.*

class TransactionAdapter : ListAdapter<Transaction, TransactionAdapter.TransactionViewHolder>(TransactionDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TransactionViewHolder {
        val binding = ItemTransactionBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return TransactionViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TransactionViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class TransactionViewHolder(
        private val binding: ItemTransactionBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(transaction: Transaction) {
            binding.apply {
                // Removed tvTransactionType and tvStatus which don't exist in layout
                tvAmount.text = formatCurrency(transaction.amount)
                tvDescription.text = transaction.description
                tvDate.text = formatDate(transaction.createdAt)
                
                // تنظیم رنگ بر اساس نوع تراکنش
                val colorRes = when (transaction.type) {
                    TransactionType.SALE -> android.R.color.holo_green_dark
                    TransactionType.EXPENSE -> android.R.color.holo_red_dark
                    TransactionType.WITHDRAWAL -> android.R.color.holo_orange_dark
                    TransactionType.INCOME -> android.R.color.holo_blue_dark
                    TransactionType.TRANSFER -> android.R.color.holo_blue_dark
                    TransactionType.CAPITAL -> android.R.color.holo_blue_dark
                }
                tvAmount.setTextColor(itemView.context.getColor(colorRes))
            }
        }

        private fun formatCurrency(amount: Double): String {
            return String.format("% ,.0f تومان", amount)
        }

        private fun formatDate(date: Date): String {
            val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
            return dateFormat.format(date)
        }
    }

    private class TransactionDiffCallback : DiffUtil.ItemCallback<Transaction>() {
        override fun areItemsTheSame(oldItem: Transaction, newItem: Transaction): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Transaction, newItem: Transaction): Boolean {
            return oldItem == newItem
        }
    }
} 