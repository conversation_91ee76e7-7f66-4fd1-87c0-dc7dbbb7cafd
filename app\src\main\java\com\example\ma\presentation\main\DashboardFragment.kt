package com.example.ma.presentation.main

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.example.ma.R
import com.example.ma.domain.model.BusinessFinancialSummary
import com.google.android.material.button.MaterialButton
import com.google.android.material.textview.MaterialTextView
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Fragment برای نمایش داشبورد اصلی
 */
@AndroidEntryPoint
class DashboardFragment : Fragment() {

	private val viewModel: DashboardViewModel by viewModels()

	// UI Elements
	private lateinit var tvTotalSales: MaterialTextView
	private lateinit var tvTotalExpenses: MaterialTextView
	private lateinit var tvNetProfit: MaterialTextView
	private lateinit var tvMyShare: MaterialTextView
	private lateinit var tvPartnerShare: MaterialTextView
	private lateinit var tvInventoryCount: MaterialTextView
	private lateinit var btnRegisterTransaction: MaterialButton
	private lateinit var btnReports: MaterialButton

	override fun onCreateView(
		inflater: LayoutInflater,
		container: ViewGroup?,
		savedInstanceState: Bundle?
	): View? {
		return inflater.inflate(R.layout.fragment_dashboard, container, false)
	}

	override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
		super.onViewCreated(view, savedInstanceState)
		
		setupViews(view)
		observeViewModel()
		loadFinancialSummary()
	}

	private fun setupViews(view: View) {
		tvTotalSales = view.findViewById(R.id.tvTotalSales)
		tvTotalExpenses = view.findViewById(R.id.tvTotalExpenses)
		tvNetProfit = view.findViewById(R.id.tvNetProfit)
		tvMyShare = view.findViewById(R.id.tvMyShare)
		tvPartnerShare = view.findViewById(R.id.tvPartnerShare)
		tvInventoryCount = view.findViewById(R.id.tvInventoryCount)
		btnRegisterTransaction = view.findViewById(R.id.btnRegisterTransaction)
		btnReports = view.findViewById(R.id.btnReports)

		// تنظیم click listeners
		btnRegisterTransaction.setOnClickListener {
			// TODO: Navigate to transaction registration
		}

		btnReports.setOnClickListener {
			// TODO: Navigate to reports
		}
	}

	private fun observeViewModel() {
		viewModel.financialSummary.observe(viewLifecycleOwner) { summary ->
			summary?.let { updateFinancialDisplay(it) }
		}

		viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
			// TODO: Show/hide loading indicator
		}

		viewModel.error.observe(viewLifecycleOwner) { error ->
			error?.let {
				// TODO: Show error message
			}
		}
	}

	private fun loadFinancialSummary() {
		viewModel.loadFinancialSummary()
	}

	private fun updateFinancialDisplay(summary: BusinessFinancialSummary) {
		tvTotalSales.text = formatCurrency(summary.totalSales)
		tvTotalExpenses.text = formatCurrency(summary.totalExpenses)
		tvNetProfit.text = formatCurrency(summary.netProfit)
		tvMyShare.text = formatCurrency(summary.partner1Balance.profitShare)
		tvPartnerShare.text = formatCurrency(summary.partner2Balance.profitShare)
		tvInventoryCount.text = "0" // TODO: Get actual inventory count
	}

	private fun formatCurrency(amount: Double): String {
		return String.format("% ,.0f تومان", amount)
	}
} 