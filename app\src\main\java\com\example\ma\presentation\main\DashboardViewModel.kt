package com.example.ma.presentation.main

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.ma.data.realtime.FinancialRealtimeManager
import com.example.ma.data.realtime.observeFinancialSummary
import com.example.ma.data.realtime.observeQuickStats
import com.example.ma.domain.model.BusinessFinancialSummary
import com.example.ma.domain.model.QuickStats
import com.example.ma.domain.usecase.GetFinancialSummaryUseCase
import com.example.ma.domain.usecase.GetQuickStatsUseCase
import com.example.ma.utils.LogManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel برای DashboardFragment - آپدیت شده با محاسبات جدید
 */
@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val getFinancialSummaryUseCase: GetFinancialSummaryUseCase,
    private val getQuickStatsUseCase: GetQuickStatsUseCase,
    private val financialRealtimeManager: FinancialRealtimeManager
) : ViewModel() {

    private val _financialSummary = MutableLiveData<BusinessFinancialSummary?>()
    val financialSummary: LiveData<BusinessFinancialSummary?> = _financialSummary

    private val _quickStats = MutableLiveData<QuickStats?>()
    val quickStats: LiveData<QuickStats?> = _quickStats

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _isRealtimeConnected = MutableLiveData<Boolean>()
    val isRealtimeConnected: LiveData<Boolean> = _isRealtimeConnected

    init {
        setupRealtimeObservers()
        startRealtimeUpdates()
    }

    /**
     * بارگذاری خلاصه مالی کامل
     */
    fun loadFinancialSummary() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                LogManager.debug("DashboardViewModel", "شروع بارگذاری خلاصه مالی")

                val result = getFinancialSummaryUseCase()
                result.onSuccess { summary ->
                    _financialSummary.value = summary
                    LogManager.debug("DashboardViewModel", "خلاصه مالی بارگذاری شد - موجودی کل: ${summary.totalBusinessBalance}")
                }.onFailure { error ->
                    _error.value = error.message ?: "خطا در بارگذاری اطلاعات"
                    LogManager.error("DashboardViewModel", "خطا در بارگذاری خلاصه مالی", error)
                }
            } catch (e: Exception) {
                _error.value = "خطا در اتصال به سرور: ${e.message}"
                LogManager.error("DashboardViewModel", "خطای غیرمنتظره", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * بارگذاری آمار سریع
     */
    fun loadQuickStats() {
        viewModelScope.launch {
            try {
                LogManager.debug("DashboardViewModel", "شروع بارگذاری آمار سریع")

                val result = getQuickStatsUseCase()
                result.onSuccess { stats ->
                    _quickStats.value = stats
                    LogManager.debug("DashboardViewModel", "آمار سریع بارگذاری شد - فروش امروز: ${stats.todaySales}")
                }.onFailure { error ->
                    LogManager.error("DashboardViewModel", "خطا در بارگذاری آمار سریع", error)
                    // آمار سریع اختیاری است، خطا را نمایش نمی‌دهیم
                }
            } catch (e: Exception) {
                LogManager.error("DashboardViewModel", "خطای غیرمنتظره در آمار سریع", e)
            }
        }
    }

    /**
     * بارگذاری کامل داده‌ها
     */
    fun loadDashboardData() {
        loadFinancialSummary()
        loadQuickStats()
    }

    /**
     * شروع real-time updates
     */
    private fun startRealtimeUpdates() {
        financialRealtimeManager.startRealtimeUpdates()
    }

    /**
     * تنظیم observers برای real-time data
     */
    private fun setupRealtimeObservers() {
        // مشاهده تغییرات خلاصه مالی
        financialRealtimeManager.observeFinancialSummary(viewModelScope) { summary ->
            _financialSummary.value = summary
        }

        // مشاهده تغییرات آمار سریع
        financialRealtimeManager.observeQuickStats(viewModelScope) { stats ->
            _quickStats.value = stats
        }

        // مشاهده وضعیت اتصال
        viewModelScope.launch {
            financialRealtimeManager.isConnected.collect { isConnected ->
                _isRealtimeConnected.value = isConnected
            }
        }
    }

    /**
     * به‌روزرسانی دستی داده‌ها
     */
    fun refreshData() {
        financialRealtimeManager.refreshFinancialData()
    }

    /**
     * پاک کردن خطا
     */
    fun clearError() {
        _error.value = null
    }

    override fun onCleared() {
        super.onCleared()
        financialRealtimeManager.stopRealtimeUpdates()
    }
}