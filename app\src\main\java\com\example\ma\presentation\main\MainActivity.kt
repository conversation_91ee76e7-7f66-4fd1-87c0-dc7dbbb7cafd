package com.example.ma.presentation.main

import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.GravityCompat
import androidx.drawerlayout.widget.DrawerLayout
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.navigateUp
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.navigation.ui.setupWithNavController
import com.bumptech.glide.Glide
import com.example.ma.R
import com.example.ma.config.AppConfig
import com.example.ma.domain.model.User
import com.example.ma.presentation.auth.LoginActivity
import com.example.ma.presentation.test.ConnectionTestActivity
import com.google.android.material.navigation.NavigationView
import com.google.android.material.textview.MaterialTextView
import dagger.hilt.android.AndroidEntryPoint
import de.hdodenhof.circleimageview.CircleImageView

/**
 * MainActivity - صفحه اصلی اپلیکیشن
 * مدیریت Navigation Drawer و Fragment ها
 */
@AndroidEntryPoint
class MainActivity : AppCompatActivity() {

    private lateinit var drawerLayout: DrawerLayout
    private lateinit var navController: NavController
    private lateinit var appBarConfiguration: AppBarConfiguration
    private lateinit var navigationView: NavigationView

    private val viewModel: MainViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        setupNavigation()
        setupNavigationDrawer()
        setupDebugMenu()
        observeViewModel()
        loadUserProfile()
    }

    private fun setupNavigation() {
        val navHostFragment = supportFragmentManager
            .findFragmentById(R.id.nav_host_fragment) as NavHostFragment
        navController = navHostFragment.navController

        // تنظیم AppBarConfiguration برای Navigation Drawer
        appBarConfiguration = AppBarConfiguration(
            setOf(
                R.id.dashboardFragment,
                R.id.transactionsFragment,
                R.id.profileFragment,
                R.id.notificationsFragment,
                R.id.reportsFragment
            ),
            drawerLayout
        )

        setupActionBarWithNavController(navController, appBarConfiguration)
    }

    private fun setupNavigationDrawer() {
        drawerLayout = findViewById(R.id.drawer_layout)
        navigationView = findViewById(R.id.nav_view)

        // تنظیم NavigationView با NavController
        navigationView.setupWithNavController(navController)

        // تنظیم Navigation Item Selected Listener
        navigationView.setNavigationItemSelectedListener { menuItem ->
            when (menuItem.itemId) {
                R.id.nav_dashboard -> {
                    navController.navigate(R.id.dashboardFragment)
                    drawerLayout.closeDrawer(GravityCompat.START)
                    true
                }
                R.id.nav_transactions -> {
                    navController.navigate(R.id.transactionsFragment)
                    drawerLayout.closeDrawer(GravityCompat.START)
                    true
                }
                R.id.nav_profile -> {
                    navController.navigate(R.id.profileFragment)
                    drawerLayout.closeDrawer(GravityCompat.START)
                    true
                }
                R.id.nav_notifications -> {
                    navController.navigate(R.id.notificationsFragment)
                    drawerLayout.closeDrawer(GravityCompat.START)
                    true
                }
                R.id.nav_reports -> {
                    navController.navigate(R.id.reportsFragment)
                    drawerLayout.closeDrawer(GravityCompat.START)
                    true
                }
                R.id.nav_connection_test -> {
                    openConnectionTest()
                    drawerLayout.closeDrawer(GravityCompat.START)
                    true
                }
                R.id.nav_logout -> {
                    handleLogout()
                    true
                }
                else -> false
            }
        }
    }

    private fun observeViewModel() {
        viewModel.logoutResult.observe(this) { shouldLogout ->
            if (shouldLogout) {
                navigateToLogin()
            }
        }

        viewModel.currentUser.observe(this) { user ->
            user?.let { updateNavigationHeader(it) }
        }
    }

    private fun setupDebugMenu() {
        // نمایش آیتم تست اتصال فقط در حالت debug
        if (AppConfig.DEBUG_MODE) {
            val menu = navigationView.menu
            menu.findItem(R.id.nav_connection_test)?.isVisible = true
        }
    }

    private fun openConnectionTest() {
        val intent = Intent(this, ConnectionTestActivity::class.java)
        startActivity(intent)
    }

    private fun handleLogout() {
        viewModel.logout()
    }

    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    override fun onSupportNavigateUp(): Boolean {
        return navController.navigateUp(appBarConfiguration) || super.onSupportNavigateUp()
    }

    override fun onBackPressed() {
        if (drawerLayout.isDrawerOpen(GravityCompat.START)) {
            drawerLayout.closeDrawer(GravityCompat.START)
        } else {
            super.onBackPressed()
        }
    }

    /**
     * بارگذاری پروفایل کاربر
     */
    private fun loadUserProfile() {
        viewModel.loadUserProfile()
    }

    /**
     * آپدیت Navigation Header با اطلاعات کاربر
     */
    private fun updateNavigationHeader(user: User) {
        val headerView = navigationView.getHeaderView(0)
        val ivProfileImage = headerView.findViewById<CircleImageView>(R.id.ivProfileImage)
        val tvHeaderUserName = headerView.findViewById<MaterialTextView>(R.id.tvHeaderUserName)
        val tvHeaderUserEmail = headerView.findViewById<MaterialTextView>(R.id.tvHeaderUserEmail)

        // آپدیت نام کاربر
        tvHeaderUserName.text = user.displayName

        // آپدیت ایمیل
        tvHeaderUserEmail.text = user.email ?: "ایمیل ثبت نشده"

        // آپدیت عکس پروفایل
        user.profileImageUrl?.let { imageUrl ->
            Glide.with(this)
                .load(imageUrl)
                .placeholder(R.drawable.ic_person)
                .error(R.drawable.ic_person)
                .into(ivProfileImage)
        } ?: run {
            ivProfileImage.setImageResource(R.drawable.ic_person)
        }

        // اضافه کردن click listener برای عکس پروفایل
        ivProfileImage.setOnClickListener {
            navController.navigate(R.id.profileFragment)
            drawerLayout.closeDrawer(GravityCompat.START)
        }
    }
}