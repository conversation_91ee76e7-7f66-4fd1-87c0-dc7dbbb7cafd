package com.example.ma.presentation.main

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.ma.domain.model.User
import com.example.ma.domain.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel برای MainActivity
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _logoutResult = MutableLiveData<Boolean>()
    val logoutResult: LiveData<Boolean> = _logoutResult

    private val _currentUser = MutableLiveData<User?>()
    val currentUser: LiveData<User?> = _currentUser

    /**
     * بارگذاری پروفایل کاربر
     */
    fun loadUserProfile() {
        viewModelScope.launch {
            try {
                val result = authRepository.getCurrentUser()
                if (result.isSuccess) {
                    _currentUser.value = result.getOrNull()
                }
            } catch (e: Exception) {
                // در صورت خطا، کاربر null می‌شود
                _currentUser.value = null
            }
        }
    }

    /**
     * خروج کاربر از سیستم
     */
    fun logout() {
        viewModelScope.launch {
            try {
                authRepository.logout()
                _logoutResult.value = true
            } catch (e: Exception) {
                // حتی اگر خطا رخ دهد، کاربر را خارج می‌کنیم
                _logoutResult.value = true
            }
        }
    }
}