package com.example.ma.presentation.notifications

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.ma.domain.model.Notification
import com.example.ma.domain.repository.NotificationRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class NotificationViewModel @Inject constructor(
    private val notificationRepository: NotificationRepository
) : ViewModel() {

    private val _notifications = MutableLiveData<List<Notification>>()
    val notifications: LiveData<List<Notification>> = _notifications

    init {
        loadNotifications()
    }

    private fun loadNotifications() {
        viewModelScope.launch {
            try {
                // دریافت اعلانات از Repository
                val result = notificationRepository.getAllNotifications()
                result.collect { notifications ->
                    _notifications.value = notifications
                }
            } catch (e: Exception) {
                // در صورت خطا، لیست خالی نمایش داده می‌شود
                _notifications.value = emptyList()
            }
        }
    }

    fun markAsRead(notificationId: String) {
        viewModelScope.launch {
            // TODO: علامت‌گذاری اعلان به عنوان خوانده شده
        }
    }

    fun deleteNotification(notificationId: String) {
        viewModelScope.launch {
            // TODO: حذف اعلان
        }
    }
} 