package com.example.ma.presentation.notifications

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.ma.R
import com.example.ma.domain.model.Notification
import com.example.ma.domain.model.TransactionStatus
import com.example.ma.presentation.adapters.NotificationAdapter
import com.example.ma.utils.PreferencesManager
import com.google.android.material.textview.MaterialTextView
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * Fragment برای نمایش اعلانات
 */
@AndroidEntryPoint
class NotificationsFragment : Fragment() {

    private val viewModel: NotificationsViewModel by viewModels()
    private lateinit var notificationAdapter: NotificationAdapter

    @Inject lateinit var preferencesManager: PreferencesManager

    // UI Elements
    private lateinit var recyclerView: RecyclerView
    private lateinit var tvEmptyState: MaterialTextView

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_notifications, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupViews(view)
        setupRecyclerView()
        observeViewModel()
        loadNotifications()
    }

    private fun setupViews(view: View) {
        recyclerView = view.findViewById(R.id.recyclerViewNotifications)
        tvEmptyState = view.findViewById(R.id.tvEmptyState)
    }

    private fun setupRecyclerView() {
        notificationAdapter = NotificationAdapter(
            onNotificationClick = { notification ->
                handleNotificationClick(notification)
            },
            onMarkAsRead = { notification ->
                markAsRead(notification)
            },
            onApprove = { notification ->
                handleApproval(notification, true)
            },
            onReject = { notification ->
                handleApproval(notification, false)
            }
        )

        recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = notificationAdapter
        }
    }

    private fun observeViewModel() {
        viewModel.notifications.observe(viewLifecycleOwner) { notifications ->
            updateNotificationsList(notifications)
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // TODO: Show/hide loading indicator
        }

        viewModel.error.observe(viewLifecycleOwner) { error ->
            error?.let {
                // TODO: Show error message
            }
        }
    }

    private fun loadNotifications() {
        viewModel.loadNotifications()
    }

    private fun updateNotificationsList(notifications: List<Notification>) {
        if (notifications.isEmpty()) {
            recyclerView.visibility = View.GONE
            tvEmptyState.visibility = View.VISIBLE
        } else {
            recyclerView.visibility = View.VISIBLE
            tvEmptyState.visibility = View.GONE
            notificationAdapter.submitList(notifications)
        }
    }

    private fun handleNotificationClick(notification: Notification) {
        // TODO: Handle navigation if needed
    }

    private fun markAsRead(notification: Notification) {
        viewModel.markAsRead(notification.id)
    }

    private fun handleApproval(notification: Notification, approved: Boolean) {
        val transactionId = notification.data ?: return
        val newStatus = if (approved) TransactionStatus.APPROVED else TransactionStatus.REJECTED
        viewModel.processTransactionApproval(notification, approved)
        // The repository/status update should trigger realtime/flows to refresh notifications and transactions
    }
} 