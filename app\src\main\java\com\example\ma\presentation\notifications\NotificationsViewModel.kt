package com.example.ma.presentation.notifications

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.ma.domain.model.Notification
import com.example.ma.domain.model.TransactionStatus
import com.example.ma.domain.repository.NotificationRepository
import com.example.ma.domain.repository.TransactionRepository
import com.example.ma.utils.PreferencesManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel برای NotificationsFragment
 */
@HiltViewModel
class NotificationsViewModel @Inject constructor(
    private val notificationRepository: NotificationRepository,
    private val transactionRepository: TransactionRepository,
    private val preferencesManager: PreferencesManager
) : ViewModel() {

    private val _notifications = MutableLiveData<List<Notification>>()
    val notifications: LiveData<List<Notification>> = _notifications

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    /**
     * بارگذاری اعلانات
     */
    fun loadNotifications() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                // Collect flow once for initial load
                notificationRepository.getAllNotifications().collect { list ->
                    _notifications.postValue(list)
                }
            } catch (e: Exception) {
                _error.value = "خطا در اتصال به سرور: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * علامت‌گذاری اعلان به عنوان خوانده شده
     */
    fun markAsRead(notificationId: String) {
        viewModelScope.launch {
            try {
                val result = notificationRepository.markAsRead(notificationId)
                if (result.isFailure) {
                    _error.value = result.exceptionOrNull()?.message ?: "خطا در علامت‌گذاری"
                }
            } catch (e: Exception) {
                _error.value = "خطا در اتصال به سرور: ${e.message}"
            }
        }
    }

    /**
     * پردازش تایید/رد تراکنش از روی اعلان
     */
    fun processTransactionApproval(notification: Notification, approved: Boolean) {
        viewModelScope.launch {
            try {
                val transactionId = notification.data ?: return@launch
                val status = if (approved) TransactionStatus.APPROVED else TransactionStatus.REJECTED

                val updateResult = transactionRepository.updateTransactionStatus(transactionId, status)
                if (updateResult.isFailure) {
                    _error.value = updateResult.exceptionOrNull()?.message ?: "خطا در آپدیت وضعیت"
                    return@launch
                }

                // Mark original notification as read
                notificationRepository.markAsRead(notification.id)

                // Notify the requester about the decision
                val currentUserId = preferencesManager.getUserId() ?: ""
                val currentUsername = preferencesManager.getUsername() ?: ""

                val outcomeNotification = Notification(
                    id = java.util.UUID.randomUUID().toString(),
                    fromUserId = currentUserId,
                    toUserId = notification.fromUserId, // back to requester
                    senderName = currentUsername,
                    senderProfileUrl = null,
                    title = if (approved) "تراکنش تایید شد" else "تراکنش رد شد",
                    message = notification.message,
                    type = if (approved) Notification.NotificationType.TRANSACTION_APPROVED else Notification.NotificationType.TRANSACTION_REJECTED,
                    isRead = false,
                    data = transactionId,
                    createdAt = System.currentTimeMillis()
                )
                notificationRepository.createNotification(outcomeNotification)
            } catch (e: Exception) {
                _error.value = "خطا در پردازش تایید/رد: ${e.message}"
            }
        }
    }

    /**
     * حذف اعلان
     */
    fun deleteNotification(notificationId: String) {
        viewModelScope.launch {
            try {
                val result = notificationRepository.deleteNotification(notificationId)
                if (result.isSuccess) {
                    // بارگذاری مجدد اعلانات
                    loadNotifications()
                } else {
                    _error.value = result.exceptionOrNull()?.message ?: "خطا در حذف اعلان"
                }
            } catch (e: Exception) {
                _error.value = "خطا در اتصال به سرور: ${e.message}"
            }
        }
    }

    /**
     * دریافت تعداد اعلانات خوانده نشده
     */
    fun getUnreadCount(): LiveData<Int> {
        val unreadCount = MutableLiveData<Int>()
        
        viewModelScope.launch {
            try {
                val result = notificationRepository.getUnreadCount()
                if (result.isSuccess) {
                    unreadCount.value = result.getOrNull() ?: 0
                } else {
                    unreadCount.value = 0
                }
            } catch (e: Exception) {
                unreadCount.value = 0
            }
        }
        
        return unreadCount
    }

    /**
     * پاک کردن خطا
     */
    fun clearError() {
        _error.value = null
    }
} 