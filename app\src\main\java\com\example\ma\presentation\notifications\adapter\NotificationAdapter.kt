package com.example.ma.presentation.notifications.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.example.ma.R
import com.example.ma.databinding.ItemNotificationBinding
import com.example.ma.domain.model.Notification
import com.example.ma.utils.DateFormatter

class NotificationAdapter : ListAdapter<Notification, NotificationAdapter.NotificationViewHolder>(NotificationDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NotificationViewHolder {
        val binding = ItemNotificationBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        )
        return NotificationViewHolder(binding)
    }

    override fun onBindViewHolder(holder: NotificationViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class NotificationViewHolder(
        private val binding: ItemNotificationBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(notification: Notification) {
            binding.apply {
                // نام فرستنده
                tvSenderName.text = notification.senderName

                // عنوان و پیام
                tvNotificationTitle.text = notification.title
                tvNotificationMessage.text = notification.message

                // زمان
                tvNotificationTime.text = DateFormatter.formatRelative(notification.createdAt)

                // عکس پروفایل فرستنده
                notification.senderProfileUrl?.let { imageUrl ->
                    Glide.with(binding.root.context)
                        .load(imageUrl)
                        .placeholder(R.drawable.ic_person)
                        .error(R.drawable.ic_person)
                        .into(ivSenderProfile)
                } ?: run {
                    ivSenderProfile.setImageResource(R.drawable.ic_person)
                }

                // نوع اعلان
                val (typeText, typeColor) = getNotificationTypeInfo(notification.type)
                chipNotificationType.text = typeText
                chipNotificationType.setChipBackgroundColorResource(typeColor)

                // تنظیم رنگ بر اساس خوانده شده یا نشده
                if (notification.isRead) {
                    root.alpha = 0.6f
                } else {
                    root.alpha = 1.0f
                }
            }
        }

        private fun getNotificationTypeInfo(type: Notification.NotificationType): Pair<String, Int> {
            return when (type) {
                Notification.NotificationType.TRANSACTION_REQUEST -> "درخواست تایید" to R.color.color_warning
                Notification.NotificationType.TRANSACTION_APPROVED -> "تایید شده" to R.color.color_success
                Notification.NotificationType.TRANSACTION_REJECTED -> "رد شده" to R.color.color_error
                Notification.NotificationType.NEW_TRANSACTION -> "تراکنش جدید" to R.color.color_info
                Notification.NotificationType.PROFILE_UPDATE -> "بروزرسانی پروفایل" to R.color.color_primary
                Notification.NotificationType.SYSTEM_MESSAGE -> "پیام سیستم" to R.color.color_secondary
                Notification.NotificationType.REMINDER -> "یادآوری" to R.color.color_tertiary
            }
        }
    }

    private class NotificationDiffCallback : DiffUtil.ItemCallback<Notification>() {
        override fun areItemsTheSame(oldItem: Notification, newItem: Notification): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Notification, newItem: Notification): Boolean {
            return oldItem == newItem
        }
    }
} 