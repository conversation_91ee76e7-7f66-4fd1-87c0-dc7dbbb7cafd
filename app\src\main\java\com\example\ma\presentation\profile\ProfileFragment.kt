package com.example.ma.presentation.profile

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.bumptech.glide.Glide
import com.example.ma.R
import com.example.ma.domain.model.FinancialSummary
import com.example.ma.domain.model.User
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import com.google.android.material.textview.MaterialTextView
import dagger.hilt.android.AndroidEntryPoint
import de.hdodenhof.circleimageview.CircleImageView

/**
 * Fragment برای نمایش پروفایل کاربر
 */
@AndroidEntryPoint
class ProfileFragment : Fragment() {

    private val viewModel: ProfileViewModel by viewModels()

    // UI Elements
    private lateinit var ivProfileImage: CircleImageView
    private lateinit var tvUserName: MaterialTextView
    private lateinit var tvUserEmail: MaterialTextView
    private lateinit var tvUserPhone: MaterialTextView
    private lateinit var tvUserRole: MaterialTextView
    private lateinit var tvTotalSales: MaterialTextView
    private lateinit var tvTotalExpenses: MaterialTextView
    private lateinit var tvMyShare: MaterialTextView
    private lateinit var cardProfileInfo: MaterialCardView
    private lateinit var cardFinancialSummary: MaterialCardView
    private lateinit var btnEditProfile: MaterialButton
    private lateinit var btnChangePassword: MaterialButton
    private lateinit var btnChangePhoto: MaterialButton

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_profile, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupViews(view)
        observeViewModel()
        loadUserProfile()
    }

    private fun setupViews(view: View) {
        ivProfileImage = view.findViewById(R.id.ivProfileImage)
        tvUserName = view.findViewById(R.id.tvUserName)
        tvUserEmail = view.findViewById(R.id.tvUserEmail)
        tvUserPhone = view.findViewById(R.id.tvUserPhone)
        tvUserRole = view.findViewById(R.id.tvUserRole)
        tvTotalSales = view.findViewById(R.id.tvTotalSales)
        tvTotalExpenses = view.findViewById(R.id.tvTotalExpenses)
        tvMyShare = view.findViewById(R.id.tvMyShare)
        cardProfileInfo = view.findViewById(R.id.cardProfileInfo)
        cardFinancialSummary = view.findViewById(R.id.cardFinancialSummary)
        btnEditProfile = view.findViewById(R.id.btnEditProfile)
        btnChangePassword = view.findViewById(R.id.btnChangePassword)
        btnChangePhoto = view.findViewById(R.id.btnChangePhoto)

        // تنظیم click listeners
        btnEditProfile.setOnClickListener {
            // TODO: Navigate to edit profile
        }

        btnChangePassword.setOnClickListener {
            // TODO: Navigate to change password
        }

        btnChangePhoto.setOnClickListener {
            navigateToProfileImage()
        }

        ivProfileImage.setOnClickListener {
            navigateToProfileImage()
        }
    }

    private fun observeViewModel() {
        viewModel.currentUser.observe(viewLifecycleOwner) { user ->
            user?.let { updateUserDisplay(it) }
        }

        viewModel.financialSummary.observe(viewLifecycleOwner) { summary ->
            summary?.let { updateFinancialDisplay(it) }
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // TODO: Show/hide loading indicator
        }

        viewModel.error.observe(viewLifecycleOwner) { error ->
            error?.let {
                // TODO: Show error message
            }
        }
    }

    private fun loadUserProfile() {
        viewModel.loadUserProfile()
        viewModel.loadFinancialSummary()
    }

    private fun updateUserDisplay(user: User) {
        tvUserName.text = user.displayName
        tvUserEmail.text = user.email ?: "ایمیل ثبت نشده"
        tvUserPhone.text = user.phone ?: "شماره تلفن ثبت نشده"
        tvUserRole.text = "شریک کسب‌وکار"

        // بارگذاری عکس پروفایل
        user.profileImageUrl?.let { imageUrl ->
            Glide.with(this)
                .load(imageUrl)
                .placeholder(R.drawable.ic_person)
                .error(R.drawable.ic_person)
                .into(ivProfileImage)
        } ?: run {
            ivProfileImage.setImageResource(R.drawable.ic_person)
        }
    }

    private fun updateFinancialDisplay(summary: FinancialSummary) {
        tvTotalSales.text = formatCurrency(summary.totalSales)
        tvTotalExpenses.text = formatCurrency(summary.totalExpenses)
        tvMyShare.text = formatCurrency(summary.partner1Share)
    }

    private fun formatCurrency(amount: Double): String {
        return String.format("%,.0f تومان", amount)
    }

    private fun navigateToProfileImage() {
        val intent = Intent(requireContext(), ProfileImageActivity::class.java)
        startActivity(intent)
    }
} 