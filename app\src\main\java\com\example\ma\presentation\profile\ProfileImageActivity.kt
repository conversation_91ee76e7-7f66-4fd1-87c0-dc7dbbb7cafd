package com.example.ma.presentation.profile

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import com.example.ma.databinding.ActivityProfileImageBinding
import com.example.ma.utils.LogManager
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ProfileImageActivity : AppCompatActivity() {

    private lateinit var binding: ActivityProfileImageBinding
    private val viewModel: ProfileImageViewModel by viewModels()

    private val getContent = registerForActivityResult(ActivityResultContracts.GetContent()) { uri: Uri? ->
		uri?.let { onImageSelected(it) }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProfileImageBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupUI()
        observeViewModel()
    }

    private fun setupUI() {
        // دکمه انتخاب از گالری
        binding.btnSelectImage.setOnClickListener {
            getContent.launch("image/*")
        }

        // دکمه دوربین
        binding.btnTakePhoto.setOnClickListener {
            showImageSourceDialog()
        }

        // FAB دوربین (Instagram style)
        binding.fabCamera.setOnClickListener {
            showImageSourceDialog()
        }

        // دکمه ذخیره
        binding.btnSave.setOnClickListener {
            viewModel.saveProfileImage()
        }

        // دکمه لغو
        binding.btnCancel.setOnClickListener {
            finish()
        }

        // غیرفعال کردن دکمه ذخیره در ابتدا
        binding.btnSave.isEnabled = false
    }

    private fun observeViewModel() {
        viewModel.isLoading.observe(this) { isLoading ->
            // TODO: نمایش/مخفی کردن loading
            binding.btnSave.isEnabled = !isLoading
        }

        viewModel.uploadResult.observe(this) { result ->
            result.onSuccess {
                LogManager.info("ProfileImageActivity", "عکس با موفقیت آپلود شد")
                setResult(RESULT_OK)
                finish()
            }.onFailure { error ->
                LogManager.error("ProfileImageActivity", "خطا در آپلود عکس", error)
                showError("خطا در آپلود عکس: ${error.message}")
            }
        }

        viewModel.error.observe(this) { error ->
            error?.let { showError(it) }
        }
    }

    private fun showImageSourceDialog() {
        MaterialAlertDialogBuilder(this)
            .setTitle("انتخاب منبع عکس")
            .setItems(arrayOf("گالری", "دوربین")) { _, which ->
                when (which) {
                    0 -> getContent.launch("image/*")
                    1 -> {
                        // TODO: پیاده‌سازی دوربین
                        showError("دوربین هنوز پیاده‌سازی نشده")
                    }
                }
            }
            .show()
    }

    private fun showError(message: String) {
        MaterialAlertDialogBuilder(this)
            .setTitle("خطا")
            .setMessage(message)
            .setPositiveButton("باشه", null)
            .show()
    }

	private fun onImageSelected(uri: Uri) {
		LogManager.info("ProfileImageActivity", "عکس انتخاب شد: $uri")
		binding.ivProfileImage.setImageURI(uri)
		binding.btnSave.isEnabled = true
		viewModel.uploadProfileImage(uri)
    }
} 