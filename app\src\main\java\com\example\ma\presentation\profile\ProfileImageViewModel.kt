package com.example.ma.presentation.profile

import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.ma.domain.repository.AuthRepository
import com.example.ma.utils.LogManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel برای ProfileImageActivity
 */
@HiltViewModel
class ProfileImageViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _uploadResult = MutableLiveData<Result<String>>()
    val uploadResult: LiveData<Result<String>> = _uploadResult

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private var selectedImageUri: Uri? = null

    /**
     * آپلود عکس پروفایل
     */
    fun uploadProfileImage(uri: Uri) {
        selectedImageUri = uri
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                LogManager.info("ProfileImageViewModel", "شروع آپلود عکس پروفایل")

                // TODO: پیاده‌سازی آپلود به Supabase Storage
                // فعلاً فقط URI را ذخیره می‌کنیم
                val imageUrl = uri.toString()
                
                // آپدیت پروفایل کاربر
                val currentUser = authRepository.getCurrentUser().getOrNull()
                if (currentUser != null) {
                    val updatedUser = currentUser.copy(profileImageUrl = imageUrl)
                    val result = authRepository.updateProfile(updatedUser)
                    
                    if (result.isSuccess) {
                        _uploadResult.value = Result.success(imageUrl)
                        LogManager.info("ProfileImageViewModel", "عکس پروفایل با موفقیت آپدیت شد")
                    } else {
                        val error = result.exceptionOrNull()?.message ?: "خطا در آپدیت پروفایل"
                        _uploadResult.value = Result.failure(Exception(error))
                        LogManager.error("ProfileImageViewModel", "خطا در آپدیت پروفایل: $error")
                    }
                } else {
                    _uploadResult.value = Result.failure(Exception("کاربر یافت نشد"))
                }

            } catch (e: Exception) {
                _uploadResult.value = Result.failure(e)
                LogManager.error("ProfileImageViewModel", "خطا در آپلود عکس", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * ذخیره عکس پروفایل
     */
    fun saveProfileImage() {
        selectedImageUri?.let { uri ->
            uploadProfileImage(uri)
        } ?: run {
            _error.value = "لطفاً ابتدا عکسی انتخاب کنید"
        }
    }

    /**
     * پاک کردن خطا
     */
    fun clearError() {
        _error.value = null
    }
}
