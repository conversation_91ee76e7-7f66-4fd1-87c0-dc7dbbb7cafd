package com.example.ma.presentation.profile

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.ma.domain.model.FinancialSummary
import com.example.ma.domain.model.User
import com.example.ma.domain.repository.AuthRepository
import com.example.ma.domain.repository.TransactionRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel برای ProfileFragment
 */
@HiltViewModel
class ProfileViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val transactionRepository: TransactionRepository
) : ViewModel() {

    private val _currentUser = MutableLiveData<User>()
    val currentUser: LiveData<User> = _currentUser

    private val _financialSummary = MutableLiveData<FinancialSummary>()
    val financialSummary: LiveData<FinancialSummary> = _financialSummary

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    /**
     * بارگذاری اطلاعات کاربر
     */
    fun loadUserProfile() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                val result = authRepository.getCurrentUser()
                if (result.isSuccess) {
                    _currentUser.value = result.getOrNull()
                } else {
                    _error.value = result.exceptionOrNull()?.message ?: "خطا در بارگذاری اطلاعات کاربر"
                }
            } catch (e: Exception) {
                _error.value = "خطا در اتصال به سرور: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * بارگذاری خلاصه مالی
     */
    fun loadFinancialSummary() {
        viewModelScope.launch {
            try {
                // TODO: Implement getFinancialSummary method in TransactionRepository
                // For now, create a default summary
                val defaultSummary = FinancialSummary(
                    totalSales = 0.0,
                    totalExpenses = 0.0,
                    netProfit = 0.0,
                    profitMargin = 0.0,
                    totalTransactions = 0,
                    averageTransactionAmount = 0.0,
                    cashBalance = 0.0,
                    cardBalance = 0.0,
                    totalWithdrawals = 0.0,
                    partner1Share = 0.0,
                    partner2Share = 0.0
                )
                _financialSummary.value = defaultSummary
            } catch (e: Exception) {
                // خطا در خلاصه مالی را نادیده می‌گیریم
            }
        }
    }

    /**
     * آپدیت پروفایل کاربر
     */
    fun updateProfile(user: User) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                val result = authRepository.updateProfile(user)
                if (result.isSuccess) {
                    _currentUser.value = result.getOrNull()
                } else {
                    _error.value = result.exceptionOrNull()?.message ?: "خطا در آپدیت پروفایل"
                }
            } catch (e: Exception) {
                _error.value = "خطا در اتصال به سرور: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * تغییر رمز عبور
     */
    fun changePassword(oldPassword: String, newPassword: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                val result = authRepository.changePassword(oldPassword, newPassword)
                if (result.isSuccess) {
                    // رمز با موفقیت تغییر یافت
                } else {
                    _error.value = result.exceptionOrNull()?.message ?: "خطا در تغییر رمز عبور"
                }
            } catch (e: Exception) {
                _error.value = "خطا در اتصال به سرور: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * پاک کردن خطا
     */
    fun clearError() {
        _error.value = null
    }
} 