package com.example.ma.presentation.reports

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.example.ma.R
import com.example.ma.domain.model.BusinessFinancialSummary
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import com.google.android.material.textview.MaterialTextView
import dagger.hilt.android.AndroidEntryPoint

/**
 * Fragment برای نمایش گزارشات مالی
 */
@AndroidEntryPoint
class ReportsFragment : Fragment() {

	private val viewModel: ReportsViewModel by viewModels()

	// UI Elements
	private lateinit var tvTotalSales: MaterialTextView
	private lateinit var tvTotalExpenses: MaterialTextView
	private lateinit var tvNetProfit: MaterialTextView
	private lateinit var tvMyShare: MaterialTextView
	private lateinit var tvPartnerShare: MaterialTextView
	private lateinit var btnExportReport: MaterialButton
	private lateinit var btnShareReport: MaterialButton

	override fun onCreateView(
		inflater: LayoutInflater,
		container: ViewGroup?,
		savedInstanceState: Bundle?
	): View? {
		return inflater.inflate(R.layout.fragment_reports, container, false)
	}

	override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
		super.onViewCreated(view, savedInstanceState)
		
		setupViews(view)
		observeViewModel()
		loadReports()
	}

	private fun setupViews(view: View) {
		tvTotalSales = view.findViewById(R.id.tvTotalSales)
		tvTotalExpenses = view.findViewById(R.id.tvTotalExpenses)
		tvNetProfit = view.findViewById(R.id.tvNetProfit)
		tvMyShare = view.findViewById(R.id.tvMyShare)
		tvPartnerShare = view.findViewById(R.id.tvPartnerShare)
		btnExportReport = view.findViewById(R.id.btnExportReport)
		btnShareReport = view.findViewById(R.id.btnShareReport)

		btnExportReport.setOnClickListener {
			exportReport()
		}

		btnShareReport.setOnClickListener {
			shareReport()
		}
	}

	private fun observeViewModel() {
		viewModel.financialSummary.observe(viewLifecycleOwner) { summary ->
			summary?.let { updateFinancialDisplay(it) }
		}

		viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
			// TODO: Show/hide loading indicator
		}

		viewModel.error.observe(viewLifecycleOwner) { error ->
			error?.let {
				// TODO: Show error message
			}
		}
	}

	private fun loadReports() {
		viewModel.loadFinancialSummary()
	}

	private fun updateFinancialDisplay(summary: BusinessFinancialSummary) {
		tvTotalSales.text = formatCurrency(summary.totalSales)
		tvTotalExpenses.text = formatCurrency(summary.totalExpenses)
		tvNetProfit.text = formatCurrency(summary.netProfit)
		tvMyShare.text = formatCurrency(summary.partner1Balance.profitShare)
		tvPartnerShare.text = formatCurrency(summary.partner2Balance.profitShare)
	}

	private fun formatCurrency(amount: Double): String {
		return String.format("% ,.0f تومان", amount)
	}

	private fun exportReport() {
		viewModel.exportReport()
	}

	private fun shareReport() {
		viewModel.shareReport()
	}
} 