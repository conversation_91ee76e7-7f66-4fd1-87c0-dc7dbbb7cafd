package com.example.ma.presentation.settings

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import com.example.ma.R
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import com.google.android.material.switchmaterial.SwitchMaterial
import com.google.android.material.textview.MaterialTextView
import dagger.hilt.android.AndroidEntryPoint

/**
 * Fragment برای تنظیمات برنامه
 */
@AndroidEntryPoint
class SettingsFragment : Fragment() {

    private val viewModel: SettingsViewModel by viewModels()

    // UI Elements - Appearance Section
    private lateinit var cardAppearance: MaterialCardView
    private lateinit var tvCurrentTheme: MaterialTextView
    private lateinit var btnChangeTheme: MaterialButton

    // UI Elements - Notifications Section
    private lateinit var cardNotifications: MaterialCardView
    private lateinit var switchPushNotifications: SwitchMaterial
    private lateinit var switchSoundVibration: SwitchMaterial
    private lateinit var switchTransactionAlerts: SwitchMaterial

    // UI Elements - Security Section
    private lateinit var cardSecurity: MaterialCardView
    private lateinit var btnChangePassword: MaterialButton
    private lateinit var switchBiometric: SwitchMaterial
    private lateinit var switchAutoLock: SwitchMaterial

    // UI Elements - Data & Sync Section
    private lateinit var cardDataSync: MaterialCardView
    private lateinit var switchAutoSync: SwitchMaterial
    private lateinit var btnBackupData: MaterialButton
    private lateinit var btnClearCache: MaterialButton

    // UI Elements - About Section
    private lateinit var cardAbout: MaterialCardView
    private lateinit var tvAppVersion: MaterialTextView
    private lateinit var btnContactSupport: MaterialButton
    private lateinit var btnLogout: MaterialButton

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_settings, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupViews(view)
        observeViewModel()
        loadSettings()
    }

    private fun setupViews(view: View) {
        // Appearance Section
        cardAppearance = view.findViewById(R.id.cardAppearance)
        tvCurrentTheme = view.findViewById(R.id.tvCurrentTheme)
        btnChangeTheme = view.findViewById(R.id.btnChangeTheme)

        // Notifications Section
        cardNotifications = view.findViewById(R.id.cardNotifications)
        switchPushNotifications = view.findViewById(R.id.switchPushNotifications)
        switchSoundVibration = view.findViewById(R.id.switchSoundVibration)
        switchTransactionAlerts = view.findViewById(R.id.switchTransactionAlerts)

        // Security Section
        cardSecurity = view.findViewById(R.id.cardSecurity)
        btnChangePassword = view.findViewById(R.id.btnChangePassword)
        switchBiometric = view.findViewById(R.id.switchBiometric)
        switchAutoLock = view.findViewById(R.id.switchAutoLock)

        // Data & Sync Section
        cardDataSync = view.findViewById(R.id.cardDataSync)
        switchAutoSync = view.findViewById(R.id.switchAutoSync)
        btnBackupData = view.findViewById(R.id.btnBackupData)
        btnClearCache = view.findViewById(R.id.btnClearCache)

        // About Section
        cardAbout = view.findViewById(R.id.cardAbout)
        tvAppVersion = view.findViewById(R.id.tvAppVersion)
        btnContactSupport = view.findViewById(R.id.btnContactSupport)
        btnLogout = view.findViewById(R.id.btnLogout)

        setupClickListeners()
    }

    private fun setupClickListeners() {
        // Theme Selection
        btnChangeTheme.setOnClickListener {
            showThemeSelectionDialog()
        }

        // Notification Settings
        switchPushNotifications.setOnCheckedChangeListener { _, isChecked ->
            viewModel.updatePushNotifications(isChecked)
        }

        switchSoundVibration.setOnCheckedChangeListener { _, isChecked ->
            viewModel.updateSoundVibration(isChecked)
        }

        switchTransactionAlerts.setOnCheckedChangeListener { _, isChecked ->
            viewModel.updateTransactionAlerts(isChecked)
        }

        // Security Settings
        btnChangePassword.setOnClickListener {
            // TODO: Navigate to change password screen
        }

        switchBiometric.setOnCheckedChangeListener { _, isChecked ->
            viewModel.updateBiometricAuth(isChecked)
        }

        switchAutoLock.setOnCheckedChangeListener { _, isChecked ->
            viewModel.updateAutoLock(isChecked)
        }

        // Data & Sync Settings
        switchAutoSync.setOnCheckedChangeListener { _, isChecked ->
            viewModel.updateAutoSync(isChecked)
        }

        btnBackupData.setOnClickListener {
            viewModel.backupData()
        }

        btnClearCache.setOnClickListener {
            showClearCacheConfirmation()
        }

        // About & Support
        btnContactSupport.setOnClickListener {
            // TODO: Open contact support
        }

        btnLogout.setOnClickListener {
            showLogoutConfirmation()
        }
    }

    private fun observeViewModel() {
        // TODO: Implement view model observation
    }

    private fun loadSettings() {
        // TODO: Load current settings
    }

    private fun showThemeSelectionDialog() {
        // TODO: Implement theme selection dialog
    }

    private fun showClearCacheConfirmation() {
        // TODO: Implement clear cache confirmation
    }

    private fun showLogoutConfirmation() {
        // TODO: Implement logout confirmation
    }
}
