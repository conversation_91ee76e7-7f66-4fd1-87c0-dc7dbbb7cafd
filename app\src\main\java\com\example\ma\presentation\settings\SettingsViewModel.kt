package com.example.ma.presentation.settings

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.ma.domain.repository.AuthRepository
import com.example.ma.utils.LogManager
import com.example.ma.utils.PreferencesManager
import com.example.ma.utils.ThemeManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel برای SettingsFragment
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val preferencesManager: PreferencesManager
) : ViewModel() {

    private val _settings = MutableLiveData<SettingsData>()
    val settings: LiveData<SettingsData> = _settings

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _logoutResult = MutableLiveData<Boolean>()
    val logoutResult: LiveData<Boolean> = _logoutResult

    /**
     * بارگذاری تنظیمات
     */
    fun loadSettings() {
        viewModelScope.launch {
            try {
                val currentSettings = SettingsData(
                    themeMode = ThemeManager.getCurrentTheme(),
                    pushNotificationsEnabled = preferencesManager.getPushNotificationsEnabled(),
                    soundVibrationEnabled = preferencesManager.getSoundVibrationEnabled(),
                    transactionAlertsEnabled = preferencesManager.getTransactionAlertsEnabled(),
                    biometricAuthEnabled = preferencesManager.getBiometricAuthEnabled(),
                    autoLockEnabled = preferencesManager.getAutoLockEnabled(),
                    autoSyncEnabled = preferencesManager.getAutoSyncEnabled()
                )
                
                _settings.value = currentSettings
                LogManager.debug("SettingsViewModel", "تنظیمات بارگذاری شد")
                
            } catch (e: Exception) {
                LogManager.error("SettingsViewModel", "خطا در بارگذاری تنظیمات", e)
                _error.value = "خطا در بارگذاری تنظیمات: ${e.message}"
            }
        }
    }

    /**
     * تغییر تم
     */
    fun updateTheme(themeMode: ThemeManager.ThemeMode) {
        viewModelScope.launch {
            try {
                ThemeManager.setTheme(themeMode)
                
                val currentSettings = _settings.value?.copy(themeMode = themeMode)
                _settings.value = currentSettings
                
                LogManager.info("SettingsViewModel", "تم به ${themeMode.name} تغییر کرد")
                
            } catch (e: Exception) {
                LogManager.error("SettingsViewModel", "خطا در تغییر تم", e)
                _error.value = "خطا در تغییر تم: ${e.message}"
            }
        }
    }

    /**
     * تغییر وضعیت اعلانات push
     */
    fun updatePushNotifications(enabled: Boolean) {
        viewModelScope.launch {
            try {
                preferencesManager.setPushNotificationsEnabled(enabled)
                
                val currentSettings = _settings.value?.copy(pushNotificationsEnabled = enabled)
                _settings.value = currentSettings
                
                LogManager.info("SettingsViewModel", "اعلانات push: $enabled")
                
            } catch (e: Exception) {
                LogManager.error("SettingsViewModel", "خطا در تغییر تنظیمات اعلانات", e)
                _error.value = "خطا در تغییر تنظیمات اعلانات: ${e.message}"
            }
        }
    }

    /**
     * تغییر وضعیت صدا و لرزش
     */
    fun updateSoundVibration(enabled: Boolean) {
        viewModelScope.launch {
            try {
                preferencesManager.setSoundVibrationEnabled(enabled)
                
                val currentSettings = _settings.value?.copy(soundVibrationEnabled = enabled)
                _settings.value = currentSettings
                
                LogManager.info("SettingsViewModel", "صدا و لرزش: $enabled")
                
            } catch (e: Exception) {
                LogManager.error("SettingsViewModel", "خطا در تغییر تنظیمات صدا", e)
                _error.value = "خطا در تغییر تنظیمات صدا: ${e.message}"
            }
        }
    }

    /**
     * تغییر وضعیت هشدارهای تراکنش
     */
    fun updateTransactionAlerts(enabled: Boolean) {
        viewModelScope.launch {
            try {
                preferencesManager.setTransactionAlertsEnabled(enabled)
                
                val currentSettings = _settings.value?.copy(transactionAlertsEnabled = enabled)
                _settings.value = currentSettings
                
                LogManager.info("SettingsViewModel", "هشدارهای تراکنش: $enabled")
                
            } catch (e: Exception) {
                LogManager.error("SettingsViewModel", "خطا در تغییر تنظیمات هشدار", e)
                _error.value = "خطا در تغییر تنظیمات هشدار: ${e.message}"
            }
        }
    }

    /**
     * تغییر وضعیت احراز هویت بیومتریک
     */
    fun updateBiometricAuth(enabled: Boolean) {
        viewModelScope.launch {
            try {
                preferencesManager.setBiometricAuthEnabled(enabled)
                
                val currentSettings = _settings.value?.copy(biometricAuthEnabled = enabled)
                _settings.value = currentSettings
                
                LogManager.info("SettingsViewModel", "احراز هویت بیومتریک: $enabled")
                
            } catch (e: Exception) {
                LogManager.error("SettingsViewModel", "خطا در تغییر تنظیمات بیومتریک", e)
                _error.value = "خطا در تغییر تنظیمات بیومتریک: ${e.message}"
            }
        }
    }

    /**
     * تغییر وضعیت قفل خودکار
     */
    fun updateAutoLock(enabled: Boolean) {
        viewModelScope.launch {
            try {
                preferencesManager.setAutoLockEnabled(enabled)
                
                val currentSettings = _settings.value?.copy(autoLockEnabled = enabled)
                _settings.value = currentSettings
                
                LogManager.info("SettingsViewModel", "قفل خودکار: $enabled")
                
            } catch (e: Exception) {
                LogManager.error("SettingsViewModel", "خطا در تغییر تنظیمات قفل", e)
                _error.value = "خطا در تغییر تنظیمات قفل: ${e.message}"
            }
        }
    }

    /**
     * تغییر وضعیت همگام‌سازی خودکار
     */
    fun updateAutoSync(enabled: Boolean) {
        viewModelScope.launch {
            try {
                preferencesManager.setAutoSyncEnabled(enabled)
                
                val currentSettings = _settings.value?.copy(autoSyncEnabled = enabled)
                _settings.value = currentSettings
                
                LogManager.info("SettingsViewModel", "همگام‌سازی خودکار: $enabled")
                
            } catch (e: Exception) {
                LogManager.error("SettingsViewModel", "خطا در تغییر تنظیمات همگام‌سازی", e)
                _error.value = "خطا در تغییر تنظیمات همگام‌سازی: ${e.message}"
            }
        }
    }

    /**
     * پشتیبان‌گیری از داده‌ها
     */
    fun backupData() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                // TODO: Implement backup functionality
                LogManager.info("SettingsViewModel", "شروع پشتیبان‌گیری از داده‌ها")
                
                // Simulate backup process
                kotlinx.coroutines.delay(2000)
                
                LogManager.info("SettingsViewModel", "پشتیبان‌گیری با موفقیت انجام شد")
                
            } catch (e: Exception) {
                LogManager.error("SettingsViewModel", "خطا در پشتیبان‌گیری", e)
                _error.value = "خطا در پشتیبان‌گیری: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * پاک کردن حافظه موقت
     */
    fun clearCache() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                // TODO: Implement cache clearing functionality
                LogManager.info("SettingsViewModel", "شروع پاک کردن حافظه موقت")
                
                // Simulate cache clearing
                kotlinx.coroutines.delay(1000)
                
                LogManager.info("SettingsViewModel", "حافظه موقت پاک شد")
                
            } catch (e: Exception) {
                LogManager.error("SettingsViewModel", "خطا در پاک کردن حافظه موقت", e)
                _error.value = "خطا در پاک کردن حافظه موقت: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * خروج از حساب کاربری
     */
    fun logout() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                LogManager.info("SettingsViewModel", "شروع خروج از حساب")
                
                val result = authRepository.logout()
                if (result.isSuccess) {
                    LogManager.info("SettingsViewModel", "خروج از حساب با موفقیت انجام شد")
                    _logoutResult.value = true
                } else {
                    val error = result.exceptionOrNull()?.message ?: "خطا در خروج از حساب"
                    LogManager.error("SettingsViewModel", "خطا در خروج از حساب: $error")
                    _error.value = error
                }
                
            } catch (e: Exception) {
                LogManager.error("SettingsViewModel", "خطای غیرمنتظره در خروج از حساب", e)
                _error.value = "خطا در خروج از حساب: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * پاک کردن خطا
     */
    fun clearError() {
        _error.value = null
    }
}

/**
 * مدل داده‌های تنظیمات
 */
data class SettingsData(
    val themeMode: ThemeManager.ThemeMode = ThemeManager.ThemeMode.SYSTEM_DEFAULT,
    val pushNotificationsEnabled: Boolean = true,
    val soundVibrationEnabled: Boolean = true,
    val transactionAlertsEnabled: Boolean = true,
    val biometricAuthEnabled: Boolean = false,
    val autoLockEnabled: Boolean = false,
    val autoSyncEnabled: Boolean = true
)
