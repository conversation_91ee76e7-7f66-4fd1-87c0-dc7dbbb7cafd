package com.example.ma.presentation.test

import android.os.Bundle
import android.widget.Button
import android.widget.ScrollView
import android.widget.TextView
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.example.ma.R
import com.example.ma.utils.SupabaseConnectionTest
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Activity برای تست اتصال به Supabase
 * فقط برای مرحله توسعه و دیباگ
 */
@AndroidEntryPoint
class ConnectionTestActivity : AppCompatActivity() {

    @Inject
    lateinit var connectionTest: SupabaseConnectionTest

    private lateinit var btnTestConnection: Button
    private lateinit var btnGenerateReport: Button
    private lateinit var tvResults: TextView
    private lateinit var scrollView: ScrollView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // ساخت layout به صورت programmatic
        setupLayout()
        setupClickListeners()
    }

    private fun setupLayout() {
        // ساخت layout ساده
        val layout = android.widget.LinearLayout(this).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(32, 32, 32, 32)
        }

        // دکمه تست اتصال
        btnTestConnection = Button(this).apply {
            text = "تست اتصال Supabase"
            textSize = 16f
        }

        // دکمه گزارش کامل
        btnGenerateReport = Button(this).apply {
            text = "گزارش کامل"
            textSize = 16f
        }

        // TextView برای نمایش نتایج
        tvResults = TextView(this).apply {
            text = "برای شروع تست، روی دکمه کلیک کنید"
            textSize = 14f
            setPadding(16, 16, 16, 16)
            setBackgroundColor(0xFFF5F5F5.toInt())
        }

        // ScrollView برای نتایج
        scrollView = ScrollView(this).apply {
            addView(tvResults)
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                0,
                1f
            )
        }

        // اضافه کردن views به layout
        layout.addView(btnTestConnection)
        layout.addView(btnGenerateReport)
        layout.addView(scrollView)

        setContentView(layout)
    }

    private fun setupClickListeners() {
        btnTestConnection.setOnClickListener {
            testBasicConnection()
        }

        btnGenerateReport.setOnClickListener {
            generateFullReport()
        }
    }

    private fun testBasicConnection() {
        btnTestConnection.isEnabled = false
        tvResults.text = "در حال تست اتصال..."

        lifecycleScope.launch {
            try {
                val result = connectionTest.testFullConnection()
                
                val resultText = buildString {
                    appendLine("=== نتیجه تست اتصال ===")
                    appendLine("وضعیت: ${if (result.success) "✅ موفق" else "❌ ناموفق"}")
                    appendLine("پیام: ${result.message}")
                    appendLine()
                    appendLine("جزئیات:")
                    result.results.forEach { item ->
                        appendLine("- ${item.message}")
                        item.details.entries.forEach { entry ->
                            appendLine("   ${entry.key}: ${entry.value}")
                        }
                    }
                    appendLine()
                    appendLine("زمان تست: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
                }

                tvResults.text = resultText

            } catch (e: Exception) {
                tvResults.text = "خطا در تست: ${e.message}"
            } finally {
                btnTestConnection.isEnabled = true
            }
        }
    }

    private fun generateFullReport() {
        btnGenerateReport.isEnabled = false
        tvResults.text = "در حال تولید گزارش کامل..."

        lifecycleScope.launch {
            try {
                val full = connectionTest.testFullConnection()
                val summary = buildString {
                    appendLine("نتیجه کلی: ${full.message}")
                    full.results.forEach { item ->
                        appendLine("- ${item.message}")
                    }
                }
                tvResults.text = summary

            } catch (e: Exception) {
                tvResults.text = "خطا در تولید گزارش: ${e.message}"
            } finally {
                btnGenerateReport.isEnabled = true
            }
        }
    }
}
