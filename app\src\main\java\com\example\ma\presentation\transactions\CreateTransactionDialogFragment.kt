package com.example.ma.presentation.transactions

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.RadioGroup
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.activityViewModels
import com.example.ma.R
import com.example.ma.domain.model.Transaction
import com.example.ma.domain.model.TransactionType
import com.example.ma.domain.model.TransactionStatus
import com.example.ma.utils.PreferencesManager
import dagger.hilt.android.AndroidEntryPoint
import java.util.UUID
import javax.inject.Inject

@AndroidEntryPoint
class CreateTransactionDialogFragment : DialogFragment() {

    // Use activityViewModels to get a ViewModel scoped to the host activity/fragment
    private val viewModel: TransactionsViewModel by activityViewModels()

    @Inject lateinit var preferencesManager: PreferencesManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_create_transaction, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        val rgType = view.findViewById<RadioGroup>(R.id.rgTransactionType)
        val etAmount = view.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.etAmount)
        val etDescription = view.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.etDescription)
        val btnSave = view.findViewById<Button>(R.id.btnSave)
        val btnCancel = view.findViewById<Button>(R.id.btnCancel)

        btnCancel.setOnClickListener {
            dismiss()
        }

        btnSave.setOnClickListener {
            val amountText = etAmount.text.toString()
            val description = etDescription.text.toString()
            val selectedTypeId = rgType.checkedRadioButtonId

            if (amountText.isBlank() || description.isBlank() || selectedTypeId == -1) {
                Toast.makeText(requireContext(), "لطفاً تمام فیلدها را پر کنید", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            val amount = amountText.toLongOrNull()
            if (amount == null || amount <= 0) {
                Toast.makeText(requireContext(), "مبلغ نامعتبر است", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            val type = when (selectedTypeId) {
                R.id.rbSale -> TransactionType.SALE
                R.id.rbExpense -> TransactionType.EXPENSE
                R.id.rbCapital -> TransactionType.CAPITAL
                R.id.rbWithdrawal -> TransactionType.WITHDRAWAL
                else -> {
                    Toast.makeText(requireContext(), "نوع تراکنش انتخاب نشده است", Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }
            }

            val currentUserId = preferencesManager.getUserId() ?: run {
                Toast.makeText(requireContext(), "کاربر وارد نشده است", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            // Partner id can be derived based on the two known partners
            val partnerUserId = if (currentUserId == "user1") "user2" else "user1"
            val currentUserDisplayName = preferencesManager.getUsername() ?: "کاربر"

            val newTransaction = Transaction(
                id = UUID.randomUUID().toString(),
                userId = currentUserId,
                type = type,
                amount = amount.toDouble(),
                description = description,
                status = TransactionStatus.PENDING
            )

            viewModel.createTransaction(newTransaction, currentUserId, partnerUserId, currentUserDisplayName, null)
            dismiss()
        }
    }
    
    override fun onStart() {
        super.onStart()
        // Set dialog width to match parent
        dialog?.window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
    }
}
