package com.example.ma.presentation.transactions

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.ma.R
import com.example.ma.domain.model.Transaction
import com.example.ma.presentation.adapters.TransactionAdapter
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.google.android.material.textview.MaterialTextView
import dagger.hilt.android.AndroidEntryPoint
import java.util.UUID

/**
 * Fragment برای نمایش لیست تراکنش‌ها
 */
@AndroidEntryPoint
class TransactionsFragment : Fragment() {

    private val viewModel: TransactionsViewModel by viewModels()
    private lateinit var transactionAdapter: TransactionAdapter

    // UI Elements
    private lateinit var recyclerView: RecyclerView
    private lateinit var tvEmptyState: MaterialTextView
    private lateinit var fabAddTransaction: FloatingActionButton

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_transactions, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupViews(view)
        setupRecyclerView()
        observeViewModel()
        loadTransactions()
    }

    private fun setupViews(view: View) {
        recyclerView = view.findViewById(R.id.recyclerViewTransactions)
        tvEmptyState = view.findViewById(R.id.tvEmptyState)
        fabAddTransaction = view.findViewById(R.id.fabAddTransaction)

        fabAddTransaction.setOnClickListener {
            showCreateTransactionDialog()
        }
    }

    private fun setupRecyclerView() {
        transactionAdapter = TransactionAdapter(
            onTransactionClick = { transaction ->
                // TODO: Handle transaction click
            },
            onTransactionLongClick = { transaction ->
                // TODO: Handle transaction long click
                true
            }
        )

        recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = transactionAdapter
        }
    }

    private fun observeViewModel() {
        viewModel.transactions.observe(viewLifecycleOwner) { transactions ->
            updateTransactionsList(transactions)
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            // TODO: Show/hide loading indicator
        }

        viewModel.error.observe(viewLifecycleOwner) { error ->
            error?.let {
                Toast.makeText(requireContext(), "خطا: $it", Toast.LENGTH_LONG).show()
                // TODO: Implement clearError method in ViewModel
            }
        }

        // TODO: Implement createTransactionResult observation
    }

    private fun loadTransactions() {
        viewModel.loadTransactions()
    }

    private fun updateTransactionsList(transactions: List<Transaction>) {
        if (transactions.isEmpty()) {
            recyclerView.visibility = View.GONE
            tvEmptyState.visibility = View.VISIBLE
        } else {
            recyclerView.visibility = View.VISIBLE
            tvEmptyState.visibility = View.GONE
            transactionAdapter.submitList(transactions)
        }
    }

    private fun showCreateTransactionDialog() {
        val dialogFragment = CreateTransactionDialogFragment()
        dialogFragment.show(parentFragmentManager, "CreateTransactionDialog")
    }
}
