package com.example.ma.presentation.transactions

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.ma.domain.model.Transaction
import com.example.ma.domain.repository.TransactionRepository
import com.example.ma.domain.usecase.CreateTransactionUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for TransactionsFragment
 */
@HiltViewModel
class TransactionsViewModel @Inject constructor(
    private val transactionRepository: TransactionRepository,
    private val createTransactionUseCase: CreateTransactionUseCase
) : ViewModel() {

    private val _createTransactionResult = MutableLiveData<Result<Unit>>()
    val createTransactionResult: LiveData<Result<Unit>> = _createTransactionResult

    private val _transactions = MutableLiveData<List<Transaction>>()
    val transactions: LiveData<List<Transaction>> = _transactions

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    /**
     * بارگذاری تراکنش‌ها
     */
    fun loadTransactions() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                val result = transactionRepository.getAllTransactions()
                if (result.isSuccess) {
                    _transactions.value = result.getOrNull() ?: emptyList()
                } else {
                    _error.value = result.exceptionOrNull()?.message ?: "خطا در بارگذاری تراکنش‌ها"
                }
            } catch (e: Exception) {
                _error.value = "خطا در اتصال به سرور: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * فیلتر تراکنش‌ها بر اساس نوع
     */
    fun filterTransactionsByType(type: com.example.ma.domain.model.TransactionType) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                val result = transactionRepository.getTransactionsByType(type)
                if (result.isSuccess) {
                    _transactions.value = result.getOrNull() ?: emptyList()
                } else {
                    _error.value = result.exceptionOrNull()?.message ?: "خطا در فیلتر تراکنش‌ها"
                }
            } catch (e: Exception) {
                _error.value = "خطا در اتصال به سرور: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * فیلتر تراکنش‌ها بر اساس وضعیت
     */
    fun filterTransactionsByStatus(status: com.example.ma.domain.model.TransactionStatus) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null

                val result = transactionRepository.getTransactionsByStatus(status)
                if (result.isSuccess) {
                    _transactions.value = result.getOrNull() ?: emptyList()
                } else {
                    _error.value = result.exceptionOrNull()?.message ?: "خطا در فیلتر تراکنش‌ها"
                }
            } catch (e: Exception) {
                _error.value = "خطا در اتصال به سرور: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * آپدیت وضعیت تراکنش
     */
    fun updateTransactionStatus(transactionId: String, status: com.example.ma.domain.model.TransactionStatus) {
        viewModelScope.launch {
            try {
                val result = transactionRepository.updateTransactionStatus(transactionId, status)
                if (result.isSuccess) {
                    // بارگذاری مجدد تراکنش‌ها
                    loadTransactions()
                } else {
                    _error.value = result.exceptionOrNull()?.message ?: "خطا در آپدیت وضعیت"
                }
            } catch (e: Exception) {
                _error.value = "خطا در اتصال به سرور: ${e.message}"
            }
        }
    }

    /**
     * ایجاد یک تراکنش جدید
     */
    fun createTransaction(transaction: Transaction, currentUserId: String, partnerUserId: String, currentUserDisplayName: String, currentUserAvatarUrl: String?) {
        viewModelScope.launch {
            _isLoading.value = true
            val result = createTransactionUseCase(transaction, currentUserId, partnerUserId, currentUserDisplayName, currentUserAvatarUrl)
            _createTransactionResult.value = result
            if (result.isSuccess) {
                // Refresh the list after creation
                loadTransactions()
            } else {
                _error.value = result.exceptionOrNull()?.message ?: "خطا در ایجاد تراکنش"
            }
            _isLoading.value = false
        }
    }
}
