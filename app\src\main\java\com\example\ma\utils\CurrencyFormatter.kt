package com.example.ma.utils

import java.text.NumberFormat
import java.util.Locale

/**
 * فرمت کردن مبالغ مالی
 */
object CurrencyFormatter {
    
    private val numberFormat = NumberFormat.getNumberInstance(Locale("fa", "IR"))
    
    fun format(amount: Double): String {
        return "${numberFormat.format(amount)} تومان"
    }
    
    fun formatCompact(amount: Double): String {
        return when {
            amount >= 1_000_000_000 -> "${String.format("%.1f", amount / 1_000_000_000)} میلیارد"
            amount >= 1_000_000 -> "${String.format("%.1f", amount / 1_000_000)} میلیون"
            amount >= 1_000 -> "${String.format("%.1f", amount / 1_000)} هزار"
            else -> "${numberFormat.format(amount)}"
        }
    }
    
    fun formatWithoutCurrency(amount: Double): String {
        return numberFormat.format(amount)
    }
} 