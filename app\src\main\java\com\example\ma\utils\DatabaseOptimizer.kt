package com.example.ma.utils

import com.example.ma.data.local.AppDatabase
import com.example.ma.utils.LogManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * بهینه‌ساز دیتابیس برای بهبود عملکرد و نگهداری
 */
class DatabaseOptimizer(
    private val database: AppDatabase
) {

    suspend fun optimizeDatabase(): OptimizationResult = withContext(Dispatchers.IO) {
        try {
            LogManager.info("DatabaseOptimizer", "شروع بهینه‌سازی دیتابیس...")
            val db = database.openHelper.writableDatabase
            db.execSQL("VACUUM")
            db.execSQL("REINDEX")
            db.execSQL("PRAGMA journal_mode=WAL")
            db.execSQL("PRAGMA cache_size=10000")
            db.execSQL("PRAGMA synchronous=NORMAL")
            db.execSQL("PRAGMA temp_store=MEMORY")
            OptimizationResult(true, "بهینه‌سازی موفقیت‌آمیز بود", System.currentTimeMillis())
        } catch (e: Exception) {
            LogManager.error("DatabaseOptimizer", "خطا در بهینه‌سازی دیتابیس", e)
            OptimizationResult(false, "خطا در بهینه‌سازی: ${e.message}", System.currentTimeMillis())
        }
    }

    suspend fun getDatabaseStats(): Map<String, Any> = withContext(Dispatchers.IO) {
        try {
            val db = database.openHelper.readableDatabase
            val stats = mutableMapOf<String, Any>()

            listOf("users", "transactions", "notifications").forEach { tableName ->
                try {
                    val count = db.compileStatement("SELECT COUNT(*) FROM $tableName").simpleQueryForLong()
                    stats["${tableName}_count"] = count.toInt()
                } catch (e: Exception) {
                    stats["${tableName}_count"] = "error: ${e.message}"
                }
            }

            try {
                val tcount = db.compileStatement("SELECT COUNT(*) FROM sqlite_master WHERE type='table'").simpleQueryForLong()
                stats["table_count"] = tcount.toInt()
            } catch (e: Exception) {
                LogManager.warning("DatabaseOptimizer", "table_count: ${e.message}")
            }

            try {
                val path = db.path
                val fileSize = java.io.File(path).length()
                stats["database_size_bytes"] = fileSize
                stats["database_size_mb"] = fileSize / (1024 * 1024)
            } catch (e: Exception) {
                LogManager.warning("DatabaseOptimizer", "database_size: ${e.message}")
            }
            stats
        } catch (e: Exception) {
            LogManager.error("DatabaseOptimizer", "stats error", e)
            mapOf("error" to e.message.toString())
        }
    }

    suspend fun cleanupOldData(daysToKeep: Int = 90): CleanupResult = withContext(Dispatchers.IO) {
        try {
            val cutoffDate = System.currentTimeMillis() - (daysToKeep * 24 * 60 * 60 * 1000L)
            val notificationDao = database.notificationDao()
            val oldNotifications = notificationDao.getNotificationsOlderThan(cutoffDate)
            oldNotifications.forEach { notificationDao.deleteNotification(it) }
            CleanupResult(true, "پاک‌سازی موفقیت‌آمیز بود", oldNotifications.size)
        } catch (e: Exception) {
            LogManager.error("DatabaseOptimizer", "cleanup error", e)
            CleanupResult(false, "خطا در پاک‌سازی: ${e.message}", 0)
        }
    }

    suspend fun checkDatabaseHealth(): DatabaseHealthResult = withContext(Dispatchers.IO) {
        try {
            val db = database.openHelper.readableDatabase
            val issues = mutableListOf<String>()
            try {
                val tcount = db.compileStatement("SELECT COUNT(*) FROM sqlite_master WHERE type='table'").simpleQueryForLong()
                if (tcount == 0L) issues.add("No tables found in database")
            } catch (e: Exception) {
                issues.add("Error checking tables: ${e.message}")
            }
            val isHealthy = issues.isEmpty()
            DatabaseHealthResult(isHealthy, if (isHealthy) "دیتابیس سالم است" else "مشکلاتی در دیتابیس یافت شد", issues, System.currentTimeMillis())
        } catch (e: Exception) {
            LogManager.error("DatabaseOptimizer", "health error", e)
            DatabaseHealthResult(false, "خطا در بررسی سلامت: ${e.message}", listOf("Error: ${e.message}"), System.currentTimeMillis())
        }
    }

    suspend fun backupDatabase(backupPath: String): BackupResult = withContext(Dispatchers.IO) {
        try {
            val dbPath = database.openHelper.readableDatabase.path
            java.io.File(dbPath).copyTo(java.io.File(backupPath), overwrite = true)
            BackupResult(true, "پشتیبان‌گیری موفقیت‌آمیز بود", backupPath, System.currentTimeMillis())
        } catch (e: Exception) {
            LogManager.error("DatabaseOptimizer", "backup error", e)
            BackupResult(false, "خطا در پشتیبان‌گیری: ${e.message}", backupPath, System.currentTimeMillis())
        }
    }

    suspend fun restoreDatabase(backupPath: String): RestoreResult = withContext(Dispatchers.IO) {
        try {
            val dbPath = database.openHelper.readableDatabase.path
            java.io.File(backupPath).copyTo(java.io.File(dbPath), overwrite = true)
            RestoreResult(true, "بازگردانی موفقیت‌آمیز بود", System.currentTimeMillis())
        } catch (e: Exception) {
            LogManager.error("DatabaseOptimizer", "restore error", e)
            RestoreResult(false, "خطا در بازگردانی: ${e.message}", System.currentTimeMillis())
        }
    }

    suspend fun clearCache(): CacheClearResult = withContext(Dispatchers.IO) {
        try {
            val db = database.openHelper.writableDatabase
            db.execSQL("PRAGMA shrink_memory")
            CacheClearResult(true, "پاک کردن حافظه موقت موفقیت‌آمیز بود", System.currentTimeMillis())
        } catch (e: Exception) {
            LogManager.error("DatabaseOptimizer", "clear cache error", e)
            CacheClearResult(false, "خطا در پاک کردن حافظه موقت: ${e.message}", System.currentTimeMillis())
        }
    }
}

// Data classes for results
data class OptimizationResult(
    val success: Boolean,
    val message: String,
    val optimizationTime: Long
)

data class CleanupResult(
    val success: Boolean,
    val message: String,
    val deletedRecords: Int
)

data class DatabaseHealthResult(
    val isHealthy: Boolean,
    val message: String,
    val issues: List<String>,
    val checkTime: Long
)

data class BackupResult(
    val success: Boolean,
    val message: String,
    val backupPath: String,
    val backupTime: Long
)

data class RestoreResult(
    val success: Boolean,
    val message: String,
    val restoreTime: Long
)

data class CacheClearResult(
    val success: Boolean,
    val message: String,
    val clearTime: Long
)

