package com.example.ma.utils

import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * فرمت کردن تاریخ‌ها
 */
object DateFormatter {
    
    private val persianCalendar = Calendar.getInstance(Locale("fa", "IR"))
    private val dateFormat = SimpleDateFormat("yyyy/MM/dd HH:mm", Locale("fa", "IR"))
    private val timeFormat = SimpleDateFormat("HH:mm", Locale("fa", "IR"))
    
    fun format(timestamp: Long): String {
        val date = Date(timestamp)
        return dateFormat.format(date)
    }
    
    fun formatTime(timestamp: Long): String {
        val date = Date(timestamp)
        return timeFormat.format(date)
    }
    
    fun formatRelative(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp
        
        return when {
            diff < 60 * 1000 -> "همین الان"
            diff < 60 * 60 * 1000 -> "${diff / (60 * 1000)} دقیقه پیش"
            diff < 24 * 60 * 60 * 1000 -> "${diff / (60 * 60 * 1000)} ساعت پیش"
            diff < 7 * 24 * 60 * 60 * 1000 -> "${diff / (24 * 60 * 60 * 1000)} روز پیش"
            else -> format(timestamp)
        }
    }
    
    fun isToday(timestamp: Long): Boolean {
        val calendar = Calendar.getInstance()
        val today = calendar.timeInMillis
        calendar.timeInMillis = timestamp
        
        return calendar.get(Calendar.YEAR) == Calendar.getInstance().get(Calendar.YEAR) &&
               calendar.get(Calendar.DAY_OF_YEAR) == Calendar.getInstance().get(Calendar.DAY_OF_YEAR)
    }
    
    fun isYesterday(timestamp: Long): Boolean {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -1)
        val yesterday = calendar.timeInMillis
        calendar.timeInMillis = timestamp
        
        return calendar.get(Calendar.YEAR) == Calendar.getInstance().apply { 
            add(Calendar.DAY_OF_YEAR, -1) 
        }.get(Calendar.YEAR) &&
               calendar.get(Calendar.DAY_OF_YEAR) == Calendar.getInstance().apply { 
                   add(Calendar.DAY_OF_YEAR, -1) 
               }.get(Calendar.DAY_OF_YEAR)
    }
} 