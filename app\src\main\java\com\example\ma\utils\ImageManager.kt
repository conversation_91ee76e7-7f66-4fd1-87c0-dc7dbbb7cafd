package com.example.ma.utils

import android.content.Context
import android.graphics.drawable.Drawable
import android.widget.ImageView
import androidx.annotation.DrawableRes
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.example.ma.R
import com.example.ma.utils.LogManager
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدیریت بارگذاری و کش تصاویر با استفاده از Glide
 */
@Singleton
class ImageManager @Inject constructor(
    private val context: Context
) {

    /**
     * بارگذاری تصویر پروفایل
     */
    fun loadProfileImage(
        imageView: ImageView,
        imageUrl: String?,
        onSuccess: (() -> Unit)? = null,
        onError: (() -> Unit)? = null
    ) {
        val requestOptions = RequestOptions()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .override(300, 300)
            .centerCrop()
            .placeholder(R.drawable.profile_placeholder)
            .error(R.drawable.profile_placeholder)

        Glide.with(context)
            .load(imageUrl)
            .apply(requestOptions)
            .listener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>,
                    isFirstResource: Boolean
                ): Boolean {
                    LogManager.warning("ImageManager", "خطا در بارگذاری تصویر پروفایل: ${e?.message}")
                    onError?.invoke()
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable>,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    LogManager.debug("ImageManager", "تصویر پروفایل با موفقیت بارگذاری شد")
                    onSuccess?.invoke()
                    return false
                }
            })
            .into(imageView)
    }

    /**
     * بارگذاری تصویر کوچک (thumbnail)
     */
    fun loadThumbnail(
        imageView: ImageView,
        imageUrl: String?,
        placeholderRes: Int = R.drawable.profile_placeholder
    ) {
        val requestOptions = RequestOptions()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .override(200, 200) // سایز کوچک برای thumbnail
            .centerCrop()
            .placeholder(placeholderRes)
            .error(placeholderRes)

        Glide.with(context)
            .load(imageUrl)
            .apply(requestOptions)
            .into(imageView)
    }

    /**
     * پیش‌بارگذاری تصویر برای بهبود performance
     */
    fun preloadImage(imageUrl: String?) {
        if (!imageUrl.isNullOrEmpty()) {
            Glide.with(context)
                .load(imageUrl)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .preload()
        }
    }

    /**
     * پاک کردن کش تصاویر
     */
    fun clearImageCache() {
        try {
            Glide.get(context).clearMemory()
            Thread {
                Glide.get(context).clearDiskCache()
            }.start()
            LogManager.info("ImageManager", "کش تصاویر پاک شد")
        } catch (e: Exception) {
            LogManager.error("ImageManager", "خطا در پاک کردن کش تصاویر", e)
        }
    }

    /**
     * دریافت اندازه کش تصاویر
     */
    fun getCacheSize(): Long {
        return try {
            val cacheDir = Glide.getPhotoCacheDir(context)
            cacheDir?.let { dir ->
                dir.listFiles()?.sumOf { it.length() } ?: 0L
            } ?: 0L
        } catch (e: Exception) {
            LogManager.error("ImageManager", "خطا در محاسبه اندازه کش", e)
            0L
        }
    }

    /**
     * بهینه‌سازی memory usage
     */
    fun optimizeMemoryUsage() {
        try {
            // پاک کردن memory cache
            Glide.get(context).clearMemory()
            
            // تنظیم memory cache size
            Glide.get(context).setMemoryCategory(com.bumptech.glide.MemoryCategory.LOW)
            
            LogManager.info("ImageManager", "بهینه‌سازی memory usage تکمیل شد")
        } catch (e: Exception) {
            LogManager.error("ImageManager", "خطا در بهینه‌سازی memory usage", e)
        }
    }

    /**
     * بارگذاری تصویر با اندازه سفارشی
     */
    fun loadImageWithSize(
        imageView: ImageView,
        imageUrl: String?,
        width: Int,
        height: Int,
        placeholderRes: Int = R.drawable.profile_placeholder
    ) {
        val requestOptions = RequestOptions()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .override(width, height)
            .centerCrop()
            .placeholder(placeholderRes)
            .error(placeholderRes)

        Glide.with(context)
            .load(imageUrl)
            .apply(requestOptions)
            .into(imageView)
    }

    /**
     * بارگذاری تصویر با کیفیت پایین برای شبکه کند
     */
    fun loadImageLowQuality(
        imageView: ImageView,
        imageUrl: String?,
        placeholderRes: Int = R.drawable.profile_placeholder
    ) {
        val requestOptions = RequestOptions()
            .diskCacheStrategy(DiskCacheStrategy.DATA)
            .override(150, 150)
            .centerCrop()
            .placeholder(placeholderRes)
            .error(placeholderRes)

        Glide.with(context)
            .load(imageUrl)
            .apply(requestOptions)
            .into(imageView)
    }

    /**
     * بارگذاری تصویر با انیمیشن
     */
    fun loadImageWithAnimation(
        imageView: ImageView,
        imageUrl: String?,
        placeholderRes: Int = R.drawable.profile_placeholder
    ) {
        val requestOptions = RequestOptions()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .placeholder(placeholderRes)
            .error(placeholderRes)

        Glide.with(context)
            .load(imageUrl)
            .apply(requestOptions)
            .transition(com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions.withCrossFade())
            .into(imageView)
    }

    /**
     * بارگذاری تصویر با priority بالا
     */
    fun loadImageHighPriority(
        imageView: ImageView,
        imageUrl: String?,
        placeholderRes: Int = R.drawable.profile_placeholder
    ) {
        val requestOptions = RequestOptions()
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .priority(com.bumptech.glide.Priority.HIGH)
            .placeholder(placeholderRes)
            .error(placeholderRes)

        Glide.with(context)
            .load(imageUrl)
            .apply(requestOptions)
            .into(imageView)
    }

    /**
     * بررسی وجود تصویر در کش
     */
    fun isImageCached(imageUrl: String?): Boolean {
        return try {
            if (imageUrl.isNullOrEmpty()) return false
            
            val future = Glide.with(context)
                .load(imageUrl)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .onlyRetrieveFromCache(true)
                .submit()
            
            val drawable = future.get()
            drawable != null
        } catch (e: Exception) {
            false
        }
    }

    /**
     * دریافت تصویر از کش
     */
    fun getCachedImage(imageUrl: String?): Drawable? {
        return try {
            if (imageUrl.isNullOrEmpty()) return null
            
            val future = Glide.with(context)
                .load(imageUrl)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .submit()
            
            future.get()
        } catch (e: Exception) {
            LogManager.error("ImageManager", "خطا در دریافت تصویر از کش", e)
            null
        }
    }
}
