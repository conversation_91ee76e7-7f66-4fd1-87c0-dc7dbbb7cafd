package com.example.ma.utils

import android.content.Context
import android.util.Log
import com.example.ma.config.AppConfig

object LogManager {
    private const val TAG = "MA_APP"
    
    fun init(context: Context) {
        if (AppConfig.DEBUG_MODE) {
            Log.d(TAG, "LogManager initialized")
        }
    }
    
    fun debug(tag: String, message: String) {
        if (AppConfig.DEBUG_MODE) {
            Log.d("$TAG:$tag", message)
        }
    }
    
    fun info(tag: String, message: String) {
        if (AppConfig.ENABLE_LOGGING) {
            Log.i("$TAG:$tag", message)
        }
    }

    fun warning(tag: String, message: String) {
        if (AppConfig.ENABLE_LOGGING) {
            Log.w("$TAG:$tag", message)
        }
    }

    fun error(tag: String, message: String, throwable: Throwable? = null) {
        if (AppConfig.ENABLE_LOGGING) {
            Log.e("$TAG:$tag", message, throwable)
        }
    }
} 