package com.example.ma.utils

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import com.example.ma.config.AppConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.net.HttpURLConnection
import java.net.URL
import javax.inject.Inject
import javax.inject.Singleton

/**
 * بهینه‌سازی عملکرد شبکه و اتصالات
 */
@Singleton
class NetworkOptimizer @Inject constructor(
    private val context: Context
) {

    /**
     * بررسی وضعیت اتصال به اینترنت
     */
    fun isNetworkAvailable(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return false
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
            capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
            capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            networkInfo?.isConnected == true
        }
    }

    /**
     * تشخیص نوع اتصال شبکه
     */
    fun getNetworkType(): NetworkType {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork ?: return NetworkType.NONE
            val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return NetworkType.NONE
            
            return when {
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> NetworkType.WIFI
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> NetworkType.CELLULAR
                capabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> NetworkType.ETHERNET
                else -> NetworkType.OTHER
            }
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo ?: return NetworkType.NONE
            
            return when (networkInfo.type) {
                ConnectivityManager.TYPE_WIFI -> NetworkType.WIFI
                ConnectivityManager.TYPE_MOBILE -> NetworkType.CELLULAR
                ConnectivityManager.TYPE_ETHERNET -> NetworkType.ETHERNET
                else -> NetworkType.OTHER
            }
        }
    }

    /**
     * تست سرعت اتصال به سرور
     */
    suspend fun testConnectionSpeed(url: String = AppConfig.SUPABASE_URL): ConnectionSpeedResult = withContext(Dispatchers.IO) {
        try {
            LogManager.info("NetworkOptimizer", "شروع تست سرعت اتصال...")
            
            val startTime = System.currentTimeMillis()
            val connection = URL(url).openConnection() as HttpURLConnection
            
            connection.apply {
                requestMethod = "HEAD"
                connectTimeout = AppConfig.Network.CONNECT_TIMEOUT.toInt() * 1000
                readTimeout = AppConfig.Network.READ_TIMEOUT.toInt() * 1000
                setRequestProperty("User-Agent", "MA-App/${AppConfig.APP_VERSION_NAME}")
            }
            
            val responseCode = connection.responseCode
            val endTime = System.currentTimeMillis()
            val latency = endTime - startTime
            
            connection.disconnect()
            
            val speedCategory = when {
                latency < 100 -> SpeedCategory.EXCELLENT
                latency < 300 -> SpeedCategory.GOOD
                latency < 1000 -> SpeedCategory.FAIR
                else -> SpeedCategory.POOR
            }
            
            LogManager.info("NetworkOptimizer", "تست سرعت تکمیل شد: ${latency}ms")
            
            ConnectionSpeedResult(
                success = responseCode == 200,
                latency = latency,
                speedCategory = speedCategory,
                networkType = getNetworkType()
            )
            
        } catch (e: Exception) {
            LogManager.error("NetworkOptimizer", "خطا در تست سرعت اتصال", e)
            ConnectionSpeedResult(
                success = false,
                latency = -1,
                speedCategory = SpeedCategory.POOR,
                networkType = getNetworkType(),
                error = e.message
            )
        }
    }

    /**
     * بهینه‌سازی تنظیمات شبکه بر اساس نوع اتصال
     */
    fun getOptimizedNetworkConfig(): NetworkConfig {
        val networkType = getNetworkType()
        
        return when (networkType) {
            NetworkType.WIFI -> NetworkConfig(
                connectTimeout = AppConfig.Network.CONNECT_TIMEOUT,
                readTimeout = AppConfig.Network.READ_TIMEOUT,
                maxRetries = AppConfig.Network.MAX_RETRIES,
                retryDelay = AppConfig.Network.RETRY_DELAY_MS,
                enableCompression = true,
                enableCaching = true
            )
            NetworkType.CELLULAR -> NetworkConfig(
                connectTimeout = AppConfig.Network.CONNECT_TIMEOUT + 10,
                readTimeout = AppConfig.Network.READ_TIMEOUT + 10,
                maxRetries = AppConfig.Network.MAX_RETRIES + 1,
                retryDelay = AppConfig.Network.RETRY_DELAY_MS * 2,
                enableCompression = true,
                enableCaching = true
            )
            else -> NetworkConfig(
                connectTimeout = AppConfig.Network.CONNECT_TIMEOUT + 20,
                readTimeout = AppConfig.Network.READ_TIMEOUT + 20,
                maxRetries = AppConfig.Network.MAX_RETRIES + 2,
                retryDelay = AppConfig.Network.RETRY_DELAY_MS * 3,
                enableCompression = true,
                enableCaching = false
            )
        }
    }

    /**
     * مانیتورینگ کیفیت اتصال
     */
    suspend fun monitorNetworkQuality(): NetworkQualityReport = withContext(Dispatchers.IO) {
        try {
            val isAvailable = isNetworkAvailable()
            val networkType = getNetworkType()
            val speedResult = if (isAvailable) testConnectionSpeed() else null
            
            val quality = when {
                !isAvailable -> NetworkQuality.NO_CONNECTION
                speedResult?.speedCategory == SpeedCategory.EXCELLENT -> NetworkQuality.EXCELLENT
                speedResult?.speedCategory == SpeedCategory.GOOD -> NetworkQuality.GOOD
                speedResult?.speedCategory == SpeedCategory.FAIR -> NetworkQuality.FAIR
                else -> NetworkQuality.POOR
            }
            
            NetworkQualityReport(
                isAvailable = isAvailable,
                networkType = networkType,
                quality = quality,
                latency = speedResult?.latency ?: -1,
                timestamp = System.currentTimeMillis()
            )
            
        } catch (e: Exception) {
            LogManager.error("NetworkOptimizer", "خطا در مانیتورینگ کیفیت شبکه", e)
            NetworkQualityReport(
                isAvailable = false,
                networkType = NetworkType.NONE,
                quality = NetworkQuality.NO_CONNECTION,
                latency = -1,
                timestamp = System.currentTimeMillis(),
                error = e.message
            )
        }
    }

    /**
     * گزارش کامل وضعیت شبکه
     */
    suspend fun generateNetworkReport(): String = withContext(Dispatchers.IO) {
        val report = StringBuilder()
        
        report.appendLine("=== گزارش وضعیت شبکه ===")
        report.appendLine("زمان: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(java.util.Date())}")
        report.appendLine()
        
        val qualityReport = monitorNetworkQuality()
        
        report.appendLine("وضعیت اتصال: ${if (qualityReport.isAvailable) "✅ متصل" else "❌ قطع"}")
        report.appendLine("نوع شبکه: ${qualityReport.networkType}")
        report.appendLine("کیفیت اتصال: ${qualityReport.quality}")
        
        if (qualityReport.latency > 0) {
            report.appendLine("تاخیر: ${qualityReport.latency}ms")
        }
        
        if (qualityReport.error != null) {
            report.appendLine("خطا: ${qualityReport.error}")
        }
        
        report.appendLine()
        
        // تنظیمات بهینه
        val config = getOptimizedNetworkConfig()
        report.appendLine("تنظیمات بهینه:")
        report.appendLine("  Connect Timeout: ${config.connectTimeout}s")
        report.appendLine("  Read Timeout: ${config.readTimeout}s")
        report.appendLine("  Max Retries: ${config.maxRetries}")
        report.appendLine("  Retry Delay: ${config.retryDelay}ms")
        report.appendLine("  Compression: ${if (config.enableCompression) "فعال" else "غیرفعال"}")
        report.appendLine("  Caching: ${if (config.enableCaching) "فعال" else "غیرفعال"}")
        
        report.appendLine()
        report.appendLine("=== پایان گزارش ===")
        
        report.toString()
    }
}

/**
 * انواع شبکه
 */
enum class NetworkType {
    WIFI, CELLULAR, ETHERNET, OTHER, NONE
}

/**
 * دسته‌بندی سرعت
 */
enum class SpeedCategory {
    EXCELLENT, GOOD, FAIR, POOR
}

/**
 * کیفیت شبکه
 */
enum class NetworkQuality {
    EXCELLENT, GOOD, FAIR, POOR, NO_CONNECTION
}

/**
 * نتیجه تست سرعت
 */
data class ConnectionSpeedResult(
    val success: Boolean,
    val latency: Long,
    val speedCategory: SpeedCategory,
    val networkType: NetworkType,
    val error: String? = null
)

/**
 * تنظیمات بهینه شبکه
 */
data class NetworkConfig(
    val connectTimeout: Long,
    val readTimeout: Long,
    val maxRetries: Int,
    val retryDelay: Long,
    val enableCompression: Boolean,
    val enableCaching: Boolean
)

/**
 * گزارش کیفیت شبکه
 */
data class NetworkQualityReport(
    val isAvailable: Boolean,
    val networkType: NetworkType,
    val quality: NetworkQuality,
    val latency: Long,
    val timestamp: Long,
    val error: String? = null
)
