package com.example.ma.utils

import android.content.Context
import android.content.SharedPreferences
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدیریت SharedPreferences برای تنظیمات اپلیکیشن
 */
@Singleton
class PreferencesManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val PREF_NAME = "ma_preferences"
        
        // Keys for settings
        private const val KEY_PUSH_NOTIFICATIONS = "push_notifications_enabled"
        private const val KEY_SOUND_VIBRATION = "sound_vibration_enabled"
        private const val KEY_TRANSACTION_ALERTS = "transaction_alerts_enabled"
        private const val KEY_BIOMETRIC_AUTH = "biometric_auth_enabled"
        private const val KEY_AUTO_LOCK = "auto_lock_enabled"
        private const val KEY_AUTO_SYNC = "auto_sync_enabled"
        
        // Keys for user data
        private const val KEY_USER_ID = "user_id"
        private const val KEY_USERNAME = "username"
        private const val KEY_ACCESS_TOKEN = "access_token"
        private const val KEY_REFRESH_TOKEN = "refresh_token"
        private const val KEY_LAST_SYNC_TIME = "last_sync_time"
        
        // Keys for app state
        private const val KEY_FIRST_LAUNCH = "first_launch"
        private const val KEY_ONBOARDING_COMPLETED = "onboarding_completed"
    }
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    
    // Settings Methods
    fun getPushNotificationsEnabled(): Boolean = prefs.getBoolean(KEY_PUSH_NOTIFICATIONS, true)
    fun setPushNotificationsEnabled(enabled: Boolean) = prefs.edit().putBoolean(KEY_PUSH_NOTIFICATIONS, enabled).apply()
    
    fun getSoundVibrationEnabled(): Boolean = prefs.getBoolean(KEY_SOUND_VIBRATION, true)
    fun setSoundVibrationEnabled(enabled: Boolean) = prefs.edit().putBoolean(KEY_SOUND_VIBRATION, enabled).apply()
    
    fun getTransactionAlertsEnabled(): Boolean = prefs.getBoolean(KEY_TRANSACTION_ALERTS, true)
    fun setTransactionAlertsEnabled(enabled: Boolean) = prefs.edit().putBoolean(KEY_TRANSACTION_ALERTS, enabled).apply()
    
    fun getBiometricAuthEnabled(): Boolean = prefs.getBoolean(KEY_BIOMETRIC_AUTH, false)
    fun setBiometricAuthEnabled(enabled: Boolean) = prefs.edit().putBoolean(KEY_BIOMETRIC_AUTH, enabled).apply()
    
    fun getAutoLockEnabled(): Boolean = prefs.getBoolean(KEY_AUTO_LOCK, false)
    fun setAutoLockEnabled(enabled: Boolean) = prefs.edit().putBoolean(KEY_AUTO_LOCK, enabled).apply()
    
    fun getAutoSyncEnabled(): Boolean = prefs.getBoolean(KEY_AUTO_SYNC, true)
    fun setAutoSyncEnabled(enabled: Boolean) = prefs.edit().putBoolean(KEY_AUTO_SYNC, enabled).apply()
    
    // User Data Methods
    fun getUserId(): String? = prefs.getString(KEY_USER_ID, null)
    fun setUserId(userId: String) = prefs.edit().putString(KEY_USER_ID, userId).apply()
    
    fun getUsername(): String? = prefs.getString(KEY_USERNAME, null)
    fun setUsername(username: String) = prefs.edit().putString(KEY_USERNAME, username).apply()
    
    fun getAccessToken(): String? = prefs.getString(KEY_ACCESS_TOKEN, null)
    fun setAccessToken(token: String) = prefs.edit().putString(KEY_ACCESS_TOKEN, token).apply()
    
    fun getRefreshToken(): String? = prefs.getString(KEY_REFRESH_TOKEN, null)
    fun setRefreshToken(token: String) = prefs.edit().putString(KEY_REFRESH_TOKEN, token).apply()
    
    fun getLastSyncTime(): Long = prefs.getLong(KEY_LAST_SYNC_TIME, 0L)
    fun setLastSyncTime(time: Long) = prefs.edit().putLong(KEY_LAST_SYNC_TIME, time).apply()
    
    // App State Methods
    fun isFirstLaunch(): Boolean = prefs.getBoolean(KEY_FIRST_LAUNCH, true)
    fun setFirstLaunch(isFirst: Boolean) = prefs.edit().putBoolean(KEY_FIRST_LAUNCH, isFirst).apply()
    
    fun isOnboardingCompleted(): Boolean = prefs.getBoolean(KEY_ONBOARDING_COMPLETED, false)
    fun setOnboardingCompleted(completed: Boolean) = prefs.edit().putBoolean(KEY_ONBOARDING_COMPLETED, completed).apply()
    
    // Utility Methods
    fun clearUserData() {
        prefs.edit()
            .remove(KEY_USER_ID)
            .remove(KEY_USERNAME)
            .remove(KEY_ACCESS_TOKEN)
            .remove(KEY_REFRESH_TOKEN)
            .apply()
    }
    
    fun clearAllData() {
        prefs.edit().clear().apply()
    }
    
    fun isUserLoggedIn(): Boolean {
        return !getUserId().isNullOrEmpty() && !getAccessToken().isNullOrEmpty()
    }
    
    /**
     * دریافت تمام تنظیمات به صورت Map برای debugging
     */
    fun getAllSettings(): Map<String, Any?> {
        return mapOf(
            "push_notifications" to getPushNotificationsEnabled(),
            "sound_vibration" to getSoundVibrationEnabled(),
            "transaction_alerts" to getTransactionAlertsEnabled(),
            "biometric_auth" to getBiometricAuthEnabled(),
            "auto_lock" to getAutoLockEnabled(),
            "auto_sync" to getAutoSyncEnabled(),
            "user_id" to getUserId(),
            "username" to getUsername(),
            "is_logged_in" to isUserLoggedIn(),
            "first_launch" to isFirstLaunch(),
            "onboarding_completed" to isOnboardingCompleted(),
            "last_sync_time" to getLastSyncTime()
        )
    }
}
