package com.example.ma.utils

import com.example.ma.data.remote.SupabaseClient
import com.example.ma.utils.LogManager
import io.github.jan.supabase.postgrest.query.Columns
import io.github.jan.supabase.postgrest.query.Order
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * تست اتصال به Supabase
 */
@Singleton
class SupabaseConnectionTest @Inject constructor(
    private val supabaseClient: SupabaseClient
) {

    /**
     * تست کامل اتصال به Supabase
     */
    suspend fun testFullConnection(): FullConnectionTestResult = withContext(Dispatchers.IO) {
        try {
            LogManager.info("SupabaseTest", "شروع تست کامل اتصال به Supabase...")
            
            val results = mutableListOf<ConnectionTestResult>()
            
            // تست اتصال به database
            val dbResult = testDatabaseConnection()
            results.add(dbResult)
            
            // تست اتصال به auth
            val authResult = testAuthConnection()
            results.add(authResult)
            
            // تست اتصال به realtime
            val realtimeResult = testRealtimeConnection()
            results.add(realtimeResult)
            
            // تست اتصال به storage
            val storageResult = testStorageConnection()
            results.add(storageResult)
            
            val allSuccessful = results.all { it.success }
            val message = if (allSuccessful) "تمام اتصالات موفقیت‌آمیز" else "برخی اتصالات ناموفق"
            
            LogManager.info("SupabaseTest", "تست کامل اتصال تکمیل شد: $message")
            
            FullConnectionTestResult(
                success = allSuccessful,
                message = message,
                results = results,
                testTime = System.currentTimeMillis()
            )
            
        } catch (e: Exception) {
            LogManager.error("SupabaseTest", "خطا در تست کامل اتصال", e)
            FullConnectionTestResult(
                success = false,
                message = "خطا در تست اتصال: ${e.message}",
                results = emptyList(),
                testTime = System.currentTimeMillis()
            )
        }
    }

    /**
     * تست اتصال به database
     */
    private suspend fun testDatabaseConnection(): ConnectionTestResult = withContext(Dispatchers.IO) {
        try {
            LogManager.debug("SupabaseTest", "تست اتصال به database...")
            
            // تست ساده: دریافت یک ردیف از جدول users (اگر وجود داشته باشد)
            val rows = supabaseClient.database
                .from("users")
                .select(columns = Columns.list("id")) {
                    order(column = "id", order = Order.ASCENDING)
                    limit(count = 1)
                }
                .decodeList<Map<String, Any?>>()

            ConnectionTestResult(
                success = true,
                message = "اتصال به database موفقیت‌آمیز",
                details = mapOf("row_count" to rows.size)
            )

        } catch (e: Exception) {
            LogManager.warning("SupabaseTest", "خطا در اتصال به database: ${e.message}")
            ConnectionTestResult(
                success = false,
                message = "خطا در اتصال به database: ${e.message}",
                details = mapOf("error" to e.toString())
            )
        }
    }

    /**
     * تست اتصال به auth
     */
    private suspend fun testAuthConnection(): ConnectionTestResult = withContext(Dispatchers.IO) {
        try {
            LogManager.debug("SupabaseTest", "تست اتصال به auth...")
            
            // بررسی وضعیت session فعلی
            val session = supabaseClient.auth.currentSessionOrNull()
            
            LogManager.debug("SupabaseTest", "وضعیت session: ${if (session != null) "موجود" else "خالی"}")
            
            ConnectionTestResult(
                success = true,
                message = "اتصال به auth موفقیت‌آمیز",
                details = mapOf(
                    "session_exists" to (session != null),
                    "user_id" to (session?.user?.id ?: "null")
                )
            )

        } catch (e: Exception) {
            LogManager.warning("SupabaseTest", "خطا در اتصال به auth: ${e.message}")
            ConnectionTestResult(
                success = false,
                message = "خطا در اتصال به auth: ${e.message}",
                details = mapOf("error" to e.toString())
            )
        }
    }

    /**
     * تست اتصال به realtime
     */
    suspend fun testRealtimeConnection(): ConnectionTestResult = withContext(Dispatchers.IO) {
        try {
            LogManager.debug("SupabaseTest", "تست اتصال به realtime...")
            
            // TODO: پیاده‌سازی تست realtime
            
            ConnectionTestResult(
                success = true,
                message = "realtime در دسترس است",
                details = mapOf("status" to "available")
            )

        } catch (e: Exception) {
            LogManager.warning("SupabaseTest", "خطا در اتصال به realtime: ${e.message}")
            ConnectionTestResult(
                success = false,
                message = "خطا در اتصال به realtime: ${e.message}",
                details = mapOf("error" to e.toString())
            )
        }
    }

    /**
     * تست اتصال به storage
     */
    suspend fun testStorageConnection(): ConnectionTestResult = withContext(Dispatchers.IO) {
        try {
            LogManager.debug("SupabaseTest", "تست اتصال به storage...")
            
            // تست ساده: لیست buckets
            val buckets = supabaseClient.storage.retrieveBuckets()
            
            LogManager.debug("SupabaseTest", "تعداد buckets: ${buckets.size}")
            
            ConnectionTestResult(
                success = true,
                message = "اتصال به storage موفقیت‌آمیز",
                details = mapOf("bucket_count" to buckets.size)
            )

        } catch (e: Exception) {
            LogManager.warning("SupabaseTest", "خطا در اتصال به storage: ${e.message}")
            ConnectionTestResult(
                success = false,
                message = "خطا در اتصال به storage: ${e.message}",
                details = mapOf("error" to e.toString())
            )
        }
    }

    /**
     * تست عملکرد query
     */
    suspend fun testQueryPerformance(): QueryPerformanceResult = withContext(Dispatchers.IO) {
        try {
            LogManager.debug("SupabaseTest", "تست عملکرد query...")
            
            val startTime = System.currentTimeMillis()
            
            // تست query ساده
            val rows = supabaseClient.database
                .from("users")
                .select(columns = Columns.list("id", "email")) {
                    limit(count = 10)
                }
                .decodeList<Map<String, Any?>>()
            
            val endTime = System.currentTimeMillis()
            val duration = endTime - startTime
            
            LogManager.debug("SupabaseTest", "Query در ${duration}ms اجرا شد")
            
            QueryPerformanceResult(
                success = true,
                message = "تست عملکرد query موفقیت‌آمیز",
                duration = duration,
                recordCount = rows.size
            )
            
        } catch (e: Exception) {
            LogManager.warning("SupabaseTest", "خطا در تست عملکرد query: ${e.message}")
            QueryPerformanceResult(
                success = false,
                message = "خطا در تست عملکرد query: ${e.message}",
                duration = 0,
                recordCount = 0
            )
        }
    }

    /**
     * تست اتصال شبکه
     */
    suspend fun testNetworkConnection(): NetworkTestResult = withContext(Dispatchers.IO) {
        try {
            LogManager.debug("SupabaseTest", "تست اتصال شبکه...")
            
            val startTime = System.currentTimeMillis()
            
            // تست ping ساده (یک select سبک)
            val rows = supabaseClient.database
                .from("users")
                .select(columns = Columns.list("id")) {
                    limit(count = 1)
                }
                .decodeList<Map<String, Any?>>()
            
            val endTime = System.currentTimeMillis()
            val latency = endTime - startTime
            
            val status = when {
                latency < 100 -> "عالی"
                latency < 500 -> "خوب"
                latency < 1000 -> "متوسط"
                else -> "کند"
            }
            
            LogManager.debug("SupabaseTest", "Latency: ${latency}ms ($status)")
            
            NetworkTestResult(
                success = true,
                message = "تست شبکه موفقیت‌آمیز",
                latency = latency,
                status = status
            )
            
        } catch (e: Exception) {
            LogManager.warning("SupabaseTest", "خطا در تست شبکه: ${e.message}")
            NetworkTestResult(
                success = false,
                message = "خطا در تست شبکه: ${e.message}",
                latency = 0,
                status = "ناموفق"
            )
        }
    }
}

// Data classes for results
data class ConnectionTestResult(
    val success: Boolean,
    val message: String,
    val details: Map<String, Any>
)

data class FullConnectionTestResult(
    val success: Boolean,
    val message: String,
    val results: List<ConnectionTestResult>,
    val testTime: Long
)

data class QueryPerformanceResult(
    val success: Boolean,
    val message: String,
    val duration: Long,
    val recordCount: Int
)

data class NetworkTestResult(
    val success: Boolean,
    val message: String,
    val latency: Long,
    val status: String
)
