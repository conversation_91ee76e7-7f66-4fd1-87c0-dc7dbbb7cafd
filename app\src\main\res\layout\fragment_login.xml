<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:background="@color/md_theme_background"
    tools:context=".presentation.auth.LoginFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="24dp">

        <!-- Header Section -->
        <LinearLayout
            android:id="@+id/headerLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginTop="48dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- App Logo -->
            <ImageView
                android:id="@+id/ivLogo"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:src="@drawable/ic_launcher_foreground"
                android:background="@drawable/circle_background"
                android:padding="24dp"
                android:contentDescription="@string/app_logo" />

            <!-- App Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/app_name"
                android:textAppearance="@style/TextAppearance.MA.HeadlineLarge"
                android:textColor="@color/md_theme_primary" />

            <!-- Subtitle -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/login_subtitle"
                android:textAppearance="@style/TextAppearance.MA.BodyMedium"
                android:textColor="@color/md_theme_onSurfaceVariant"
                android:gravity="center" />

        </LinearLayout>

        <!-- Login Form -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/loginCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            style="@style/Widget.MA.Card"
            app:layout_constraintTop_toBottomOf="@id/headerLayout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- Email Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/email"
                    style="@style/Widget.MA.TextInputLayout"
                    app:startIconDrawable="@drawable/ic_email"
                    app:startIconTint="@color/md_theme_primary"
                    app:errorEnabled="true">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etEmail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textEmailAddress"
                        android:maxLines="1"
                        android:text="@string/sample_email" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Password Input -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:hint="@string/password"
                    style="@style/Widget.MA.TextInputLayout"
                    app:startIconDrawable="@drawable/ic_lock"
                    app:startIconTint="@color/md_theme_primary"
                    app:endIconMode="password_toggle"
                    app:errorEnabled="true">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etPassword"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword"
                        android:maxLines="1"
                        android:text="@string/sample_password" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Remember Me & Forgot Password -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <com.google.android.material.checkbox.MaterialCheckBox
                        android:id="@+id/cbRememberMe"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/remember_me"
                        android:textColor="@color/md_theme_onSurfaceVariant" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnForgotPassword"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/forgot_password"
                        style="@style/Widget.Material3.Button.TextButton"
                        android:textColor="@color/md_theme_primary" />

                </LinearLayout>

                <!-- Login Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnLogin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:text="@string/login"
                    style="@style/Widget.MA.Button"
                    android:enabled="false" />

                <!-- Divider -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1"
                        android:background="@color/md_theme_outlineVariant" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="16dp"
                        android:text="@string/or"
                        android:textAppearance="@style/TextAppearance.MA.BodyMedium"
                        android:textColor="@color/md_theme_onSurfaceVariant" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1"
                        android:background="@color/md_theme_outlineVariant" />

                </LinearLayout>

                <!-- Social Login Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:orientation="vertical">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnGoogleSignIn"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/continue_with_google"
                        android:drawableStart="@drawable/ic_google"
                        android:drawablePadding="12dp"
                        style="@style/Widget.MA.Button.Secondary"
                        app:icon="@drawable/ic_google"
                        app:iconGravity="textStart"
                        app:iconPadding="12dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnAppleSignIn"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:text="@string/continue_with_apple"
                        android:drawableStart="@drawable/ic_apple"
                        android:drawablePadding="12dp"
                        style="@style/Widget.MA.Button.Secondary"
                        app:icon="@drawable/ic_apple"
                        app:iconGravity="textStart"
                        app:iconPadding="12dp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Sign Up Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:orientation="horizontal"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@id/loginCard"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/dont_have_account"
                android:textAppearance="@style/TextAppearance.MA.BodyMedium"
                android:textColor="@color/md_theme_onSurfaceVariant" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnSignUp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="@string/sign_up"
                style="@style/Widget.Material3.Button.TextButton"
                android:textColor="@color/md_theme_primary" />

        </LinearLayout>

        <!-- Loading Progress -->
        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progressIndicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:indeterminate="true"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/btnSignUp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView> 