<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    style="@style/Widget.MA.Card"
    app:cardElevation="4dp"
    app:cardCornerRadius="16dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Status Indicator -->
        <View
            android:id="@+id/statusIndicator"
            android:layout_width="4dp"
            android:layout_height="0dp"
            android:background="@color/success_green"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- Transaction Type Icon -->
        <ImageView
            android:id="@+id/ivTransactionType"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginStart="16dp"
            android:background="@drawable/circle_background"
            android:padding="12dp"
            android:src="@drawable/ic_expense"
            android:contentDescription="@string/transaction_type"
            app:layout_constraintStart_toEndOf="@id/statusIndicator"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Recurring Indicator -->
        <ImageView
            android:id="@+id/ivRecurring"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/ic_recurring"
            android:contentDescription="@string/recurring_transaction"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/ivTransactionType"
            app:layout_constraintTop_toTopOf="@id/ivTransactionType" />

        <!-- Amount -->
        <TextView
            android:id="@+id/tvAmount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:textAppearance="@style/TextAppearance.MA.TitleLarge"
            android:textColor="@color/expense_red"
            android:gravity="end"
            tools:text="$150.00"
            app:layout_constraintStart_toEndOf="@id/ivRecurring"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/ivTransactionType" />

        <!-- Description -->
        <TextView
            android:id="@+id/tvDescription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:textAppearance="@style/TextAppearance.MA.TitleMedium"
            android:textColor="@color/md_theme_onSurface"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="Grocery shopping at Walmart"
            app:layout_constraintStart_toEndOf="@id/statusIndicator"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivTransactionType" />

        <!-- Category and Subcategory -->
        <LinearLayout
            android:id="@+id/categoryLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintStart_toEndOf="@id/statusIndicator"
            app:layout_constraintTop_toBottomOf="@id/tvDescription">

            <TextView
                android:id="@+id/tvCategory"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.MA.BodyMedium"
                android:textColor="@color/md_theme_primary"
                android:background="@drawable/category_background"
                android:paddingHorizontal="12dp"
                android:paddingVertical="4dp"
                tools:text="Food" />

            <TextView
                android:id="@+id/tvSubcategory"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textAppearance="@style/TextAppearance.MA.BodySmall"
                android:textColor="@color/md_theme_onSurfaceVariant"
                android:background="@drawable/subcategory_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp"
                tools:text="Groceries" />

        </LinearLayout>

        <!-- Date and Time -->
        <LinearLayout
            android:id="@+id/dateTimeLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            app:layout_constraintStart_toEndOf="@id/statusIndicator"
            app:layout_constraintTop_toBottomOf="@id/categoryLayout">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                android:src="@drawable/ic_calendar"
                android:contentDescription="@string/date"
                android:tint="@color/md_theme_onSurfaceVariant" />

            <TextView
                android:id="@+id/tvDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.MA.BodySmall"
                android:textColor="@color/md_theme_onSurfaceVariant"
                tools:text="Dec 15, 2024" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="4dp"
                android:textAppearance="@style/TextAppearance.MA.BodySmall"
                android:textColor="@color/md_theme_onSurfaceVariant"
                tools:text="•" />

            <TextView
                android:id="@+id/tvTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.MA.BodySmall"
                android:textColor="@color/md_theme_onSurfaceVariant"
                tools:text="14:30" />

        </LinearLayout>

        <!-- Location -->
        <LinearLayout
            android:id="@+id/locationLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:visibility="gone"
            app:layout_constraintStart_toEndOf="@id/statusIndicator"
            app:layout_constraintTop_toBottomOf="@id/dateTimeLayout"
            tools:visibility="visible">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="4dp"
                android:src="@drawable/ic_location"
                android:contentDescription="@string/location"
                android:tint="@color/md_theme_onSurfaceVariant" />

            <TextView
                android:id="@+id/tvLocation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.MA.BodySmall"
                android:textColor="@color/md_theme_onSurfaceVariant"
                tools:text="Walmart Supercenter" />

        </LinearLayout>

        <!-- Tags -->
        <com.google.android.material.chip.ChipGroup
            android:id="@+id/chipGroupTags"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:visibility="gone"
            app:chipSpacingHorizontal="8dp"
            app:chipSpacingVertical="4dp"
            app:layout_constraintStart_toEndOf="@id/statusIndicator"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/locationLayout"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView> 