<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/dashboardFragment">

    <!-- Dashboard Fragment -->
    <fragment
        android:id="@+id/dashboardFragment"
        android:name="com.example.ma.presentation.main.DashboardFragment"
        android:label="@string/nav_dashboard"
        tools:layout="@layout/fragment_dashboard" />

    <!-- Transactions Fragment -->
    <fragment
        android:id="@+id/transactionsFragment"
        android:name="com.example.ma.presentation.transactions.TransactionsFragment"
        android:label="@string/nav_transactions"
        tools:layout="@layout/fragment_transactions" />

    <!-- Profile Fragment -->
    <fragment
        android:id="@+id/profileFragment"
        android:name="com.example.ma.presentation.profile.ProfileFragment"
        android:label="@string/nav_profile"
        tools:layout="@layout/fragment_profile" />

    <!-- Notifications Fragment -->
    <fragment
        android:id="@+id/notificationsFragment"
        android:name="com.example.ma.presentation.notifications.NotificationsFragment"
        android:label="@string/nav_notifications"
        tools:layout="@layout/fragment_notifications" />

    <!-- Reports Fragment -->
    <fragment
        android:id="@+id/reportsFragment"
        android:name="com.example.ma.presentation.reports.ReportsFragment"
        android:label="@string/nav_reports"
        tools:layout="@layout/fragment_reports" />

</navigation> 