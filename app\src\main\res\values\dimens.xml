<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>

    <!-- Text sizes -->
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_medium">16sp</dimen>
    <dimen name="text_size_large">20sp</dimen>
    <dimen name="text_size_extra_large">24sp</dimen>

    <!-- Spacing -->
    <dimen name="spacing_small">8dp</dimen>
    <dimen name="spacing_medium">16dp</dimen>
    <dimen name="spacing_large">24dp</dimen>
    <dimen name="spacing_extra_large">32dp</dimen>

    <!-- Button dimensions -->
    <dimen name="button_height">48dp</dimen>
    <dimen name="button_corner_radius">8dp</dimen>

    <!-- Card dimensions -->
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">4dp</dimen>

    <!-- Navigation drawer -->
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="nav_header_vertical_spacing">8dp</dimen>

    <!-- Dashboard card dimensions -->
    <dimen name="dashboard_card_height">120dp</dimen>
    <dimen name="dashboard_icon_size">48dp</dimen>
</resources>