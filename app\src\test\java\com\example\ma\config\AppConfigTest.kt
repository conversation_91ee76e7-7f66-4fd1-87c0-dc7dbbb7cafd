package com.example.ma.config

import org.junit.Test
import org.junit.Assert.*

/**
 * تست‌های واحد برای AppConfig
 */
class AppConfigTest {

    @Test
    fun `validateConfig should return true for valid configuration`() {
        // Given - تنظیمات معتبر
        // When
        val result = AppConfig.validateConfig()
        
        // Then
        assertTrue("تنظیمات باید معتبر باشد", result)
    }

    @Test
    fun `SUPABASE_URL should not be empty`() {
        // Given & When
        val url = AppConfig.SUPABASE_URL
        
        // Then
        assertNotNull("URL نباید null باشد", url)
        assertTrue("URL نباید خالی باشد", url.isNotEmpty())
        assertTrue("URL باید با https شروع شود", url.startsWith("https://"))
    }

    @Test
    fun `SUPABASE_ANON_KEY should not be empty`() {
        // Given & When
        val key = AppConfig.SUPABASE_ANON_KEY
        
        // Then
        assertNotNull("کلید نباید null باشد", key)
        assertTrue("کلید نباید خالی باشد", key.isNotEmpty())
        assertTrue("کلید باید حداقل 20 کاراکتر باشد", key.length > 20)
    }

    @Test
    fun `APP_VERSION_NAME should not be empty`() {
        // Given & When
        val version = AppConfig.APP_VERSION_NAME
        
        // Then
        assertNotNull("نسخه نباید null باشد", version)
        assertTrue("نسخه نباید خالی باشد", version.isNotEmpty())
    }

    @Test
    fun `APP_VERSION_CODE should be positive`() {
        // Given & When
        val versionCode = AppConfig.APP_VERSION_CODE
        
        // Then
        assertTrue("کد نسخه باید مثبت باشد", versionCode > 0)
    }

    @Test
    fun `Network timeouts should be reasonable`() {
        // Given & When
        val connectTimeout = AppConfig.Network.CONNECT_TIMEOUT
        val readTimeout = AppConfig.Network.READ_TIMEOUT
        val writeTimeout = AppConfig.Network.WRITE_TIMEOUT
        val callTimeout = AppConfig.Network.CALL_TIMEOUT
        
        // Then
        assertTrue("Connect timeout باید مثبت باشد", connectTimeout > 0)
        assertTrue("Read timeout باید مثبت باشد", readTimeout > 0)
        assertTrue("Write timeout باید مثبت باشد", writeTimeout > 0)
        assertTrue("Call timeout باید مثبت باشد", callTimeout > 0)
        
        assertTrue("Connect timeout باید کمتر از 60 ثانیه باشد", connectTimeout <= 60)
        assertTrue("Read timeout باید کمتر از 60 ثانیه باشد", readTimeout <= 60)
    }

    @Test
    fun `Cache sizes should be reasonable`() {
        // Given & When
        val profileImageCacheSize = AppConfig.Cache.PROFILE_IMAGE_CACHE_SIZE
        val apiCacheSize = AppConfig.Cache.API_CACHE_SIZE
        
        // Then
        assertTrue("Profile image cache size باید مثبت باشد", profileImageCacheSize > 0)
        assertTrue("API cache size باید مثبت باشد", apiCacheSize > 0)
        
        // حداکثر 100MB برای profile images
        assertTrue("Profile image cache نباید بیش از 100MB باشد", 
            profileImageCacheSize <= 100 * 1024 * 1024)
        
        // حداکثر 50MB برای API cache
        assertTrue("API cache نباید بیش از 50MB باشد", 
            apiCacheSize <= 50 * 1024 * 1024)
    }

    @Test
    fun `File constraints should be reasonable`() {
        // Given & When
        val maxImageSize = AppConfig.File.MAX_IMAGE_SIZE_MB
        val imageQuality = AppConfig.File.IMAGE_QUALITY
        val maxWidth = AppConfig.File.IMAGE_MAX_WIDTH
        val maxHeight = AppConfig.File.IMAGE_MAX_HEIGHT
        
        // Then
        assertTrue("حداکثر اندازه تصویر باید مثبت باشد", maxImageSize > 0)
        assertTrue("کیفیت تصویر باید بین 1 تا 100 باشد", imageQuality in 1..100)
        assertTrue("حداکثر عرض تصویر باید مثبت باشد", maxWidth > 0)
        assertTrue("حداکثر ارتفاع تصویر باید مثبت باشد", maxHeight > 0)
        
        assertTrue("حداکثر اندازه تصویر نباید بیش از 10MB باشد", maxImageSize <= 10)
        assertTrue("حداکثر عرض تصویر نباید بیش از 2048 باشد", maxWidth <= 2048)
        assertTrue("حداکثر ارتفاع تصویر نباید بیش از 2048 باشد", maxHeight <= 2048)
    }

    @Test
    fun `Security settings should be reasonable`() {
        // Given & When
        val sessionTimeout = AppConfig.Security.SESSION_TIMEOUT_MINUTES
        val maxLoginAttempts = AppConfig.Security.MAX_LOGIN_ATTEMPTS
        val lockoutMinutes = AppConfig.Security.LOGIN_LOCKOUT_MINUTES
        val passwordMinLength = AppConfig.Security.PASSWORD_MIN_LENGTH
        val usernameMinLength = AppConfig.Security.USERNAME_MIN_LENGTH
        
        // Then
        assertTrue("Session timeout باید مثبت باشد", sessionTimeout > 0)
        assertTrue("حداکثر تلاش ورود باید مثبت باشد", maxLoginAttempts > 0)
        assertTrue("مدت قفل شدن باید مثبت باشد", lockoutMinutes > 0)
        assertTrue("حداقل طول رمز عبور باید مثبت باشد", passwordMinLength > 0)
        assertTrue("حداقل طول نام کاربری باید مثبت باشد", usernameMinLength > 0)
        
        assertTrue("Session timeout نباید بیش از 8 ساعت باشد", sessionTimeout <= 480)
        assertTrue("حداکثر تلاش ورود نباید بیش از 10 باشد", maxLoginAttempts <= 10)
        assertTrue("حداقل طول رمز عبور باید حداقل 6 باشد", passwordMinLength >= 6)
        assertTrue("حداقل طول نام کاربری باید حداقل 3 باشد", usernameMinLength >= 3)
    }

    @Test
    fun `Business settings should be valid`() {
        // Given & When
        val defaultBottlePrice = AppConfig.Business.DEFAULT_BOTTLE_PRICE
        val minTransactionAmount = AppConfig.Business.MIN_TRANSACTION_AMOUNT
        val maxTransactionAmount = AppConfig.Business.MAX_TRANSACTION_AMOUNT
        val currencySymbol = AppConfig.Business.CURRENCY_SYMBOL
        val currencyCode = AppConfig.Business.CURRENCY_CODE
        
        // Then
        assertTrue("قیمت پیش‌فرض بطری باید مثبت باشد", defaultBottlePrice > 0)
        assertTrue("حداقل مبلغ تراکنش باید مثبت باشد", minTransactionAmount > 0)
        assertTrue("حداکثر مبلغ تراکنش باید مثبت باشد", maxTransactionAmount > 0)
        assertTrue("حداکثر مبلغ باید بیشتر از حداقل باشد", maxTransactionAmount > minTransactionAmount)
        
        assertNotNull("نماد ارز نباید null باشد", currencySymbol)
        assertNotNull("کد ارز نباید null باشد", currencyCode)
        assertTrue("نماد ارز نباید خالی باشد", currencySymbol.isNotEmpty())
        assertTrue("کد ارز نباید خالی باشد", currencyCode.isNotEmpty())
        
        assertEquals("کد ارز باید IRR باشد", "IRR", currencyCode)
    }

    @Test
    fun `getConfigInfo should return valid map`() {
        // Given & When
        val configInfo = AppConfig.getConfigInfo()
        
        // Then
        assertNotNull("اطلاعات تنظیمات نباید null باشد", configInfo)
        assertTrue("اطلاعات تنظیمات نباید خالی باشد", configInfo.isNotEmpty())
        
        assertTrue("باید شامل app_version باشد", configInfo.containsKey("app_version"))
        assertTrue("باید شامل app_version_code باشد", configInfo.containsKey("app_version_code"))
        assertTrue("باید شامل debug_mode باشد", configInfo.containsKey("debug_mode"))
        assertTrue("باید شامل supabase_url باشد", configInfo.containsKey("supabase_url"))
        assertTrue("باید شامل config_valid باشد", configInfo.containsKey("config_valid"))
        
        assertEquals("config_valid باید true باشد", true, configInfo["config_valid"])
    }

    @Test
    fun `Realtime settings should be valid`() {
        // Given & When
        val heartbeatInterval = AppConfig.Realtime.HEARTBEAT_INTERVAL_MS
        val reconnectDelay = AppConfig.Realtime.RECONNECT_DELAY_MS
        val maxReconnectAttempts = AppConfig.Realtime.MAX_RECONNECT_ATTEMPTS
        val websocketUrl = AppConfig.Realtime.WEBSOCKET_URL
        
        // Then
        assertTrue("Heartbeat interval باید مثبت باشد", heartbeatInterval > 0)
        assertTrue("Reconnect delay باید مثبت باشد", reconnectDelay > 0)
        assertTrue("حداکثر تلاش reconnect باید مثبت باشد", maxReconnectAttempts > 0)
        
        assertNotNull("WebSocket URL نباید null باشد", websocketUrl)
        assertTrue("WebSocket URL نباید خالی باشد", websocketUrl.isNotEmpty())
        assertTrue("WebSocket URL باید با wss شروع شود", websocketUrl.startsWith("wss://"))
    }
}
