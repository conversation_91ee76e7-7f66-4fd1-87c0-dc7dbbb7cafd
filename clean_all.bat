@echo off
echo ========================================
echo    پاک کردن کامل Build Cache
echo ========================================

echo 1. بستن تمام process های Java...
taskkill /f /im java.exe >nul 2>&1
taskkill /f /im gradle.exe >nul 2>&1

echo 2. پاک کردن build directory...
if exist "app\build" rmdir /s /q "app\build"
if exist "build" rmdir /s /q "build"

echo 3. پاک کردن .gradle directory...
if exist ".gradle" rmdir /s /q ".gradle"

echo 4. پاک کردن global gradle cache...
if exist "%USERPROFILE%\.gradle\caches" rmdir /s /q "%USERPROFILE%\.gradle\caches"

echo 5. پاک کردن Android Studio cache...
if exist "%USERPROFILE%\.android\build-cache" rmdir /s /q "%USERPROFILE%\.android\build-cache"

echo 6. شروع Clean Build...
call gradlew clean

echo ========================================
echo    تمام cache ها پاک شدند!
echo    حالا gradlew assembleDebug رو اجرا کنید
echo ========================================
pause
