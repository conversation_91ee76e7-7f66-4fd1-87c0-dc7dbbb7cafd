# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true
# Gradle Configuration Cache (disabled for compatibility)
# org.gradle.configuration-cache=true
# Gradle Build Cache
org.gradle.caching=true
# Gradle Daemon
org.gradle.daemon=true
# Suppress SDK version warnings
android.suppressUnsupportedCompileSdk=34
# Enable incremental compilation
kotlin.incremental=true
# JVM Args (reverted to stable settings)
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
org.gradle.java.home=C:\\Program Files\\Android\\Android Studio\\jbr

# Network timeout settings for Iran
systemProp.org.gradle.internal.http.connectionTimeout=300000
systemProp.org.gradle.internal.http.socketTimeout=300000
# Kotlin daemon tuning and execution strategy
kotlin.compiler.execution.strategy=in-process
kotlin.daemon.useFallbackStrategy=false
# Ensure Kotlin daemon has enough memory when it does start
kotlin.daemon.jvmargs=-Xmx2048m -Xms512m -XX:MaxMetaspaceSize=768m -XX:ReservedCodeCacheSize=320m