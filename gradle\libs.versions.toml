[versions]
androidGradlePlugin = "8.5.2"
kotlin = "2.2.10"
ksp = "2.2.10-1.0.28"
hilt = "2.51.1"
coreKtx = "1.12.0"
appcompat = "1.6.1"
material = "1.11.0"
constraintlayout = "2.1.4"
navigation = "2.7.7"
lifecycle = "2.7.0"
coroutines = "1.8.0"
room = "2.6.1"
okhttp = "4.12.0"
retrofit = "2.11.0"
gson = "2.10.1"
glide = "4.16.0"
imageCropper = "0.7.0"
canhubCropper = "4.5.0"
circleimageview = "3.1.0"
supabase = "3.2.2"
junit = "4.13.2"
mockito = "5.11.0"
mockitoInline = "5.2.0"
coreTesting = "2.2.0"
coroutinesTest = "1.8.0"
junitExt = "1.1.5"
espressoCore = "3.5.1"
testRunner = "1.5.2"
testRules = "1.5.0"
leakcanary = "2.13"
ktor = "2.3.9"
kotlinxDatetime = "0.5.0"
desugarJdkLibs = "2.0.4"

[libraries]
# AndroidX
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
androidx-material = { group = "com.google.android.material", name = "material", version.ref = "material" }
androidx-constraintlayout = { group = "androidx.constraintlayout", name = "constraintlayout", version.ref = "constraintlayout" }
androidx-navigation-fragment-ktx = { group = "androidx.navigation", name = "navigation-fragment-ktx", version.ref = "navigation" }
androidx-navigation-ui-ktx = { group = "androidx.navigation", name = "navigation-ui-ktx", version.ref = "navigation" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycle" }
androidx-lifecycle-livedata-ktx = { group = "androidx.lifecycle", name = "lifecycle-livedata-ktx", version.ref = "lifecycle" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycle" }
androidx-room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
androidx-room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
androidx-room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }

# Coroutines
kotlinx-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "coroutines" }
kotlinx-coroutines-core = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-core", version.ref = "coroutines" }

# KotlinX DateTime
kotlinx-datetime = { group = "org.jetbrains.kotlinx", name = "kotlinx-datetime", version.ref = "kotlinxDatetime" }

# Hilt
google-hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
google-hilt-compiler = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "hilt" }

# Networking
squareup-okhttp = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp" }
squareup-okhttp-logging = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp" }
squareup-retrofit = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
squareup-retrofit-converter-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "retrofit" }
google-gson = { group = "com.google.code.gson", name = "gson", version.ref = "gson" }

# Image
github-glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glide" }
github-glide-compiler = { group = "com.github.bumptech.glide", name = "compiler", version.ref = "glide" }
vanniktech-imagecropper = { group = "com.vanniktech", name = "android-image-cropper", version.ref = "imageCropper" }
canhub-image-cropper = { group = "com.canhub", name = "android-image-cropper", version.ref = "canhubCropper" }
hdodenhof-circleimageview = { group = "de.hdodenhof", name = "circleimageview", version.ref = "circleimageview" }

# Supabase
supabase-bom = { group = "io.github.jan-tennert.supabase", name = "bom", version.ref = "supabase" }
supabase-postgrest = { group = "io.github.jan-tennert.supabase", name = "postgrest-kt", version.ref = "supabase" }
supabase-gotrue = { group = "io.github.jan-tennert.supabase", name = "auth-kt", version.ref = "supabase" }
supabase-realtime = { group = "io.github.jan-tennert.supabase", name = "realtime-kt", version.ref = "supabase" }
supabase-storage = { group = "io.github.jan-tennert.supabase", name = "storage-kt", version.ref = "supabase" }

# Ktor
ktor-client-core = { group = "io.ktor", name = "ktor-client-core", version.ref = "ktor" }
ktor-client-android = { group = "io.ktor", name = "ktor-client-android", version.ref = "ktor" }
ktor-client-content-negotiation = { group = "io.ktor", name = "ktor-client-content-negotiation", version.ref = "ktor" }
ktor-serialization-kotlinx-json = { group = "io.ktor", name = "ktor-serialization-kotlinx-json", version.ref = "ktor" }

# Testing
test-junit = { group = "junit", name = "junit", version.ref = "junit" }
test-mockito-core = { group = "org.mockito", name = "mockito-core", version.ref = "mockito" }
test-mockito-inline = { group = "org.mockito", name = "mockito-inline", version.ref = "mockitoInline" }
test-androidx-arch-core-testing = { group = "androidx.arch.core", name = "core-testing", version.ref = "coreTesting" }
test-kotlinx-coroutines-test = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-test", version.ref = "coroutinesTest" }
androidTest-androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitExt" }
androidTest-androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidTest-androidx-runner = { group = "androidx.test", name = "runner", version.ref = "testRunner" }
androidTest-androidx-rules = { group = "androidx.test", name = "rules", version.ref = "testRules" }

# Debug
debug-leakcanary = { group = "com.squareup.leakcanary", name = "leakcanary-android", version.ref = "leakcanary" }

# Desugar JDK
desugar-jdk = { group = "com.android.tools", name = "desugar_jdk_libs", version.ref = "desugarJdkLibs" }

[plugins]
android-application = { id = "com.android.application", version.ref = "androidGradlePlugin" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
kotlin-kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlin" }
hilt-android = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }
