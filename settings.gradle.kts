pluginManagement {
	repositories {
		google()
		mavenCentral()
		gradlePluginPortal()
	}

	resolutionStrategy {
		eachPlugin {
			when (requested.id.id) {
				"com.android.application" -> useModule("com.android.tools.build:gradle:${requested.version}")
				"org.jetbrains.kotlin.android" -> useModule("org.jetbrains.kotlin:kotlin-gradle-plugin:${requested.version}")
				"org.jetbrains.kotlin.plugin.serialization" -> useModule("org.jetbrains.kotlin:kotlin-serialization:${requested.version}")
			}
		}
	}
}


dependencyResolutionManagement {
	repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
	repositories {
		// Ensure Supabase artifacts are fetched from Maven Central
		exclusiveContent {
			forRepository {
				mavenCentral()
			}
			filter {
				includeGroup("io.github.jan-tennert.supabase")
			}
		}
		// Primary repositories
		google()
		mavenCentral()
		maven { url = uri("https://jitpack.io") }
	}
}

rootProject.name = "MA"
include(":app")