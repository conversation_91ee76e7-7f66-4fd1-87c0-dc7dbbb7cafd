# 📱 مشخصات تفصیلی صفحات و ویژگی‌ها

## 🏠 صفحه اصلی (MainActivity)

### **Layout و Components:**
```xml
- AppBarLayout با Toolbar سفارشی
- NavigationDrawer با header شخصی‌سازی شده
- BottomNavigationView با 4 تب اصلی
- FloatingActionButton برای تراکنش سریع
- CardViews برای نمایش آمار
- RecyclerView برای آخرین تراکنش‌ها
- SwipeRefreshLayout برای refresh
```

### **ویژگی‌های عملکردی:**
✅ **ثبت تراکنش جدید** با validation کامل
✅ **نمایش موجودی real-time** هر دو شریک
✅ **آمار فروش/هزینه/برداشت** با نمودار
✅ **آخرین تراکنش‌ها** با امکان فیلتر
✅ **دسترسی سریع** به عملیات مهم
✅ **نوتیفیکیشن badge** برای اعلانات جدید
✅ **عکس پروفایل** با قابلیت تغییر سریع

### **Business Logic:**
```kotlin
- محاسبه موجودی real-time
- validation مبلغ و توضیحات
- تشخیص نوع تراکنش (فروش/هزینه/برداشت)
- ارسال اعلان به شریک دیگر
- ذخیره local + sync با server
- error handling و retry mechanism
```

## 🔐 صفحه ورود (LoginActivity)

### **UI Components:**
```xml
- Logo انیمیشن‌دار برند
- TextInputLayouts با Material Design
- PasswordToggle برای نمایش/مخفی کردن رمز
- Login Button با loading state
- Biometric Login Option (اختیاری)
- Forgot Password Link
- Version Info در footer
```

### **Authentication Flow:**
```kotlin
1. Input Validation (username, password format)
2. Network Connectivity Check
3. API Call با JWT Response
4. Token Storage (Encrypted SharedPreferences)
5. User Profile Fetch
6. Navigation به MainActivity
7. Session Setup و Permissions
```

### **Security Features:**
✅ **Password Strength Validation**
✅ **Brute Force Protection** (rate limiting)
✅ **Secure Token Storage** (EncryptedSharedPreferences)
✅ **Biometric Authentication** (fingerprint/face)
✅ **Auto-logout** بعد از مدت زمان مشخص
✅ **Session Validation** در هر app launch

## 👤 صفحه پروفایل (ProfileActivity)

### **Sections:**
```xml
1. Header Section:
   - عکس پروفایل دایره‌ای (قابل تغییر)
   - نام و نام خانوادگی
   - نقش در شراکت

2. Personal Info Card:
   - شماره تلفن (editable)
   - ایمیل (editable)
   - تاریخ عضویت

3. Financial Summary Card:
   - موجودی فعلی
   - سهم از شراکت
   - کل فروش‌ها
   - کل هزینه‌ها

4. Settings Section:
   - تغییر تم (روشن/تاریک)
   - تنظیمات اعلانات
   - تغییر رمز عبور
   - خروج از حساب
```

### **Profile Image Management:**
```kotlin
- انتخاب از گالری با crop functionality
- عکس‌گیری مستقیم با دوربین
- فشرده‌سازی هوشمند (max 1MB)
- آپلود به Supabase Storage
- cache محلی برای سرعت
- fallback به avatar پیش‌فرض
```

## 🔔 صفحه اعلانات (NotificationActivity)

### **UI Structure:**
```xml
- Toolbar با search و filter options
- TabLayout برای دسته‌بندی:
  * همه اعلانات
  * دریافتی
  * ارسالی
  * خوانده نشده
- RecyclerView با custom adapter
- Empty State برای حالت خالی
- Pull-to-refresh functionality
```

### **Notification Types:**
```kotlin
1. Transaction Requests:
   - درخواست تایید تراکنش
   - اطلاع از تراکنش جدید
   - تایید/رد تراکنش

2. Profile Updates:
   - تغییر عکس پروفایل
   - بروزرسانی اطلاعات

3. System Messages:
   - پیام‌های سیستمی
   - یادآوری‌ها
   - اطلاعیه‌ها
```

### **Real-time Features:**
```kotlin
- WebSocket connection برای live updates
- Push notifications با FCM
- Badge count در bottom navigation
- Sound و vibration (قابل تنظیم)
- Mark as read/unread functionality
```

## 💰 صفحه گزارشات مالی (FinancialReportsActivity)

### **Report Types:**
```xml
1. Daily Reports:
   - فروش روزانه
   - هزینه‌های روزانه
   - سود خالص

2. Monthly Reports:
   - نمودار فروش ماهانه
   - مقایسه با ماه قبل
   - روند رشد

3. Partner Comparison:
   - عملکرد هر شریک
   - سهم از فروش
   - آمار تراکنش‌ها

4. Category Analysis:
   - تحلیل هزینه‌ها بر اساس دسته
   - پرفروش‌ترین محصولات
   - بهینه‌سازی هزینه‌ها
```

### **Chart Components:**
```kotlin
- LineChart برای روند فروش
- PieChart برای توزیع هزینه‌ها
- BarChart برای مقایسه ماهانه
- Interactive charts با zoom/pan
- Export به PDF/Excel
```

## ⚙️ صفحه تنظیمات (SettingsActivity)

### **Settings Categories:**
```xml
1. Appearance:
   - تم (روشن/تاریک/خودکار)
   - اندازه فونت
   - زبان (فارسی/انگلیسی)

2. Notifications:
   - اعلانات push
   - صدا و vibration
   - زمان‌بندی اعلانات

3. Security:
   - تغییر رمز عبور
   - احراز هویت بیومتریک
   - قفل خودکار

4. Data & Sync:
   - همگام‌سازی خودکار
   - backup و restore
   - پاک کردن cache

5. About:
   - نسخه برنامه
   - لیسانس‌ها
   - تماس با پشتیبانی
```

## 📊 صفحه آمار و نمودارها (StatisticsActivity)

### **Dashboard Widgets:**
```xml
- KPI Cards (فروش، هزینه، سود)
- Progress Bars برای اهداف ماهانه
- Trend Indicators (افزایش/کاهش)
- Quick Stats (تعداد تراکنش‌ها)
```

### **Interactive Charts:**
```kotlin
- Real-time data updates
- Date range picker
- Filter by transaction type
- Drill-down capability
- Export functionality
```
