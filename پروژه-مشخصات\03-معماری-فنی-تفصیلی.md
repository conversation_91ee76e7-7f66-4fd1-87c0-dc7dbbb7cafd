# 🏗️ معماری فنی تفصیلی

## 📦 ساختار پروژه (Package Structure)

```
com.example.ma/
├── 📱 presentation/
│   ├── ui/
│   │   ├── main/           # صفحه اصلی
│   │   ├── auth/           # احراز هویت
│   │   ├── profile/        # پروفایل کاربر
│   │   ├── notifications/  # اعلانات
│   │   ├── reports/        # گزارشات
│   │   └── settings/       # تنظیمات
│   ├── adapters/           # RecyclerView Adapters
│   ├── fragments/          # Fragment ها
│   └── viewmodels/         # ViewModels
├── 🧠 domain/
│   ├── entities/           # Business Entities
│   ├── usecases/           # Use Cases
│   ├── repositories/       # Repository Interfaces
│   └── validators/         # Business Validators
├── 🗄️ data/
│   ├── local/
│   │   ├── database/       # Room Database
│   │   ├── dao/            # Data Access Objects
│   │   └── entities/       # Database Entities
│   ├── remote/
│   │   ├── api/            # API Interfaces
│   │   ├── dto/            # Data Transfer Objects
│   │   └── services/       # Network Services
│   └── repositories/       # Repository Implementations
├── 🔧 utils/
│   ├── managers/           # System Managers
│   ├── extensions/         # Kotlin Extensions
│   ├── helpers/            # Helper Classes
│   └── constants/          # Constants
└── 🎯 di/                  # Dependency Injection
```

## 🏛️ Clean Architecture Layers

### **1. Presentation Layer**
```kotlin
// ViewModel Example
class MainViewModel @Inject constructor(
    private val createTransactionUseCase: CreateTransactionUseCase,
    private val getBalanceUseCase: GetBalanceUseCase
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()
    
    fun createTransaction(transaction: TransactionRequest) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            createTransactionUseCase(transaction)
                .onSuccess { result ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        message = "تراکنش با موفقیت ثبت شد"
                    )
                }
                .onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = error.message
                    )
                }
        }
    }
}
```

### **2. Domain Layer**
```kotlin
// Use Case Example
class CreateTransactionUseCase @Inject constructor(
    private val transactionRepository: TransactionRepository,
    private val notificationRepository: NotificationRepository,
    private val transactionValidator: TransactionValidator
) {
    suspend operator fun invoke(request: TransactionRequest): Result<Transaction> {
        return try {
            // Validation
            transactionValidator.validate(request)
                .onFailure { return Result.failure(it) }
            
            // Business Logic
            val transaction = Transaction(
                id = UUID.randomUUID().toString(),
                amount = request.amount,
                type = request.type,
                description = request.description,
                userId = request.userId,
                createdAt = System.currentTimeMillis(),
                status = TransactionStatus.PENDING
            )
            
            // Save Transaction
            val savedTransaction = transactionRepository.createTransaction(transaction)
            
            // Send Notification
            notificationRepository.sendTransactionNotification(savedTransaction)
            
            Result.success(savedTransaction)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
```

### **3. Data Layer**
```kotlin
// Repository Implementation
@Singleton
class TransactionRepositoryImpl @Inject constructor(
    private val localDataSource: TransactionLocalDataSource,
    private val remoteDataSource: TransactionRemoteDataSource,
    private val networkManager: NetworkManager
) : TransactionRepository {
    
    override suspend fun createTransaction(transaction: Transaction): Transaction {
        return if (networkManager.isConnected()) {
            // Server-first approach
            val remoteTransaction = remoteDataSource.createTransaction(transaction)
            localDataSource.insertTransaction(remoteTransaction)
            remoteTransaction
        } else {
            // Offline mode
            val localTransaction = transaction.copy(syncStatus = SyncStatus.PENDING)
            localDataSource.insertTransaction(localTransaction)
            localTransaction
        }
    }
    
    override fun getTransactions(userId: String): Flow<List<Transaction>> {
        return localDataSource.getTransactions(userId)
            .map { transactions ->
                if (networkManager.isConnected()) {
                    syncTransactions(userId)
                }
                transactions
            }
    }
}
```

## 🗄️ Database Schema (Room)

### **Entities:**
```kotlin
@Entity(tableName = "users")
data class UserEntity(
    @PrimaryKey val id: String,
    val username: String,
    val displayName: String,
    val email: String?,
    val phone: String?,
    val profileImageUrl: String?,
    val isActive: Boolean = true,
    val createdAt: Long,
    val updatedAt: Long,
    val syncStatus: SyncStatus = SyncStatus.SYNCED
)

@Entity(
    tableName = "transactions",
    foreignKeys = [
        ForeignKey(
            entity = UserEntity::class,
            parentColumns = ["id"],
            childColumns = ["userId"],
            onDelete = ForeignKey.CASCADE
        )
    ]
)
data class TransactionEntity(
    @PrimaryKey val id: String,
    val amount: Double,
    val type: TransactionType,
    val description: String,
    val userId: String,
    val approvedBy: String? = null,
    val status: TransactionStatus = TransactionStatus.PENDING,
    val createdAt: Long,
    val updatedAt: Long,
    val syncStatus: SyncStatus = SyncStatus.SYNCED
)

@Entity(tableName = "notifications")
data class NotificationEntity(
    @PrimaryKey val id: String,
    val fromUserId: String,
    val toUserId: String,
    val type: NotificationType,
    val title: String,
    val message: String,
    val data: String? = null, // JSON data
    val isRead: Boolean = false,
    val createdAt: Long,
    val syncStatus: SyncStatus = SyncStatus.SYNCED
)
```

### **DAOs:**
```kotlin
@Dao
interface TransactionDao {
    @Query("SELECT * FROM transactions WHERE userId = :userId ORDER BY createdAt DESC")
    fun getTransactionsByUser(userId: String): Flow<List<TransactionEntity>>
    
    @Query("SELECT * FROM transactions WHERE syncStatus != 'SYNCED'")
    suspend fun getUnsyncedTransactions(): List<TransactionEntity>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTransaction(transaction: TransactionEntity): Long
    
    @Update
    suspend fun updateTransaction(transaction: TransactionEntity)
    
    @Query("UPDATE transactions SET status = :status WHERE id = :id")
    suspend fun updateTransactionStatus(id: String, status: TransactionStatus)
}
```

## 🌐 Network Layer

### **API Service:**
```kotlin
interface TransactionApiService {
    @POST("transactions")
    suspend fun createTransaction(
        @Body transaction: TransactionDto
    ): Response<TransactionDto>
    
    @GET("transactions")
    suspend fun getTransactions(
        @Query("user_id") userId: String,
        @Query("limit") limit: Int = 50,
        @Query("offset") offset: Int = 0
    ): Response<List<TransactionDto>>
    
    @PUT("transactions/{id}/approve")
    suspend fun approveTransaction(
        @Path("id") transactionId: String,
        @Body approval: TransactionApprovalDto
    ): Response<TransactionDto>
}
```

### **WebSocket for Real-time:**
```kotlin
@Singleton
class RealtimeManager @Inject constructor(
    private val webSocketClient: WebSocketClient,
    private val notificationHandler: NotificationHandler
) {
    fun subscribeToNotifications(userId: String) {
        webSocketClient.connect("notifications:user_id=eq.$userId") { message ->
            when (message.eventType) {
                "INSERT" -> notificationHandler.handleNewNotification(message.new)
                "UPDATE" -> notificationHandler.handleUpdatedNotification(message.new)
                "DELETE" -> notificationHandler.handleDeletedNotification(message.old)
            }
        }
    }
}
```

## 🔧 Dependency Injection (Hilt)

### **Modules:**
```kotlin
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return Room.databaseBuilder(
            context,
            AppDatabase::class.java,
            "ma_database"
        )
        .addMigrations(MIGRATION_1_2, MIGRATION_2_3)
        .build()
    }
    
    @Provides
    fun provideTransactionDao(database: AppDatabase): TransactionDao {
        return database.transactionDao()
    }
}

@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    @Provides
    @Singleton
    fun provideOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(AuthInterceptor())
            .addInterceptor(LoggingInterceptor())
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build()
    }
    
    @Provides
    @Singleton
    fun provideRetrofit(okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BuildConfig.API_BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
}
```
