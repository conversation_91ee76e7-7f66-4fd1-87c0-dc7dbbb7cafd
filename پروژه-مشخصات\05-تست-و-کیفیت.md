# 🧪 تست و کنترل کیفیت

## 🔬 Testing Strategy

### **Unit Tests:**
```kotlin
// ViewModel Test Example
@ExtendWith(MockitoExtension::class)
class MainViewModelTest {
    
    @Mock
    private lateinit var createTransactionUseCase: CreateTransactionUseCase
    
    @Mock
    private lateinit var getBalanceUseCase: GetBalanceUseCase
    
    private lateinit var viewModel: MainViewModel
    
    @Before
    fun setup() {
        viewModel = MainViewModel(createTransactionUseCase, getBalanceUseCase)
    }
    
    @Test
    fun `createTransaction should update UI state correctly on success`() = runTest {
        // Given
        val transaction = TransactionRequest(
            amount = 1000.0,
            type = "SALE",
            description = "فروش آب"
        )
        val expectedResult = Transaction(
            id = "123",
            amount = 1000.0,
            type = TransactionType.SALE,
            description = "فروش آب"
        )
        
        whenever(createTransactionUseCase(transaction)).thenReturn(Result.success(expectedResult))
        
        // When
        viewModel.createTransaction(transaction)
        
        // Then
        val uiState = viewModel.uiState.value
        assertThat(uiState.isLoading).isFalse()
        assertThat(uiState.message).isEqualTo("تراکنش با موفقیت ثبت شد")
        assertThat(uiState.error).isNull()
    }
    
    @Test
    fun `createTransaction should handle validation errors`() = runTest {
        // Given
        val invalidTransaction = TransactionRequest(
            amount = -100.0,
            type = "SALE",
            description = ""
        )
        
        whenever(createTransactionUseCase(invalidTransaction))
            .thenReturn(Result.failure(ValidationException("مبلغ نامعتبر")))
        
        // When
        viewModel.createTransaction(invalidTransaction)
        
        // Then
        val uiState = viewModel.uiState.value
        assertThat(uiState.isLoading).isFalse()
        assertThat(uiState.error).isEqualTo("مبلغ نامعتبر")
    }
}
```

### **Repository Tests:**
```kotlin
@ExtendWith(MockitoExtension::class)
class TransactionRepositoryTest {
    
    @Mock
    private lateinit var localDataSource: TransactionLocalDataSource
    
    @Mock
    private lateinit var remoteDataSource: TransactionRemoteDataSource
    
    @Mock
    private lateinit var networkManager: NetworkManager
    
    private lateinit var repository: TransactionRepositoryImpl
    
    @Before
    fun setup() {
        repository = TransactionRepositoryImpl(localDataSource, remoteDataSource, networkManager)
    }
    
    @Test
    fun `createTransaction should use remote when connected`() = runTest {
        // Given
        val transaction = createSampleTransaction()
        whenever(networkManager.isConnected()).thenReturn(true)
        whenever(remoteDataSource.createTransaction(transaction)).thenReturn(transaction)
        
        // When
        val result = repository.createTransaction(transaction)
        
        // Then
        verify(remoteDataSource).createTransaction(transaction)
        verify(localDataSource).insertTransaction(transaction)
        assertThat(result).isEqualTo(transaction)
    }
    
    @Test
    fun `createTransaction should use local when offline`() = runTest {
        // Given
        val transaction = createSampleTransaction()
        whenever(networkManager.isConnected()).thenReturn(false)
        
        // When
        val result = repository.createTransaction(transaction)
        
        // Then
        verify(localDataSource).insertTransaction(any())
        verifyNoInteractions(remoteDataSource)
        assertThat(result.syncStatus).isEqualTo(SyncStatus.PENDING)
    }
}
```

### **UI Tests (Espresso):**
```kotlin
@RunWith(AndroidJUnit4::class)
@LargeTest
class MainActivityTest {
    
    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)
    
    @get:Rule
    val hiltRule = HiltAndroidRule(this)
    
    @Before
    fun setup() {
        hiltRule.inject()
    }
    
    @Test
    fun createTransaction_shouldShowSuccessMessage() {
        // Given - User is on main screen
        onView(withId(R.id.fab_add_transaction)).check(matches(isDisplayed()))
        
        // When - User creates a transaction
        onView(withId(R.id.fab_add_transaction)).perform(click())
        onView(withId(R.id.et_amount)).perform(typeText("1000"))
        onView(withId(R.id.et_description)).perform(typeText("فروش آب"))
        onView(withId(R.id.btn_save)).perform(click())
        
        // Then - Success message should be shown
        onView(withText("تراکنش با موفقیت ثبت شد"))
            .check(matches(isDisplayed()))
    }
    
    @Test
    fun createTransaction_withInvalidAmount_shouldShowError() {
        // When - User enters invalid amount
        onView(withId(R.id.fab_add_transaction)).perform(click())
        onView(withId(R.id.et_amount)).perform(typeText("-100"))
        onView(withId(R.id.btn_save)).perform(click())
        
        // Then - Error should be shown
        onView(withText("مبلغ باید بیشتر از صفر باشد"))
            .check(matches(isDisplayed()))
    }
}
```

## 🔍 Code Quality Tools

### **Static Analysis (Detekt):**
```yaml
# detekt.yml
complexity:
  active: true
  CyclomaticComplexMethod:
    active: true
    threshold: 15
  LongMethod:
    active: true
    threshold: 60
  LongParameterList:
    active: true
    functionThreshold: 6

style:
  active: true
  MagicNumber:
    active: true
    ignoreNumbers: ['-1', '0', '1', '2']
  MaxLineLength:
    active: true
    maxLineLength: 120

naming:
  active: true
  ClassNaming:
    active: true
    classPattern: '[A-Z][a-zA-Z0-9]*'
  FunctionNaming:
    active: true
    functionPattern: '[a-z][a-zA-Z0-9]*'
```

### **Code Coverage (JaCoCo):**
```gradle
android {
    buildTypes {
        debug {
            testCoverageEnabled true
        }
    }
}

tasks.register('jacocoTestReport', JacocoReport) {
    dependsOn 'testDebugUnitTest'
    
    reports {
        xml.required = true
        html.required = true
    }
    
    def fileFilter = [
        '**/R.class',
        '**/R$*.class',
        '**/BuildConfig.*',
        '**/Manifest*.*',
        '**/*Test*.*',
        'android/**/*.*',
        '**/*_Hilt*.*'
    ]
    
    def debugTree = fileTree(dir: "$project.buildDir/intermediates/javac/debug", excludes: fileFilter)
    def mainSrc = "$project.projectDir/src/main/java"
    
    sourceDirectories.setFrom(files([mainSrc]))
    classDirectories.setFrom(files([debugTree]))
    executionData.setFrom(fileTree(dir: project.buildDir, includes: [
        'jacoco/testDebugUnitTest.exec',
        'outputs/code_coverage/debugAndroidTest/connected/**/*.ec'
    ]))
}
```

## 🚀 CI/CD Pipeline

### **GitHub Actions:**
```yaml
name: Android CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
    
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
    
    - name: Run unit tests
      run: ./gradlew testDebugUnitTest
    
    - name: Run static analysis
      run: ./gradlew detekt
    
    - name: Generate test report
      run: ./gradlew jacocoTestReport
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./build/reports/jacoco/jacocoTestReport/jacocoTestReport.xml
    
    - name: Build APK
      run: ./gradlew assembleDebug
    
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: app-debug
        path: app/build/outputs/apk/debug/app-debug.apk

  ui-test:
    runs-on: macos-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'
    
    - name: Run UI tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: 29
        script: ./gradlew connectedDebugAndroidTest
```

## 📊 Quality Metrics

### **Code Quality Standards:**
```kotlin
// Example of well-documented code
/**
 * Creates a new transaction and handles the complete workflow including
 * validation, persistence, and notification.
 *
 * @param request The transaction request containing amount, type, and description
 * @return Result containing the created transaction or an error
 * @throws ValidationException if the request data is invalid
 * @throws NetworkException if the operation fails due to network issues
 */
suspend fun createTransaction(request: TransactionRequest): Result<Transaction> {
    // Implementation with proper error handling
}
```

### **Performance Benchmarks:**
```kotlin
@RunWith(AndroidJUnit4::class)
class DatabaseBenchmarkTest {
    
    @get:Rule
    val benchmarkRule = BenchmarkRule()
    
    @Test
    fun insertTransaction_performance() {
        benchmarkRule.measureRepeated {
            // Measure transaction insertion time
            val transaction = createSampleTransaction()
            runWithTimingDisabled {
                // Setup code
            }
            
            // Measured operation
            transactionDao.insertTransaction(transaction)
        }
    }
}
```

### **Memory Leak Detection:**
```kotlin
class MemoryLeakTest {
    
    @Test
    fun mainActivity_shouldNotLeakMemory() {
        val scenario = ActivityScenario.launch(MainActivity::class.java)
        
        scenario.moveToState(Lifecycle.State.DESTROYED)
        
        // Force garbage collection
        System.gc()
        System.runFinalization()
        System.gc()
        
        // Check for leaks using LeakCanary or similar
        assertThat(getRetainedInstances()).isEmpty()
    }
}
