# 📋 دستورالعمل پیاده‌سازی برای برنامه‌نویس

## 🎯 مراحل توسعه (Development Phases)

### **Phase 1: Project Setup (هفته 1)**
```bash
# 1. Create new Android project
- Minimum SDK: API 24 (Android 7.0)
- Target SDK: API 34 (Android 14)
- Language: Kotlin
- Build system: Gradle with Kotlin DSL

# 2. Setup dependencies
implementation "androidx.core:core-ktx:1.12.0"
implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.7.0"
implementation "androidx.activity:activity-compose:1.8.2"
implementation "androidx.compose.ui:ui:1.5.8"
implementation "androidx.compose.material3:material3:1.1.2"

# Hilt for DI
implementation "com.google.dagger:hilt-android:2.48"
kapt "com.google.dagger:hilt-compiler:2.48"

# Room for local database
implementation "androidx.room:room-runtime:2.6.1"
implementation "androidx.room:room-ktx:2.6.1"
kapt "androidx.room:room-compiler:2.6.1"

# Network
implementation "com.squareup.retrofit2:retrofit:2.9.0"
implementation "com.squareup.retrofit2:converter-gson:2.9.0"
implementation "com.squareup.okhttp3:logging-interceptor:4.12.0"

# Image loading
implementation "com.github.bumptech.glide:glide:4.16.0"

# 3. Setup project structure
mkdir -p app/src/main/java/com/example/ma/{presentation,domain,data,utils,di}
```

### **Phase 2: Core Architecture (هفته 2)**
```kotlin
// 1. Setup Hilt Application
@HiltAndroidApp
class MAApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        // Initialize logging, crash reporting, etc.
    }
}

// 2. Create base classes
abstract class BaseActivity<VB : ViewBinding, VM : ViewModel> : AppCompatActivity() {
    protected abstract val binding: VB
    protected abstract val viewModel: VM
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setupObservers()
        setupClickListeners()
    }
    
    protected abstract fun setupObservers()
    protected abstract fun setupClickListeners()
}

// 3. Setup database entities and DAOs
@Entity(tableName = "users")
data class UserEntity(...)

@Dao
interface UserDao {
    @Query("SELECT * FROM users WHERE username = :username")
    suspend fun getUserByUsername(username: String): UserEntity?
}
```

### **Phase 3: Authentication System (هفته 3)**
```kotlin
// 1. Create LoginActivity
class LoginActivity : BaseActivity<ActivityLoginBinding, AuthViewModel>() {
    
    override val binding by lazy { ActivityLoginBinding.inflate(layoutInflater) }
    override val viewModel: AuthViewModel by viewModels()
    
    override fun setupObservers() {
        viewModel.authState.observe(this) { state ->
            when (state) {
                is AuthState.Loading -> showLoading()
                is AuthState.Success -> navigateToMain()
                is AuthState.Error -> showError(state.message)
            }
        }
    }
    
    override fun setupClickListeners() {
        binding.btnLogin.setOnClickListener {
            val username = binding.etUsername.text.toString()
            val password = binding.etPassword.text.toString()
            viewModel.login(username, password)
        }
    }
}

// 2. Implement AuthViewModel
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {
    
    private val _authState = MutableLiveData<AuthState>()
    val authState: LiveData<AuthState> = _authState
    
    fun login(username: String, password: String) {
        viewModelScope.launch {
            _authState.value = AuthState.Loading
            
            authRepository.login(username, password)
                .onSuccess { result ->
                    _authState.value = AuthState.Success(result)
                }
                .onFailure { error ->
                    _authState.value = AuthState.Error(error.message ?: "خطا در ورود")
                }
        }
    }
}
```

### **Phase 4: Main UI Implementation (هفته 4-5)**
```kotlin
// 1. MainActivity with Navigation
class MainActivity : BaseActivity<ActivityMainBinding, MainViewModel>() {
    
    override val binding by lazy { ActivityMainBinding.inflate(layoutInflater) }
    override val viewModel: MainViewModel by viewModels()
    
    private lateinit var navController: NavController
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupNavigation()
        setupBottomNavigation()
        setupDrawer()
    }
    
    private fun setupNavigation() {
        val navHostFragment = supportFragmentManager
            .findFragmentById(R.id.nav_host_fragment) as NavHostFragment
        navController = navHostFragment.navController
    }
    
    private fun setupBottomNavigation() {
        binding.bottomNavigation.setupWithNavController(navController)
    }
}

// 2. Transaction creation dialog
class CreateTransactionDialog : DialogFragment() {
    
    private var _binding: DialogCreateTransactionBinding? = null
    private val binding get() = _binding!!
    
    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogCreateTransactionBinding.inflate(layoutInflater)
        
        return AlertDialog.Builder(requireContext())
            .setView(binding.root)
            .create()
    }
    
    private fun setupValidation() {
        binding.etAmount.addTextChangedListener { text ->
            val amount = text.toString().toDoubleOrNull()
            binding.btnSave.isEnabled = amount != null && amount > 0
        }
    }
}
```

### **Phase 5: Data Layer Implementation (هفته 6)**
```kotlin
// 1. Repository implementation
@Singleton
class TransactionRepositoryImpl @Inject constructor(
    private val localDataSource: TransactionDao,
    private val remoteDataSource: TransactionApiService,
    private val networkManager: NetworkManager
) : TransactionRepository {
    
    override suspend fun createTransaction(transaction: Transaction): Result<Transaction> {
        return try {
            if (networkManager.isConnected()) {
                // Server-first approach
                val remoteTransaction = remoteDataSource.createTransaction(transaction.toDto())
                val localTransaction = remoteTransaction.toEntity()
                localDataSource.insertTransaction(localTransaction)
                Result.success(localTransaction.toDomain())
            } else {
                // Offline mode
                val offlineTransaction = transaction.copy(
                    syncStatus = SyncStatus.PENDING
                )
                localDataSource.insertTransaction(offlineTransaction.toEntity())
                Result.success(offlineTransaction)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

// 2. API service
interface TransactionApiService {
    @POST("transactions")
    suspend fun createTransaction(@Body transaction: TransactionDto): TransactionDto
    
    @GET("transactions")
    suspend fun getTransactions(
        @Query("user_id") userId: String
    ): List<TransactionDto>
}
```

## 🔧 Implementation Guidelines

### **Code Style Standards:**
```kotlin
// 1. Naming conventions
class UserRepository          // PascalCase for classes
fun getUserById()            // camelCase for functions
val userName: String         // camelCase for properties
const val MAX_RETRY_COUNT    // UPPER_SNAKE_CASE for constants

// 2. Function documentation
/**
 * Creates a new transaction with validation and persistence.
 *
 * @param transaction The transaction to create
 * @return Result containing the created transaction or error
 */
suspend fun createTransaction(transaction: Transaction): Result<Transaction>

// 3. Error handling pattern
suspend fun performOperation(): Result<Data> {
    return try {
        val result = riskyOperation()
        Result.success(result)
    } catch (e: NetworkException) {
        Result.failure(NetworkError("اتصال اینترنت برقرار نیست"))
    } catch (e: ValidationException) {
        Result.failure(ValidationError(e.message))
    } catch (e: Exception) {
        Result.failure(UnknownError("خطای غیرمنتظره"))
    }
}
```

### **UI Implementation Standards:**
```xml
<!-- 1. Layout naming convention -->
activity_main.xml           <!-- Activities -->
fragment_profile.xml        <!-- Fragments -->
dialog_create_transaction.xml <!-- Dialogs -->
item_notification.xml       <!-- RecyclerView items -->

<!-- 2. ID naming convention -->
android:id="@+id/btn_save"           <!-- Buttons: btn_ -->
android:id="@+id/et_amount"          <!-- EditText: et_ -->
android:id="@+id/tv_title"           <!-- TextView: tv_ -->
android:id="@+id/rv_transactions"    <!-- RecyclerView: rv_ -->

<!-- 3. String resources -->
<string name="btn_save">ذخیره</string>
<string name="error_invalid_amount">مبلغ نامعتبر است</string>
<string name="msg_transaction_created">تراکنش با موفقیت ثبت شد</string>
```

### **Testing Implementation:**
```kotlin
// 1. Unit test structure
class TransactionViewModelTest {
    
    @Mock
    private lateinit var repository: TransactionRepository
    
    private lateinit var viewModel: TransactionViewModel
    
    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        viewModel = TransactionViewModel(repository)
    }
    
    @Test
    fun `createTransaction should emit success state when repository succeeds`() = runTest {
        // Given
        val transaction = createSampleTransaction()
        whenever(repository.createTransaction(transaction))
            .thenReturn(Result.success(transaction))
        
        // When
        viewModel.createTransaction(transaction)
        
        // Then
        val state = viewModel.uiState.value
        assertThat(state.isLoading).isFalse()
        assertThat(state.message).isEqualTo("تراکنش با موفقیت ثبت شد")
    }
}

// 2. UI test structure
@RunWith(AndroidJUnit4::class)
class MainActivityTest {
    
    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)
    
    @Test
    fun createTransaction_shouldShowInList() {
        // Perform UI actions
        onView(withId(R.id.fab_add)).perform(click())
        onView(withId(R.id.et_amount)).perform(typeText("1000"))
        onView(withId(R.id.btn_save)).perform(click())
        
        // Verify results
        onView(withText("1000 تومان")).check(matches(isDisplayed()))
    }
}
```

## 📱 Deployment Checklist

### **Pre-release Checklist:**
```bash
# 1. Code quality checks
./gradlew detekt                    # Static analysis
./gradlew testDebugUnitTest        # Unit tests
./gradlew connectedDebugAndroidTest # UI tests
./gradlew jacocoTestReport         # Coverage report

# 2. Performance checks
- Memory usage < 150MB
- App launch time < 2 seconds
- Smooth 60fps animations
- Network calls optimized

# 3. Security checks
- No hardcoded secrets
- ProGuard/R8 enabled for release
- Certificate pinning implemented
- Input validation complete

# 4. Accessibility checks
- Content descriptions added
- Touch targets >= 48dp
- Color contrast ratios met
- Screen reader compatibility

# 5. Localization checks
- All strings externalized
- RTL layout support
- Persian number formatting
- Date/time localization
```

### **Release Build Configuration:**
```gradle
android {
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            buildConfigField "String", "API_BASE_URL", "\"https://api.production.com\""
            buildConfigField "boolean", "DEBUG_MODE", "false"
        }
    }
    
    signingConfigs {
        release {
            storeFile file("../keystore/release.keystore")
            storePassword System.getenv("KEYSTORE_PASSWORD")
            keyAlias System.getenv("KEY_ALIAS")
            keyPassword System.getenv("KEY_PASSWORD")
        }
    }
}
